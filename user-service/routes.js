const passport = require('passport');
const fs = require('fs');
const path = require('path');
const _ = require('underscore');
const pjson = require('./package.json');

const requireEndpoints = (dir, appConfig, passportConfig) => fs.readdirSync(path.join(__dirname, dir)).forEach((file) => {
  const fileArray = file.split('.');
  const fileName = _.head(fileArray);
  const fileExt = _.head(_.tail(fileArray));
  if (fileExt === 'js') {
    const fileUrl = path.join(__dirname, dir, fileName);
    require(fileUrl)(appConfig, passportConfig); // eslint-disable-line
  }
});

module.exports = function (app) {
  app.get('/', (req, res) => res.status(200).json({
    status: 200,
    message: 'EzyBackend User Service - OK',
    version: pjson.version
  }));

  // Health check endpoint for Docker and monitoring
  app.get('/health', (req, res) => {
    res.status(200).json({
      status: 'healthy',
      service: 'ezybackend-user-service',
      version: pjson.version,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      environment: process.env.NODE_ENV || 'development'
    });
  });

  requireEndpoints('workers');
  requireEndpoints('api', app, passport);
  requireEndpoints('webhooks', app);

  app.use((req, res) => {
    res.json({ status: 404, message: 'The endpoint was not found. Please check.' });
  });
};
