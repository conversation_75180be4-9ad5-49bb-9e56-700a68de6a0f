/*
#     EzyBackend - Core Engine that powers Backend as a Service
#     (c) 2014 HackerBay, Inc.
#     EzyBackend may be freely distributed under the Apache 2 License
#
#     This webpack file is used to package SDK. It's NOT used for the server.
#     Modernized for Webpack 5 and Node.js 20+
*/

const path = require('path');
const webpack = require('webpack');
const TerserPlugin = require('terser-webpack-plugin');

const createConfig = (isProduction = false) => ({
  mode: isProduction ? 'production' : 'development',
  entry: './data-service/sdk/src/entry.js',
  output: {
    path: path.resolve(__dirname, 'data-service/sdk/dist'),
    filename: isProduction ? 'ezybackend.min.js' : 'ezybackend.js',
    library: {
      name: 'EzyBackend',
      type: 'umd',
      umdNamedDefine: true,
    },
    globalObject: 'typeof self !== \'undefined\' ? self : this',
    clean: true,
  },
  externals: {
    'socket.io-client': {
      commonjs: 'socket.io-client',
      commonjs2: 'socket.io-client',
      amd: 'socket.io-client',
      root: 'io'
    },
    'axios': {
      commonjs: 'axios',
      commonjs2: 'axios',
      amd: 'axios',
      root: 'axios'
    },
  },
  module: {
    noParse: /node_modules\/localforage\/dist\/localforage\.js/,
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              ['@babel/preset-env', {
                targets: {
                  browsers: ['> 1%', 'last 2 versions', 'not dead'],
                  node: '20'
                },
                modules: false,
              }]
            ],
            compact: false,
          },
        },
      },
    ],
  },
  optimization: isProduction ? {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            warnings: false,
            drop_console: false,
          },
          mangle: false,
          format: {
            comments: false,
          },
        },
        extractComments: false,
      }),
    ],
  } : {},
  plugins: [
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify(isProduction ? 'production' : 'development'),
      'process.env.EZYBACKEND_VERSION': JSON.stringify(require('./data-service/package.json').version),
    }),
  ],
  resolve: {
    extensions: ['.js', '.json'],
  },
  devtool: isProduction ? 'source-map' : 'eval-source-map',
});

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production';
  return createConfig(isProduction);
};
