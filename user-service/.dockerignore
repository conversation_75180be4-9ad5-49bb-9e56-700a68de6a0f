# EzyBackend User Service Docker ignore file
# Optimized for production builds

# Version control
.git/
.gitignore

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage and test files
coverage/
.nyc_output/
test/
*.test.js
*.spec.js

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Environment files
.env
.env.test
.env.local
.env.production

# Logs
logs/
*.log

# Temporary files
*.tmp
tmp/
temp/

# Development tools
.husky/
.prettierrc
.eslintrc.js
.mocharc.json

# Documentation
README.md
CHANGELOG.md
docs/

# Docker files
Dockerfile*
.dockerignore

# Credentials and secrets
credentials.tar.enc
*.pem
*.key
*.crt

# Uploads directory (runtime created)
uploads/

# Legacy files
Gruntfile.js
