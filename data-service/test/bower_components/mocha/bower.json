{"name": "mocha", "version": "2.2.0", "homepage": "http://mocha.github.io/mocha", "description": "simple, flexible, fun test framework", "authors": ["<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "repository": {"type": "git", "url": "git://github.com/mochajs/mocha.git"}, "main": ["mocha.js", "mocha.css"], "ignore": ["bin", "editors", "images", "lib", "support", "test", ".giti<PERSON>re", ".n<PERSON><PERSON><PERSON>", ".travis.yml", "component.json", "index.js", "<PERSON><PERSON><PERSON>", "package.json"], "keywords": ["mocha", "test", "bdd", "tdd", "tap"], "license": "MIT"}