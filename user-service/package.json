{"name": "ezybackend-user-service", "description": "EzyBackend User Service - Authentication and user management API with Passport.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "version": "2.0.0", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/Ji<PERSON>erkumar2030/ezybackend.git"}, "scripts": {"start": "node server.js", "dev": "NODE_ENV=development node server.js", "test": "npm run lint && npm run test-unit", "test-unit": "NODE_ENV=test mocha --reporter spec test/**/*.test.js --exit", "test-watch": "npm run test-unit -- --watch", "lint": "eslint . --ext .js --fix", "lint-check": "eslint . --ext .js", "format": "prettier --write \"**/*.{js,json,md}\"", "format-check": "prettier --check \"**/*.{js,json,md}\"", "prepare": "husky install || true"}, "dependencies": {"agenda": "^5.0.0", "axios": "^1.10.0", "basic-auth": "^2.0.1", "body-parser": "^2.2.0", "busboy": "^1.6.0", "connect": "^3.7.0", "connect-flash": "^0.1.1", "connect-mongo": "^5.1.0", "connect-redis": "^9.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cron": "^4.3.1", "ejs": "^3.1.10", "express": "^5.1.0", "express-session": "^1.18.1", "express-winston": "^4.2.0", "gridfs-stream": "^1.1.1", "html-pdf": "^3.0.1", "ioredis": "^5.6.1", "jsdom": "^26.1.0", "json2xls": "^0.1.2", "json2xlsx": "^0.1.6", "mailgun.js": "^10.2.3", "mandrill-api": "^1.0.45", "moment": "^2.30.1", "mongodb": "^6.17.0", "mongoose": "^8.9.3", "nodemailer": "^7.0.5", "nodemailer-mailgun-transport": "^2.1.5", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "passport-linkedin-oauth2": "^2.0.0", "passport-local": "^1.0.0", "passport-twitter": "^1.0.4", "q": "^1.5.1", "sendgrid": "^5.2.3", "should": "^13.2.3", "slack-notify": "^0.1.7", "socket.io": "^4.8.1", "stripe": "^17.5.0", "underscore": "^1.13.7", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-loggly-transport": "^1.0.3", "xmlhttprequest": "^1.8.0"}, "devDependencies": {"chai": "^5.2.0", "chai-http": "^5.1.1", "eslint": "^9.30.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.32.0", "husky": "^9.1.7", "mocha": "^11.7.1", "prettier": "^3.4.2", "supertest": "^7.0.0"}}