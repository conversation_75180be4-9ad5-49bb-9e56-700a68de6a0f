<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css" integrity="sha384-1q8mTJOASx8j1Au+a5WDVnPi2lkFfwwEAa8hDDdjZlpLegxhjVME1fgjWPGmkzs7" crossorigin="anonymous">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.5.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
    <link rel="stylesheet" href="assets/css/login.css">   

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.0/jquery.min.js"></script>
    <script src="https://ezybackend.io/js-sdk/ezybackend.js"></script>

</head>
<body>

<div class="wrapper">
    <div class="container">
    <div class="row">
    <div class="col-sm-12 col-md-12 col-lg-12">

    <div class="flex-general-column-wrapper-center" style="height: 99vh;position: relative;">

        <!--Login or Sing Up form-->
        <div id="authenticate-card" class="" style=" width:340px;">
            <div class="" style="height:auto;border-radius:2px; border:1px solid #BAB8B8;">
                <!--Header-->
                <div style="width:100%;height:117px;background-color:#dddddd;padding:15px;">                 

                    <div style="width:100%;" class="flex-general-column-wrapper-center">
                        <% if (generalSettings && generalSettings.appIcon) { %>
                            <div style="width:50px;height:50px;border-radius: 10px;overflow: hidden;">
                                <img src="<%= generalSettings.appIcon %>" style="width:50px;height:50px;">
                            </div>
                        <%}else{%>
                            <div style="width:50px;height:50px;border-radius: 10px;overflow: hidden;">
                                <img src="assets/images/CbLogoIcon.png" style="width:50px;height:50px;">
                            </div>
                        <%}%>    
                    </div>

                    <div style="width:100%;margin-top: -5px;" class="flex-general-column-wrapper-center">
                        <% if (generalSettings && generalSettings.appName) { %>
                            <h3><%= generalSettings.appName %></h3>
                        <%}else{%>
                            <h3>App on EzyBackend</h3>
                        <%}%>    
                    </div>

                </div>
                <!--Header-->

                <!--Main Body Spinner-->
                <div id="authenticate-spinner" style="width: 100%;height: 360px;background-color: white;" class="flex-general-column-wrapper-center">
                    <div style="width: 60px;height: 60px;" class="flex-general-column-wrapper-center">
                        <div style="" class="loader-circle advanced flex-general-column-wrapper-center">                        
                        </div>                        
                    </div>
                </div>
                <!--Main Body Spinner-->

                <!--Main Body Content-->
                <div id="authenticate-mainbody" style="">
                    <!--Error Block-->
                    <div id="authenticate-error" style="background-color: #f04848;padding: 10px;" class="solo-horizontal-center">
                        <span style="color:white;">Unknown Error</span>
                    </div>
                    <!--Error Block-->

                    <!--Body-->
                    <div style="background-color: white;padding-top: 5px;">
                        <!--Login and Sing Up buttons-->
                        <% if(!authSettings || (authSettings && authSettings.custom && authSettings.custom.enabled)){ %>
                        <div class="flex-general-column-wrapper-center" style="margin-top: 20px;">
                            <div class="flex-general-row-wrapper">
                               <div class="width:100%;height:30px;">
                                    <button class="default-inputfield login-btn" style="">LOG IN</button>
                               </div>
                               <div class="width:100%;height:30px;">
                                    <button class="default-inputfield sinup-btn" style="">SIGN UP</button>
                               </div>
                               
                            </div>
                        </div>
                        <%}%>
                        <div style="background-color: white;padding-bottom: 24px;padding-left: 20px;padding-right: 20px;">                           

                            <!--Social Connection Buttons-->
                            <div class="flex-general-column-wrapper-center" style="margin-top: 5px;width: 100%;">
                                <div class="solo-horizontal-center" style="width: 98%;">
                                    <!--facebook-->
                                    <% if(authSettings && authSettings.facebook && authSettings.facebook.enabled){ %>
                                    <div id="facebook-login-btn" class="social-btns facebook flex-general-column-wrapper-center">
                                        <i class="fa fa-facebook"></i>
                                    </div>
                                    <%}%>

                                    <!--twitter-->
                                    <% if(authSettings && authSettings.twitter && authSettings.twitter.enabled){ %>
                                    <div  id="twittr-login-btn" class="social-btns twitter flex-general-column-wrapper-center">
                                        <i class="fa fa-twitter"></i>
                                    </div>
                                    <%}%>

                                    <!--google-->
                                    <% if(authSettings && authSettings.google && authSettings.google.enabled){ %>
                                    <div  id="google-login-btn" class="social-btns google flex-general-column-wrapper-center">
                                        <i class="fa fa-google"></i>
                                    </div>
                                    <%}%>

                                    <!--linkedin-->
                                    <% if(authSettings && authSettings.linkedIn && authSettings.linkedIn.enabled){ %>
                                    <div  id="linkedin-login-btn" class="social-btns linkedin flex-general-column-wrapper-center">
                                        <i class="fa fa-linkedin" aria-hidden="true"></i>
                                    </div>
                                    <%}%>

                                    <!--github-->
                                    <% if(authSettings && authSettings.github && authSettings.github.enabled){ %>
                                    <div  id="github-login-btn"  class="social-btns github flex-general-column-wrapper-center">
                                        <i class="fa fa-github"></i>
                                    </div>
                                    <%}%>
                                 
                                </div>
                            </div>
                            
                            <!---OR-->
                            <div style="width: 100%;margin-top: 20px;" class="solo-horizontal-center">
                                <% if((authSettings && authSettings.custom && authSettings.custom.enabled) && ((authSettings && authSettings.facebook && authSettings.facebook.enabled) || (authSettings && authSettings.twitter && authSettings.twitter.enabled) || (authSettings && authSettings.google && authSettings.google.enabled) || (authSettings && authSettings.linkedIn && authSettings.linkedIn.enabled) || (authSettings && authSettings.github && authSettings.github.enabled))){ %>
                                <p id="authenticate-info" style="color:gray">Or</p>
                                <%}%>
                            </div>                            


                            <% if(!authSettings || (authSettings && authSettings.custom && authSettings.custom.enabled)){ %>
                            <!--Login Input Fields-->                        
                            <div id="login-inputfields" style="width: 100%;margin-top: 8px;" class="solo-horizontal-center">                                        
                                <div style="width: 95%;">
                                    <div id="login-username-wrap" class="flex-general-row-wrapper input-field" style="">
                                        <div class="flex-general-column-wrapper-center" style="width:13%; height: 35px;background-color:#f1f1f1;">
                                            <i class="icon ion-ios-person" style="color:gray;font-size: 22px;"></i>
                                        </div>
                                        <div style="width:86.98%; height: 35px;">
                                            <input id="login-username" type="text" placeholder="Username" class="default-inputfield" required style="width: 100%;height: 100%;padding-left: 12px;">
                                        </div>
                                    </div>

                                    <div id="login-password-wrap" class="flex-general-row-wrapper input-field" style="">
                                        <div class="flex-general-column-wrapper-center" style="width:13%; height: 35px;background-color:#f1f1f1;">
                                            <i class="icon ion-ios-locked-outline" style="color:gray;font-size: 22px;"></i>
                                        </div>
                                        <div style="width:86.98%; height: 35px;">
                                            <input id="login-password" type="password" placeholder="Password" class="default-inputfield" required style="width: 100%;height: 100%;padding-left: 12px;">
                                        </div>
                                    </div>
                                </div>                                        
                            </div>
                            <!--/Login Input Fields-->
                           

                            <!--Signup Input Fields-->
                            
                            <div id="signup-inputfields" style="width: 100%;margin-top: 8px;" class="solo-horizontal-center">                                        
                                <div style="width: 95%;">
                                    <div id="signup-username-wrap" class="flex-general-row-wrapper input-field" style="">
                                        <div class="flex-general-column-wrapper-center" style="width:13%; height: 35px;background-color:#f1f1f1;">
                                            <i class="icon ion-ios-person" style="color:gray;font-size: 22px;"></i>
                                        </div>
                                        <div style="width:86.98%; height: 35px;">
                                            <input id="signup-username" type="text" placeholder="Enter Username" class="default-inputfield" required style="width: 100%;height: 100%;padding-left: 12px;">
                                        </div>
                                    </div>                                

                                    <div id="signup-password-wrap"  class="flex-general-row-wrapper input-field" style="">
                                        <div class="flex-general-column-wrapper-center" style="width:13%; height: 35px;background-color:#f1f1f1;">
                                            <i class="icon ion-ios-locked-outline" style="color:gray;font-size: 22px;"></i>
                                        </div>
                                        <div style="width:86.98%; height: 35px;">
                                            <input id="signup-password" type="password" placeholder="Create a Password" class="default-inputfield" required style="width: 100%;height: 100%;padding-left: 12px;">
                                        </div>
                                    </div>

                                    <div id="signup-email-wrap" class="flex-general-row-wrapper input-field" style="">
                                        <div class="flex-general-column-wrapper-center" style="width:13%; height: 35px;background-color:#f1f1f1;">
                                            <i class="icon ion-ios-email-outline" style="color:gray;font-size: 22px;"></i>
                                        </div>
                                        <div style="width:86.98%; height: 35px;">
                                            <input id="signup-email" type="text" placeholder="Enter Email" class="default-inputfield" required style="width: 100%;height: 100%;padding-left: 12px;">
                                        </div>
                                    </div>
                                </div>                                        
                            </div>

                            <!--/Signup Input Fields-->

                            <!--Forgot Password-->
                            <div id="forget-password-block" style="width: 100%;margin-top: 15px;" class="solo-horizontal-center">
                                <span id="forget-password" style="color:gray;cursor: pointer;">Don't remember your password?</span>
                            </div>
                            <%}%>

                        </div>    
                    </div>
                    <!--Body-->

                    <% if(!authSettings || (authSettings && authSettings.custom && authSettings.custom.enabled)){ %>
                    <!--Button-->
                    <div class="final-btn" style="">
                       <div style="width: 100%;height: 30px;" class="flex-general-column-wrapper-center">
                            <span style="color:white;font-size: 34px;">
                                <i class="icon ion-log-in"></i>
                            </span>
                       </div>
                    </div>
                    <!--Button-->
                    <%}%>

                </div>
                <!--/Main Body Content-->
            </div>
        </div>
        <!--/Login or Sing Up form-->



        <!--Forgot Password-->
        <div id="forgetpassword-card" class="" style="width:340px;">
            <div class="" style="height:auto;border-radius:2px; border:1px solid #BAB8B8;">
                <!--Header-->
                <div style="width:100%;height:117px;background-color:#dddddd;padding:15px;">

                    <div id="goback-btn" style="width: 30px;height: 30px;position: absolute;">
                        <div id="goback-to-authenticate-card" class="flex-general-column-wrapper-center" style="width:100%;height:100%; border-radius: 25px;background-color: white;cursor: pointer;">
                            <i class="icon ion-ios-arrow-left" style="color:black;font-size:20px;"></i>
                            
                        </div>
                    </div>

                    <div style="width:100%;" class="flex-general-column-wrapper-center">
                        <% if (generalSettings && generalSettings.appIcon) { %>
                            <div style="width:50px;height:50px;border-radius: 10px;overflow: hidden;">
                                <img src="<%= generalSettings.appIcon %>" style="width:50px;height:50px;">
                            </div>
                        <%}else{%>
                            <div style="width:50px;height:50px;border-radius: 10px;overflow: hidden;">
                                <img src="assets/images/CbLogoIcon.png" style="width:50px;height:50px;">
                            </div>
                        <%}%>    
                    </div>

                    <div style="width:100%;margin-top: -5px;" class="flex-general-column-wrapper-center">
                       <h3>Password Reset</h3>
                    </div>

                </div>
                <!--Header-->

                <!--Main Body Spinner-->
                <div id="forgetpassword-spinner" style="width: 100%;height: 360px;background-color: white;" class="flex-general-column-wrapper-center">
                    <div style="width: 60px;height: 60px;" class="flex-general-column-wrapper-center">
                        <div id="req-spinner" class="loader-circle advanced flex-general-column-wrapper-center">       
                        </div>
                        <div id="req-response" style="color:gray;" class="flex-general-column-wrapper-center">                   
                        </div>                       
                    </div>
                </div>
                <!--Main Body Spinner-->

                <!--Main Body Content-->
                <div id="forgetpassword-mainbody">

                    <!--Error Block-->
                    <div id="forgetpassword-error" style="background-color: #f04848;padding: 10px;" class="solo-horizontal-center">
                        <span style="color:white;">Unknown Error</span>
                    </div>
                    <!--Error Block-->

                    <!--Body-->
                    <div style="background-color: white;padding: 10px;">
                        
                        <!--Info-->
                        <div style="width: 100%;margin-top: 20px;" class="solo-horizontal-center">
                            <p style="color:gray">
                            Please enter your email address.
                            </p>
                            <p style="color:gray;margin-top: -7px;">
                             We will send you an email to reset your password.
                            </p>
                        </div>

                        <!--Input Fields-->
                        <div style="width: 100%;margin-top: 8px;" class="solo-horizontal-center">                                        
                            <div style="width: 95%;">
                                <div id="forgot-email-wrap" class="flex-general-row-wrapper input-field" style="">
                                    <div class="flex-general-column-wrapper-center" style="width:13%; height: 35px;background-color:#f1f1f1;">
                                        <i class="icon ion-ios-email-outline" style="color:gray;font-size: 22px;"></i>
                                    </div>
                                    <div style="width:86.98%; height: 35px;">
                                        <input id="forgot-email" type="text" placeholder="Enter Email" class="default-inputfield" required style="width: 100%;height: 100%;padding-left: 12px;">
                                    </div>
                                </div>                                           
                            </div>                                        
                        </div>                                    
                        
                    </div>
                    <!--Body-->

                    <!--Button-->
                    <div class="resetpwd-final-btn" style="margin-top: 2px;">
                       <div style="width: 100%;height: 30px;" class="flex-general-column-wrapper-center">
                            <span style="color:white;font-size: 34px;">
                                <i class="icon ion-log-in"></i>
                            </span>
                       </div>
                    </div>
                    <!--Button-->

                </div>
                <!--/Main Body Content-->

            </div>
            
        </div>
        <!--/Forgot Password-->

        <div class="flex-general-column-wrapper-center" style="width: 100%;height: 60px;margin-top: 1.5%;">
            <div class="flex-general-row-wrapper">
                <p style="color:#aeb3ba;">powered by</p>
               <p style="color:#aeb3ba;">&nbsp;<a style="color:#aeb3ba;" href="https://www.ezybackend.io">EzyBackend</a></p>
            </div>
        </div>
    </div>     

</div>
</div>
</div>

</body>
<script>  
    var serverURL="https://ezybackend.i/api/";  
    var __isDevelopment = false;

    if(window.location.host.indexOf('localhost') > -1){
        __isDevelopment = true;
        var serverURL="http://localhost:4730"; 
    }

    //This is the fucntion to get querystring, 
    function getParameterByName(name) {
        name = name.replace(/[\[]/, "\\[").replace(/[\]]/, "\\]");
        var regex = new RegExp("[\\?&]" + name + "=([^&#]*)"),
            results = regex.exec(location.search);
        return results === null ? "" : decodeURIComponent(results[1].replace(/\+/g, " "));
    }

    function _emailValidation(email){
        var filter = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;

        if (!filter.test(email)) {            
            return false;
        }

        return true;
    }

    function LightenDarkenColor(col, amt) {
      
        var usePound = false;
      
        if (col[0] == "#") {
            col = col.slice(1);
            usePound = true;
        }
     
        var num = parseInt(col,16);
     
        var r = (num >> 16) + amt;
     
        if (r > 255) r = 255;
        else if  (r < 0) r = 0;
     
        var b = ((num >> 8) & 0x00FF) + amt;
     
        if (b > 255) b = 255;
        else if  (b < 0) b = 0;
     
        var g = (num & 0x0000FF) + amt;
     
        if (g > 255) g = 255;
        else if (g < 0) g = 0;
     
        return (usePound?"#":"") + (g | (b << 8) | (r << 16)).toString(16);
      
    }

    //Remove #_=_' from URL
    if (window.location.hash == '#_=_'){

        // Check if the browser supports history.replaceState.
        if (history.replaceState) {

            // Keep the exact URL up to the hash.
            var cleanHref = window.location.href.split('#')[0];

            // Replace the URL in the address bar without messing with the back button.
            history.replaceState(null, null, cleanHref);

        } else {

            // Well, you're on an old browser, we can get rid of the _=_ but not the #.
            window.location.hash = '';

        }

    }

    var isLoginFields=true;

    // Binding the server variables
    var _appKeys =<%-JSON.stringify(appKeys)%>;       
    var _generalSettings =<%-JSON.stringify(generalSettings)%>; 
    var _authSettings =<%-JSON.stringify(authSettings)%>; 

    $(document).ready(function(){
        //Init EzyBackend
        CB.CloudApp.init(serverURL,_appKeys.appId, _appKeys.masterKey);    

        $("#forgetpassword-card").hide();
        $("#forgetpassword-error").hide();
        $("#forgetpassword-spinner").hide();

        $("#authenticate-error").hide();
        $("#authenticate-spinner").hide();

        $(".login-btn").addClass("btn-active");
        $("#signup-inputfields").hide();
        isLoginFields=true; 

        $(".final-btn").css({"background-color": _authSettings.general.primaryColor}); 
        $(".resetpwd-final-btn").css({"background-color": _authSettings.general.primaryColor});

        //If user logged already, redirect to callback url
        if(CB.CloudUser.current && _authSettings.general.callbackURL){
            window.location.href=_authSettings.general.callbackURL+"?cbtoken="+CB._getSessionId();
        }       

    });

    $(".final-btn").hover(function(){
        $(this).css("background-color", LightenDarkenColor(_authSettings.general.primaryColor,-40));
    }, function(){
        $(this).css("background-color", _authSettings.general.primaryColor);
    });

    $(".resetpwd-final-btn").hover(function(){
        $(this).css("background-color", LightenDarkenColor(_authSettings.general.primaryColor,-40));
    }, function(){
        $(this).css("background-color", _authSettings.general.primaryColor);
    });
  

    $("#forget-password").click(function(){ 
        $("#authenticate-card").hide();
        $("#forgetpassword-card").show();
        $("#req-response").text(""); 
        $("#req-spinner").show();         
    });

    $("#goback-to-authenticate-card").click(function(){ 
        $("#authenticate-card").show();
        $("#forgetpassword-card").hide();          
    });

    $(".sinup-btn").click(function(){ 
        $("#login-username-wrap").removeClass("input-field-error");
        $("#login-password-wrap").removeClass("input-field-error");
        $("#signup-username-wrap").removeClass("input-field-error");       
        $("#signup-password-wrap").removeClass("input-field-error");
        $("#signup-email-wrap").removeClass("input-field-error");

        $(".login-btn").removeClass("btn-active");
        $(".sinup-btn").addClass("btn-active"); 

        $("#signup-inputfields").show();
        $("#login-inputfields").hide();

        $("#authenticate-info").text("Or Enter your username, password and email");
        $("#forget-password-block").hide(); 

        isLoginFields=false;
    });

    $(".login-btn").click(function(){
        $("#login-username-wrap").removeClass("input-field-error");
        $("#login-password-wrap").removeClass("input-field-error");        
        $("#signup-username-wrap").removeClass("input-field-error"); 
        $("#signup-password-wrap").removeClass("input-field-error");
        $("#signup-email-wrap").removeClass("input-field-error");

        $(".sinup-btn").removeClass("btn-active");
        $(".login-btn").addClass("btn-active"); 

        $("#signup-inputfields").hide();
        $("#login-inputfields").show();

        $("#authenticate-info").text("Or");
        $("#forget-password-block").show(); 

        isLoginFields=true;        
    });

    $(".final-btn").click(function(){ 
        
        $("#authenticate-error span").text("");
        $("#authenticate-error").hide();       

        checkCallBackUrl();//Check Callbacke Url is set

        //Login
        if(isLoginFields && _authSettings && _authSettings.general.callbackURL){
            $("#login-username-wrap").removeClass("input-field-error");
            $("#login-password-wrap").removeClass("input-field-error");

            var loginUsername=$("#login-username").val();
            var loginPassword=$("#login-password").val();
            var validationsPassed=true;

            if(!loginUsername){
                $("#login-username-wrap").addClass("input-field-error");
                validationsPassed=false;                
            }            

            if(!loginPassword){
                $("#login-password-wrap").addClass("input-field-error");
                validationsPassed=false;                
            }

            if(validationsPassed){
                //Submit form

                $("#authenticate-mainbody").hide();
                $("#authenticate-spinner").show();

                var user = new CB.CloudUser();
                user.set('username', loginUsername);
                user.set('password', loginPassword);
                user.logIn({
                  success: function(user) {
                    window.location.href=_authSettings.general.callbackURL+"?cbtoken="+CB._getSessionId();
                  },
                  error: function(error) {
                    try{
                        error=JSON.parse(error);
                    }catch(e){}

                    if(error && error.error){
                        throwAuthError(error.error);
                    }else{  
                        throwAuthError("Check username and password.");                   
                    }
                  }
                });
            }
        }

        //Sign Up
        if(!isLoginFields && _authSettings && _authSettings.general.callbackURL){
            $("#signup-email-wrap").removeClass("input-field-error");
            $("#signup-password-wrap").removeClass("input-field-error");

            var signupUserName=$("#signup-username").val();
            var signupPassword=$("#signup-password").val();
            var signupEmail=$("#signup-email").val();
            var validationsPassed=true;

            if(!signupUserName){
                $("#signup-username-wrap").addClass("input-field-error");
                validationsPassed=false;                
            }

            if(!signupPassword){
                $("#signup-password-wrap").addClass("input-field-error");
                validationsPassed=false;                
            }

            if(!signupEmail){
                $("#signup-email-wrap").addClass("input-field-error");
                validationsPassed=false;                
            }

            if(signupEmail && !_emailValidation(signupEmail)){
                $("#signup-email-wrap").addClass("input-field-error"); 
                validationsPassed=false;               
            }           

            if(validationsPassed){
                //Submit form
                $("#authenticate-mainbody").hide();
                $("#authenticate-spinner").show();

                var user = new CB.CloudUser();
                user.set('username',signupUserName);
                user.set('password', signupPassword);
                user.set('email', signupEmail);
                user.signUp({
                  success: function(user) {
                    window.location.href=_authSettings.general.callbackURL+"?cbtoken="+CB._getSessionId();
                  },
                  error: function(error) {                   
                    try{
                        error=JSON.parse(error);
                    }catch(e){}

                    if(error && error.error){
                        throwAuthError(error.error);
                    }else{  
                        throwAuthError("Something went wrong..");                   
                    }
                  }
                });                
            }
        }
    });


    $(".resetpwd-final-btn").click(function(){ 
        
        $("#forgetpassword-error span").text("");
        $("#forgetpassword-error").hide();
        $("#req-response").text("");
        $("#req-spinner").show();
        $("#goback-btn").show();
        

        $("#forgot-email-wrap").removeClass("input-field-error");        

        var email=$("#forgot-email").val();        
        var validationsPassed=true;

        if(!email){
            $("#forgot-email-wrap").addClass("input-field-error");
            validationsPassed=false;                
        } 

        if(email && !_emailValidation(email)){
            $("#forgot-email-wrap").addClass("input-field-error"); 
            validationsPassed=false;               
        }        

        if(validationsPassed){
            //Submit form

            $("#forgetpassword-mainbody").hide();
            $("#forgetpassword-spinner").show();
             $("#goback-btn").hide();

            CB.CloudUser.resetPassword(email,{
              success: function(response) {
                $("#req-spinner").hide();
                $("#req-response").text("Success");
                $("#goback-btn").show();                
              },
              error: function(error) {
                $("#goback-btn").show();
                try{
                    error=JSON.parse(error);
                }catch(e){}

                if(error && error.error){
                    throwForgotError(error.error);
                }else if(error && error.message){
                    throwForgotError(error.message);
                }else{  
                    throwForgotError("Something went wrong..");                   
                }
              }
            });
        }
    });

    function throwAuthError(error){
        $("#authenticate-spinner").hide();
        $("#authenticate-mainbody").show();

        $("#authenticate-error").show();
        $("#authenticate-error span").text(error);
    }

    function throwForgotError(error){
        $("#forgetpassword-spinner").hide();
        $("#forgetpassword-mainbody").show();

        $("#forgetpassword-error").show();
        $("#forgetpassword-error span").text(error);
    }      

</script>

<script type="text/javascript">
    $("#facebook-login-btn").click(function(){
        if(checkCallBackUrl()){
            var url=serverURL+"/auth/"+_appKeys.appId+"/facebook";
            requestLoginUrl(url);
        }
                
    });

    $("#twittr-login-btn").click(function(){
        if(checkCallBackUrl()){
            var url=serverURL+"/auth/"+_appKeys.appId+"/twitter";
            requestLoginUrl(url);    
        }    
    });

    $("#github-login-btn").click(function(){
        if(checkCallBackUrl()){
            var url=serverURL+"/auth/"+_appKeys.appId+"/github";
            requestLoginUrl(url); 
        }     
    });

    $("#google-login-btn").click(function(){
        if(checkCallBackUrl()){
            var url=serverURL+"/auth/"+_appKeys.appId+"/google";
            requestLoginUrl(url);  
        }     
    });

    $("#linkedin-login-btn").click(function(){
        if(checkCallBackUrl()){
            var url=serverURL+"/auth/"+_appKeys.appId+"/linkedin";
            requestLoginUrl(url);  
        }     
    });    

    function requestLoginUrl(url){
        $.get(url, function(data, status){            
            if(data && data.url){
                var loginUrl=data.url;
                window.open(loginUrl,"_self");
            }           
       });
    }

    function checkCallBackUrl() {
        if(!_authSettings || !_authSettings.general.callbackURL){
            throwAuthError("Please add your app callback URL in your EzyBackend App Settings.");
            return false;
        }
        return true;
    }
</script>

</html>
