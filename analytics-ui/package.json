{"name": "ezybackend-analytics-ui", "version": "1.0.0", "main": "server.js", "scripts": {"dev": "webpack --watch --progress", "start": "node server.js", "test": "mocha --compilers js:babel-register --require ./test/helpers.js --recursive", "test:watch": "mocha -w --compilers js:babel-register --require ./test/helpers.js --recursive"}, "license": "ISC", "dependencies": {"axios": "^0.15.3", "babel-core": "^6.10.4", "babel-eslint": "^7.2.0", "babel-loader": "^6.2.4", "babel-plugin-react-html-attrs": "^2.0.0", "babel-plugin-transform-class-properties": "^6.19.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-polyfill": "^6.9.1", "babel-preset-es2015": "^6.9.0", "babel-preset-react": "^6.11.1", "babel-preset-stage-0": "^6.16.0", "babel-register": "^6.9.0", "card-validator": "^4.1.0", "chart.js": "^2.5.0", "ezybackend": "^2.0.262", "cross-env": "^1.0.8", "css-loader": "^0.23.1", "ejs": "^2.5.5", "eslint": "^3.18.0", "eslint-loader": "^1.6.3", "expect": "^1.20.1", "express": "^4.14.0", "fs": "0.0.1-security", "group-by-time": "^1.0.0", "material-ui": "^0.18.7", "moment": "^2.18.1", "node-libs-browser": "^1.0.0", "node-sass": "^3.8.0", "path": "^0.12.7", "react": "^15.1.0", "react-addons-test-utils": "^15.1.0", "react-bootstrap": "^0.30.7", "react-bootstrap-table": "^3.1.1", "react-chartjs": "^0.8.0", "react-date-range": "^0.9.3", "react-dom": "^15.1.0", "react-redux": "^4.4.5", "react-router": "^3.0.0", "react-select": "^1.0.0-rc.5", "react-tap-event-plugin": "^2.0.1", "react-tooltip": "^3.2.2", "redux": "^3.5.2", "redux-logger": "^2.6.1", "redux-promise": "^0.5.3", "redux-thunk": "^2.1.0", "sass-loader": "^4.0.0", "style-loader": "^0.13.1", "underscore": "^1.8.3", "url-loader": "^0.5.8", "webpack": "^1.14.0"}, "devDependencies": {"babel-plugin-react-transform": "^2.0.2", "chai": "^4.0.2", "enzyme": "^2.8.2", "jsdom": "^9.12.0", "mocha": "^3.4.2", "nock": "^9.0.13", "redux-mock-store": "^1.2.3", "sinon": "^2.3.2", "webpack": "^1.14.0"}, "description": "analytics-ui\r Analytics Portal for EzyBackend", "repository": {"type": "git", "url": "git+https://github.com/ritishgumber/analytics-ui.git"}, "author": "Ritish Gumber <<EMAIL>>", "bugs": {"url": "https://github.com/ritishgumber/analytics-ui/issues"}, "homepage": "https://github.com/ritishgumber/analytics-ui#readme"}