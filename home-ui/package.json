{"name": "ezybackend-home-ui", "version": "2.0.0", "description": "EzyBackend Home UI - Marketing and landing page", "main": "server.js", "license": "Apache-2.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/Ji<PERSON>erkumar2030/ezybackend.git"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "scripts": {"start": "node server.js", "dev": "NODE_ENV=development node server.js", "build": "npm run build-css && npm run build-js && npm run optimize-images", "build-css": "sass src/scss:public/css --style compressed", "build-js": "webpack --mode production", "build-dev": "webpack --mode development", "watch": "webpack --mode development --watch", "watch-css": "sass src/scss:public/css --watch", "test": "jest", "test:lighthouse": "lighthouse http://localhost:1444 --output json --output-path ./lighthouse-report.json", "lint": "eslint . --ext .js --fix", "lint-check": "eslint . --ext .js", "format": "prettier --write \"**/*.{js,json,md,css,scss,html}\"", "format-check": "prettier --check \"**/*.{js,json,md,css,scss,html}\"", "prepare": "husky install || true"}, "dependencies": {"body-parser": "^2.2.0", "compression": "^1.7.4", "ejs": "^3.1.10", "express": "^5.1.0", "express-minify": "^1.0.0", "express-minify-html": "^0.12.0", "prerender-node": "^3.3.0", "q": "^1.5.1", "axios": "^1.10.0", "response-time": "^2.3.2", "twitter-api-v2": "^1.18.2"}, "devDependencies": {"@babel/core": "^7.25.0", "@babel/preset-env": "^7.25.0", "babel-loader": "^9.2.1", "css-loader": "^7.1.2", "eslint": "^9.30.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.32.0", "html-webpack-plugin": "^5.6.3", "husky": "^9.1.7", "imagemin": "^8.0.1", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^10.0.0", "jest": "^29.7.0", "lighthouse": "^12.2.1", "prettier": "^3.4.2", "sass": "^1.83.0", "style-loader": "^4.0.0", "webpack": "^5.99.9", "webpack-cli": "^6.0.1"}}