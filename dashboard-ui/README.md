#EzyBackend

[![Build Status](https://travis-ci.org/EzyBackend/dashboard.svg?branch=master)](https://travis-ci.org/EzyBackend/dashboard)

EzyBackend is the Complete NoSQL Database Service for your app. **Think of EzyBackend as Parse + Firebase + Algolia + Iron.io all combined into one** :
 - Data-Storage / JSON Storage / BLOB Storage
 - 100% data ownership
 - Realtime
 - Search
 - Cache
 - Queues
 - More - ACL's, User Authentication, Server-less apps and more.


#EzyBackend Dashboard

This is a EzyBackend Dashboard and can be used with [EzyBackend Engine](https://www.github.com/EzyBackend/ezybackend).

#Running EzyBackend

You can run <PERSON>zyBackend with Docker (recommended). Check out the docker project [here](https://github.com/ezybackend/docker)

#Contributing

Pull requests are very welcome!

We'd love to hear your feedback and suggestions in the issue tracker.


#LICENSE

Copyright 2016 HackerBay, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
