#
# EzyBackend Data Service Dockerfile
# Multi-stage build for Node.js 20+ with optimized production image
#
# Based on CloudBoost, rebranded as EzyBackend
# Licensed under Apache 2.0
#

# Build stage
FROM node:20-alpine AS builder

LABEL maintainer="Ji<PERSON><PERSON> <<EMAIL>>"
LABEL description="EzyBackend Data Service - Backend as a Service Platform"
LABEL version="2.0.0"

# Install build dependencies
RUN apk add --no-cache \
    make \
    gcc \
    g++ \
    python3 \
    py3-pip \
    git

WORKDIR /usr/src/app

# Copy package files
COPY package*.json ./

# Install all dependencies (including devDependencies for building)
RUN npm ci --include=dev

# Copy source code
COPY . .

# Build SDK if needed
RUN npm run build-sdk || echo "SDK build skipped"

# Remove dev dependencies and clean up
RUN npm prune --production && \
    npm cache clean --force

# Production stage
FROM node:20-alpine AS production

# Install runtime dependencies only
RUN apk add --no-cache \
    dumb-init \
    curl

# Create non-root user for security
RUN addgroup -g 1001 -S ezybackend && \
    adduser -S -D -H -u 1001 -s /sbin/nologin -G ezybackend ezybackend

WORKDIR /usr/src/app

# Copy built application from builder stage
COPY --from=builder --chown=ezybackend:ezybackend /usr/src/app .

# Create necessary directories
RUN mkdir -p logs config && \
    chown -R ezybackend:ezybackend /usr/src/app

# Switch to non-root user
USER ezybackend

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:4730/health || exit 1

# Expose ports
#   - 4730: EzyBackend HTTP REST API
EXPOSE 4730

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["npm", "start"]
