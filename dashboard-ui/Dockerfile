#
# EzyBackend Dashboard UI Dockerfile
# Multi-stage build for Node.js 20+ with optimized production image
#
# Based on CloudBoost, rebranded as EzyBackend
# Licensed under Apache 2.0
#

# Build stage
FROM node:20-alpine AS builder

LABEL maintainer="Ji<PERSON><PERSON> <<EMAIL>>"
LABEL description="EzyBackend Dashboard UI - React-based Admin Interface"
LABEL version="2.0.0"

# Install build dependencies
RUN apk add --no-cache \
    make \
    gcc \
    g++ \
    python3 \
    py3-pip \
    git

WORKDIR /usr/src/app

# Copy package files
COPY package*.json ./

# Install all dependencies (including devDependencies for building)
RUN npm ci --include=dev

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine AS production

# Install runtime dependencies
RUN apk add --no-cache \
    curl \
    nodejs \
    npm

# Copy built application from builder stage
COPY --from=builder /usr/src/app/public /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy server.js for API proxy if needed
COPY --from=builder /usr/src/app/server.js /usr/src/app/
COPY --from=builder /usr/src/app/package.json /usr/src/app/

# Install only production dependencies for server
WORKDIR /usr/src/app
RUN npm ci --only=production

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:1440/health || exit 1

# Expose ports
#   - 1440: EzyBackend Dashboard UI
EXPOSE 1440

# Start nginx and node server
CMD ["sh", "-c", "nginx -g 'daemon off;' & node server.js"]
