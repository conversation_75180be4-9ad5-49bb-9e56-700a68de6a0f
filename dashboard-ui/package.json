{"name": "ezybackend-dashboard-ui", "version": "2.0.0", "description": "EzyBackend Dashboard UI - Modern React-based admin interface", "main": "index.html", "license": "Apache-2.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/Ji<PERSON>erkumar2030/ezybackend.git"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "scripts": {"start": "node server.js", "dev": "NODE_ENV=development webpack serve --mode development", "build": "NODE_ENV=production webpack --mode production", "build-dev": "NODE_ENV=development webpack --mode development", "watch": "NODE_ENV=development webpack --mode development --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js,.jsx --fix", "lint-check": "eslint . --ext .js,.jsx", "format": "prettier --write \"**/*.{js,jsx,json,md,css,scss}\"", "format-check": "prettier --check \"**/*.{js,jsx,json,md,css,scss}\"", "prepare": "husky install || true"}, "devDependencies": {"@babel/core": "^7.25.0", "@babel/preset-env": "^7.25.0", "@babel/preset-react": "^7.25.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "babel-loader": "^9.2.1", "css-loader": "^7.1.2", "eslint": "^9.30.1", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "html-webpack-plugin": "^5.6.3", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.4.2", "sass": "^1.83.0", "sass-loader": "^16.0.3", "style-loader": "^4.0.0", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}, "dependencies": {"@mui/material": "^6.1.9", "@mui/icons-material": "^6.1.9", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "axios": "^1.10.0", "card-validator": "^10.0.0", "chart.js": "^4.4.7", "ejs": "^3.1.10", "express": "^5.1.0", "file-saver": "^2.0.5", "moment": "^2.30.1", "prop-types": "^15.8.1", "query-string": "^9.1.1", "react": "^18.3.1", "react-ace": "^12.0.0", "react-bootstrap": "^2.10.7", "react-color": "^2.19.3", "react-copy-to-clipboard": "^5.1.0", "react-custom-scrollbars-2": "^4.5.0", "react-datetime": "^3.2.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-redux": "^9.1.2", "react-router": "^6.28.0", "react-router-dom": "^6.28.0", "react-tooltip": "^5.28.0", "redux": "^5.0.1", "redux-logger": "^3.0.6", "redux-thunk": "^3.1.0", "underscore": "^1.13.7"}}