{"name": "ezybackend-data-browser", "version": "0.0.0", "description": "", "main": "server.js", "scripts": {"start": "node server.js", "build": "webpack --config webpack_config/webpack.prod.js", "client": "webpack --config webpack_config/webpack.dev.js --watch --progress"}, "author": "", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"axios": "^0.14.0", "babel-core": "^6.17.0", "babel-loader": "^6.2.5", "babel-plugin-transform-class-properties": "^6.16.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-preset-es2015": "^6.16.0", "ezybackend": "^1.1.483", "ejs": "^2.6.1", "express": "^4.14.0", "halogen": "^0.2.0", "history": "^4.3.0", "material-design-lite": "^1.2.1", "material-ui": "^0.16.0", "mobx": "^3.3.1", "mobx-react": "^4.3.3", "perfect-scrollbar": "^1.3.0", "react": "^15.4.2", "react-ace": "^3.6.1", "react-custom-scrollbars": "^4.1.2", "react-dom": "^15.4.2", "react-dropzone": "^3.6.0", "react-router": "^2.8.1", "react-tap-event-plugin": "^2.0.1", "react-tooltip": "^3.2.1", "webpack": "^1", "webpack-dev-server": "~1.14.1", "async-props": "^0.3.2", "babel-cli": "^6.18.0", "brace": "^0.11.0", "card-validator": "^4.0.0", "chart.js": "^2.5.0", "css-loader": "^0.26.1", "jquery": "^3.1.0", "moment": "^2.18.1", "path": "^0.12.7", "query-string": "^6.1.0", "react-bootstrap": "^0.30.7", "react-color": "^2.11.1", "react-copy-to-clipboard": "^4.2.3", "react-datetime": "^2.8.10", "react-dots-loader": "^1.1.4", "react-loader-advanced": "^1.6.2", "react-redux": "^4.4.6", "react-s-alert": "^1.2.2", "redux": "^3.6.0", "redux-form": "^6.2.1", "redux-thunk": "^2.1.0", "style-loader": "^0.13.1", "underscore": "^1.8.3"}, "devDependencies": {"babel-core": "^6.7.7", "babel-loader": "^6.2.4", "babel-plugin-transform-class-properties": "^6.16.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-preset-es2015": "^6.6.0", "babel-preset-react": "^6.16.0", "babel-preset-stage-0": "^6.16.0", "babel-preset-stage-1": "^6.16.0", "babel-register": "^6.24.1", "chai": "^3.5.0", "enzyme": "^2.8.2", "html-webpack-plugin": "^2.28.0", "jsdom": "^9.12.0", "json-loader": "^0.5.7", "mocha": "^3.3.0", "nock": "^9.0.13", "node-sass": "^4.9.2", "react-addons-test-utils": "^15.5.1", "redux-mock-store": "^1.2.3", "sass-loader": "^4.1.1", "sinon": "^2.2.0", "uglifyjs-webpack-plugin": "v1.0.0-beta.1", "webpack": "^1", "webpack-merge": "^4.2.1"}}