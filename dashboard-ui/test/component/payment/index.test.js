/*import React from 'react';
import Upgrade from '../../../app/components/payment/index.js';

describe('<Upgrade/>', () => {
  let wrapper;
  
  before(() => {
    const props = {
      planId: util.makeString(),
      show: true,
      hide: sinon.spy()
    };
    wrapper = shallow(<Upgrade {...props}/>, {...themeContext, selectedPlan: {
      id: 0
    }});
    console.log(wrapper.state());
  }); 

  it('Component is rendering', () => {
    expect(wrapper).to.exist;
  });
  
});*/