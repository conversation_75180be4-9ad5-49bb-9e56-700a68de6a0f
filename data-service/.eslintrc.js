/*
#     EzyBackend - Core Engine that powers Backend as a Service
#     (c) 2014 HackerBay, Inc.
#     EzyBackend may be freely distributed under the Apache 2 License
#
#     Modern ESLint configuration for Node.js 20+
*/

module.exports = {
  env: {
    node: true,
    es2022: true,
    mocha: true,
  },
  extends: [
    'eslint:recommended',
    'airbnb-base',
  ],
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module',
  },
  rules: {
    // Legacy compatibility rules
    'for-direction': 'off',
    'getter-return': 'off',
    'no-underscore-dangle': 'off',
    'no-restricted-globals': 'off',
    'no-throw-literal': 'off',
    'no-plusplus': 'off',
    'no-useless-escape': 'off',

    // Updated rules for modern Node.js
    'no-console': 'warn',
    'no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    'consistent-return': 'warn',
    'no-param-reassign': 'warn',
    'func-names': 'off',
    'prefer-arrow-callback': 'warn',
    'object-shorthand': 'warn',
    'prefer-const': 'warn',
    'no-var': 'error',
    'prefer-template': 'warn',

    // Import rules
    'import/no-dynamic-require': 'warn',
    'import/no-extraneous-dependencies': ['error', {
      devDependencies: [
        'test/**',
        'scripts/**',
        '**/*.test.js',
        '**/*.spec.js',
        'webpack.config.js',
      ],
    }],

    // Style rules
    'max-len': ['warn', {
      code: 120,
      ignoreUrls: true,
      ignoreRegExpLiterals: true
    }],
    'semi': ['error', 'always'],
    'indent': ['error', 2],
    'comma-dangle': ['error', 'always-multiline'],
    'quotes': ['error', 'single', { allowTemplateLiterals: true }],
  },
  overrides: [
    {
      files: ['test/**/*.js'],
      rules: {
        'no-unused-expressions': 'off',
        'import/no-extraneous-dependencies': 'off',
      },
    },
    {
      files: ['scripts/**/*.js'],
      rules: {
        'no-console': 'off',
      },
    },
  ],
  ignorePatterns: [
    'node_modules/',
    'dist/',
    'sdk/dist/',
    'test/test.js', // Generated file
    'coverage/',
  ],
};
