/*
#     EzyBackend User Service - ESLint Configuration
#     (c) 2014 HackerBay, Inc. 
#     EzyBackend may be freely distributed under the Apache 2 License
#     
#     Modern ESLint configuration for Node.js 20+
*/

module.exports = {
  env: {
    node: true,
    es2022: true,
    mocha: true,
  },
  extends: [
    'eslint:recommended',
    'airbnb-base',
  ],
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module',
  },
  rules: {
    // Legacy compatibility rules for gradual migration
    'no-console': 'warn',
    'no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    'consistent-return': 'warn',
    'no-param-reassign': 'warn',
    'no-underscore-dangle': 'off',
    'func-names': 'off',
    'prefer-arrow-callback': 'warn',
    'object-shorthand': 'warn',
    'prefer-const': 'warn',
    'no-var': 'error',
    'prefer-template': 'warn',
    
    // Import rules
    'import/no-dynamic-require': 'warn',
    'import/no-extraneous-dependencies': ['error', {
      devDependencies: [
        'test/**',
        '**/*.test.js',
        '**/*.spec.js',
      ],
    }],
    
    // Node.js specific
    'global-require': 'warn',
    'no-process-exit': 'warn',
    
    // Authentication service specific
    'no-shadow': 'warn',
    'camelcase': 'warn',
    
    // Style preferences
    'max-len': ['warn', { 
      code: 120, 
      ignoreUrls: true,
      ignoreRegExpLiterals: true,
      ignoreStrings: true,
      ignoreTemplateLiterals: true
    }],
    'comma-dangle': ['error', 'always-multiline'],
    'quotes': ['error', 'single', { allowTemplateLiterals: true }],
    'semi': ['error', 'always'],
    'indent': ['error', 2],
    
    // Relaxed rules for legacy codebase
    'no-use-before-define': 'warn',
    'no-nested-ternary': 'warn',
    'no-lonely-if': 'warn',
  },
  overrides: [
    {
      files: ['test/**/*.js'],
      env: {
        mocha: true,
      },
      globals: {
        expect: 'readonly',
        chai: 'readonly',
        request: 'readonly',
        app: 'readonly',
        util: 'readonly',
        URL: 'readonly',
      },
      rules: {
        'no-unused-expressions': 'off',
        'import/no-extraneous-dependencies': 'off',
        'prefer-arrow-callback': 'off',
        'func-names': 'off',
      },
    },
    {
      files: ['config/**/*.js'],
      rules: {
        'global-require': 'off',
        'import/no-dynamic-require': 'off',
      },
    },
    {
      files: ['model/**/*.js', 'services/**/*.js'],
      rules: {
        'class-methods-use-this': 'off',
      },
    },
  ],
  ignorePatterns: [
    'node_modules/',
    'coverage/',
    'dist/',
    'uploads/',
  ],
};
