{"name": "dashboard-ui", "version": "1.0.0", "description": "Dashboard UI for Cloudboost platform", "main": "index.html", "scripts": {"start": "node server.js", "client": "webpack --watch --progress", "build": "webpack --progress", "test": "echo 'Tests run'", "test:watch": "mocha -w --compilers js:babel-register --require ./test/helpers.js --recursive", "pretest": "eslint . --ext .js --fix"}, "author": "Cloudboost", "license": "ISC", "bugs": {"url": "https://github.com/p4suhag/dashboard-ui/issues"}, "homepage": "https://github.com/p4suhag/dashboard-ui#readme", "devDependencies": {"babel-core": "^6.7.7", "babel-eslint": "^10.0.1", "babel-loader": "^6.2.4", "babel-plugin-transform-class-properties": "^6.16.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-preset-es2015": "^6.6.0", "babel-preset-react": "^6.5.0", "babel-preset-stage-0": "^6.16.0", "babel-preset-stage-1": "^6.16.0", "babel-register": "^6.24.1", "chai": "^3.5.0", "enzyme": "^2.8.2", "eslint": "^5.13.0", "eslint-config-standard": "^12.0.0", "eslint-config-standard-react": "^7.0.2", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-react": "^7.12.4", "eslint-plugin-standard": "^4.0.0", "html-webpack-plugin": "^2.28.0", "jsdom": "^9.12.0", "json-loader": "^0.5.7", "mocha": "^3.3.0", "nock": "^9.0.13", "node-sass": "^4.9.2", "react-addons-test-utils": "^15.5.1", "redux-devtools-extension": "^2.13.8", "redux-mock-store": "^1.2.3", "sass-loader": "^4.1.1", "sinon": "^2.2.0", "uglifyjs-webpack-plugin": "v1.0.0-beta.1", "webpack": "^1"}, "dependencies": {"async-props": "^0.3.2", "axios": "^0.15.3", "babel-cli": "^6.18.0", "brace": "^0.11.0", "card-validator": "^4.0.0", "chart.js": "^2.5.0", "ezybackend": "^2.0.262", "css-loader": "^0.26.1", "ejs": "^2.5.3", "express": "^4.14.0", "file-saver": "^2.0.1", "jquery": "^3.1.0", "material-ui": "^0.16.7", "moment": "^2.18.1", "path": "^0.12.7", "prop-types": "^15.6.2", "query-string": "^6.1.0", "react": "^15.0.1", "react-ace": "^4.1.6", "react-bootstrap": "^0.30.7", "react-color": "^2.11.1", "react-copy-to-clipboard": "^4.2.3", "react-custom-scrollbars": "^4.1.2", "react-datetime": "^2.8.10", "react-dom": "^15.0.1", "react-dots-loader": "^1.1.4", "react-dropzone": "^3.13.1", "react-loader-advanced": "^1.6.2", "react-redux": "^4.4.6", "react-router": "^2.0.1", "react-s-alert": "^1.2.2", "react-tap-event-plugin": "^2.0.1", "react-tooltip": "^3.2.7", "redux": "^3.6.0", "redux-form": "^6.2.1", "redux-logger": "^3.0.6", "redux-promise": "^0.6.0", "redux-thunk": "^2.1.0", "style-loader": "^0.13.1", "underscore": "^1.8.3"}}