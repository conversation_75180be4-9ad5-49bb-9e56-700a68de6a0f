/*
#     EzyBackend - Core Engine that powers Backend as a Service
#     (c) 2014 HackerBay, Inc. 
#     EzyBackend may be freely distributed under the Apache 2 License
#     
#     ESBuild configuration - Alternative to Webpack for faster builds
#     Usage: node esbuild.config.js
*/

const esbuild = require('esbuild');
const path = require('path');
const fs = require('fs');

const packageJson = require('./package.json');

// Check if esbuild is installed
try {
  require.resolve('esbuild');
} catch (e) {
  console.log('📦 ESBuild not installed. Install with: npm install --save-dev esbuild');
  console.log('🔧 Using Webpack instead...');
  process.exit(0);
}

const buildConfig = {
  entryPoints: ['./sdk/src/entry.js'],
  bundle: true,
  platform: 'browser',
  target: ['es2020'],
  format: 'umd',
  globalName: 'EzyBackend',
  external: ['socket.io-client', 'axios'],
  define: {
    'process.env.NODE_ENV': '"production"',
    'process.env.EZYBACKEND_VERSION': `"${packageJson.version}"`,
  },
  banner: {
    js: `/*
 * EzyBackend SDK v${packageJson.version}
 * (c) 2014 HackerBay, Inc.
 * Licensed under Apache 2.0
 * Built with ESBuild on ${new Date().toISOString()}
 */`,
  },
};

async function build() {
  console.log('🚀 Building EzyBackend SDK with ESBuild...');
  
  try {
    // Development build
    console.log('📦 Building development version...');
    await esbuild.build({
      ...buildConfig,
      outfile: './sdk/dist/ezybackend.js',
      sourcemap: true,
      minify: false,
    });
    
    // Production build
    console.log('📦 Building production version...');
    await esbuild.build({
      ...buildConfig,
      outfile: './sdk/dist/ezybackend.min.js',
      sourcemap: true,
      minify: true,
      drop: ['console', 'debugger'],
    });
    
    // Get file sizes
    const devSize = fs.statSync('./sdk/dist/ezybackend.js').size;
    const prodSize = fs.statSync('./sdk/dist/ezybackend.min.js').size;
    
    console.log('✅ Build completed successfully!');
    console.log(`📊 Development: ${(devSize / 1024).toFixed(2)} KB`);
    console.log(`📊 Production: ${(prodSize / 1024).toFixed(2)} KB`);
    console.log(`🎯 Compression: ${((1 - prodSize / devSize) * 100).toFixed(1)}%`);
    
  } catch (error) {
    console.error('❌ Build failed:', error);
    process.exit(1);
  }
}

// Watch mode
async function watch() {
  console.log('👀 Starting ESBuild watch mode...');
  
  const ctx = await esbuild.context({
    ...buildConfig,
    outfile: './sdk/dist/ezybackend.js',
    sourcemap: true,
    minify: false,
  });
  
  await ctx.watch();
  console.log('👀 Watching for changes...');
}

// CLI handling
const command = process.argv[2];

if (command === 'watch') {
  watch();
} else {
  build();
}

module.exports = { buildConfig, build, watch };
