!function(root,factory){"object"==typeof exports&&"object"==typeof module?module.exports=factory(require("axios"),require("socket.io-client")):"function"==typeof define&&define.amd?define("ezybackend",["axios","socket.io-client"],factory):"object"==typeof exports?exports.ezybackend=factory(require("axios"),require("socket.io-client")):root.ezybackend=factory(root.axios,root["socket.io-client"])}(this,function(__WEBPACK_EXTERNAL_MODULE_49__,__WEBPACK_EXTERNAL_MODULE_74__){return function(modules){function __webpack_require__(moduleId){if(installedModules[moduleId])return installedModules[moduleId].exports;var module=installedModules[moduleId]={exports:{},id:moduleId,loaded:!1};return modules[moduleId].call(module.exports,module,module.exports,__webpack_require__),module.loaded=!0,module.exports}var installedModules={};return __webpack_require__.m=modules,__webpack_require__.c=installedModules,__webpack_require__.p="",__webpack_require__(0)}(function(modules){for(var i in modules)if(Object.prototype.hasOwnProperty.call(modules,i))switch(typeof modules[i]){case"function":break;case"object":modules[i]=function(_m){var args=_m.slice(1),fn=modules[_m[0]];return function(a,b,c){fn.apply(this,[a,b,c].concat(args))}}(modules[i]);break;default:modules[i]=modules[modules[i]]}return modules}([function(module,exports,__webpack_require__){"use strict";function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}var _CB=__webpack_require__(1),_CB2=_interopRequireDefault(_CB);try{window&&("ReactNative"==navigator.product?(_CB2.default._isNode=!0,_CB2.default._isNative=!0):_CB2.default._isNode=!1)}catch(e){_CB2.default._isNode=!0}__webpack_require__(41),__webpack_require__(72),__webpack_require__(76),__webpack_require__(77),__webpack_require__(78),__webpack_require__(79),__webpack_require__(80),__webpack_require__(81),__webpack_require__(82),__webpack_require__(83),__webpack_require__(85),__webpack_require__(86),__webpack_require__(87),__webpack_require__(88);try{window.CB=_CB2.default}catch(e){}module.exports=_CB2.default},function(module,exports,__webpack_require__){(function(process){"use strict";function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(exports,"__esModule",{value:!0});var _createClass=function(){function defineProperties(target,props){for(var i=0;i<props.length;i++){var descriptor=props[i];descriptor.enumerable=descriptor.enumerable||!1,descriptor.configurable=!0,"value"in descriptor&&(descriptor.writable=!0),Object.defineProperty(target,descriptor.key,descriptor)}}return function(Constructor,protoProps,staticProps){return protoProps&&defineProperties(Constructor.prototype,protoProps),staticProps&&defineProperties(Constructor,staticProps),Constructor}}(),_bluebird=__webpack_require__(3),_bluebird2=_interopRequireDefault(_bluebird),EzyBackend=function(){function EzyBackend(){_classCallCheck(this,EzyBackend),this._isNode=!1,this._isNative=!1,this.Socket=null,this.io=null,this.apiUrl="https://api.ezybackend.io","undefined"!=typeof process&&process.versions&&process.versions.node?this._isNode=!0:this._isNode=!1,this.Events={trigger:this.trigger.bind(this)}}return _createClass(EzyBackend,[{key:"_ajaxIE8",value:function(method,url,data){var promise=new this.Promise,xdr=new XDomainRequest;return xdr.onload=function(){var response;try{response=JSON.parse(xdr.responseText)}catch(e){promise.reject(e)}response&&promise.resolve(response)},xdr.onerror=xdr.ontimeout=function(){var fakeResponse={responseText:JSON.stringify({code:500,error:"IE's XDomainRequest does not supply error info."})};promise.reject(fakeResponse)},xdr.onprogress=function(){},xdr.open(method,url),xdr.send(data),promise}},{key:"trigger",value:function(events){var event,node,calls,tail,args,all,rest;if(!(calls=this._callbacks))return this;for(all=calls.all,events=events.split(eventSplitter),rest=slice.call(arguments,1),event=events.shift();event;){if(node=calls[event])for(tail=node.tail;(node=node.next)!==tail;)node.callback.apply(node.context||this,rest);if(node=all)for(tail=node.tail,args=[event].concat(rest);(node=node.next)!==tail;)node.callback.apply(node.context||this,args);event=events.shift()}return this}},{key:"Promise",value:function(){var resolve,reject,promise=new _bluebird2.default(function(){resolve=arguments[0],reject=arguments[1]});return{resolve:resolve,reject:reject,promise:promise}}}]),EzyBackend}(),CB=new EzyBackend;Object.setPrototypeOf?Object.setPrototypeOf(CB.Promise,_bluebird2.default):CB.Promise.prototype=_bluebird2.default.prototype,exports.default=CB}).call(exports,__webpack_require__(2))},function(module,exports){function defaultSetTimout(){throw new Error("setTimeout has not been defined")}function defaultClearTimeout(){throw new Error("clearTimeout has not been defined")}function runTimeout(fun){if(cachedSetTimeout===setTimeout)return setTimeout(fun,0);if((cachedSetTimeout===defaultSetTimout||!cachedSetTimeout)&&setTimeout)return cachedSetTimeout=setTimeout,setTimeout(fun,0);try{return cachedSetTimeout(fun,0)}catch(e){try{return cachedSetTimeout.call(null,fun,0)}catch(e){return cachedSetTimeout.call(this,fun,0)}}}function runClearTimeout(marker){if(cachedClearTimeout===clearTimeout)return clearTimeout(marker);if((cachedClearTimeout===defaultClearTimeout||!cachedClearTimeout)&&clearTimeout)return cachedClearTimeout=clearTimeout,clearTimeout(marker);try{return cachedClearTimeout(marker)}catch(e){try{return cachedClearTimeout.call(null,marker)}catch(e){return cachedClearTimeout.call(this,marker)}}}function cleanUpNextTick(){draining&&currentQueue&&(draining=!1,currentQueue.length?queue=currentQueue.concat(queue):queueIndex=-1,queue.length&&drainQueue())}function drainQueue(){if(!draining){var timeout=runTimeout(cleanUpNextTick);draining=!0;for(var len=queue.length;len;){for(currentQueue=queue,queue=[];++queueIndex<len;)currentQueue&&currentQueue[queueIndex].run();queueIndex=-1,len=queue.length}currentQueue=null,draining=!1,runClearTimeout(timeout)}}function Item(fun,array){this.fun=fun,this.array=array}function noop(){}var cachedSetTimeout,cachedClearTimeout,process=module.exports={};!function(){try{cachedSetTimeout="function"==typeof setTimeout?setTimeout:defaultSetTimout}catch(e){cachedSetTimeout=defaultSetTimout}try{cachedClearTimeout="function"==typeof clearTimeout?clearTimeout:defaultClearTimeout}catch(e){cachedClearTimeout=defaultClearTimeout}}();var currentQueue,queue=[],draining=!1,queueIndex=-1;process.nextTick=function(fun){var args=new Array(arguments.length-1);if(arguments.length>1)for(var i=1;i<arguments.length;i++)args[i-1]=arguments[i];queue.push(new Item(fun,args)),1!==queue.length||draining||runTimeout(drainQueue)},Item.prototype.run=function(){this.fun.apply(null,this.array)},process.title="browser",process.browser=!0,process.env={},process.argv=[],process.version="",process.versions={},process.on=noop,process.addListener=noop,process.once=noop,process.off=noop,process.removeListener=noop,process.removeAllListeners=noop,process.emit=noop,process.prependListener=noop,process.prependOnceListener=noop,process.listeners=function(name){return[]},process.binding=function(name){throw new Error("process.binding is not supported")},process.cwd=function(){return"/"},process.chdir=function(dir){throw new Error("process.chdir is not supported")},process.umask=function(){return 0}},function(module,exports,__webpack_require__){"use strict";function noConflict(){try{Promise===bluebird&&(Promise=old)}catch(e){}return bluebird}var old;"undefined"!=typeof Promise&&(old=Promise);var bluebird=__webpack_require__(4)();bluebird.noConflict=noConflict,module.exports=bluebird},function(module,exports,__webpack_require__){(function(process){"use strict";module.exports=function(){function Proxyable(){}function check(self,executor){if(null==self||self.constructor!==Promise)throw new TypeError("the promise constructor cannot be invoked directly\n\n    See http://goo.gl/MqrFmX\n");if("function"!=typeof executor)throw new TypeError("expecting a function but got "+util.classString(executor))}function Promise(executor){executor!==INTERNAL&&check(this,executor),this._bitField=0,this._fulfillmentHandler0=void 0,this._rejectionHandler0=void 0,this._promise0=void 0,this._receiver0=void 0,this._resolveFromExecutor(executor),this._promiseCreated(),this._fireEvent("promiseCreated",this)}function deferResolve(v){this.promise._resolveCallback(v)}function deferReject(v){this.promise._rejectCallback(v,!1)}function fillTypes(value){var p=new Promise(INTERNAL);p._fulfillmentHandler0=value,p._rejectionHandler0=value,p._promise0=value,p._receiver0=value}var getDomain,makeSelfResolutionError=function(){return new TypeError("circular promise resolution chain\n\n    See http://goo.gl/MqrFmX\n")},reflectHandler=function(){return new Promise.PromiseInspection(this._target())},apiRejection=function(msg){return Promise.reject(new TypeError(msg))},UNDEFINED_BINDING={},util=__webpack_require__(5);getDomain=util.isNode?function(){var ret=process.domain;return void 0===ret&&(ret=null),ret}:function(){return null},util.notEnumerableProp(Promise,"_getDomain",getDomain);var es5=__webpack_require__(6),Async=__webpack_require__(7),async=new Async;es5.defineProperty(Promise,"_async",{value:async});var errors=__webpack_require__(12),TypeError=Promise.TypeError=errors.TypeError;Promise.RangeError=errors.RangeError;var CancellationError=Promise.CancellationError=errors.CancellationError;Promise.TimeoutError=errors.TimeoutError,Promise.OperationalError=errors.OperationalError,Promise.RejectionError=errors.OperationalError,Promise.AggregateError=errors.AggregateError;var INTERNAL=function(){},APPLY={},NEXT_FILTER={},tryConvertToPromise=__webpack_require__(13)(Promise,INTERNAL),PromiseArray=__webpack_require__(14)(Promise,INTERNAL,tryConvertToPromise,apiRejection,Proxyable),Context=__webpack_require__(15)(Promise),createContext=Context.create,debug=__webpack_require__(16)(Promise,Context),PassThroughHandlerContext=(debug.CapturedTrace,__webpack_require__(17)(Promise,tryConvertToPromise,NEXT_FILTER)),catchFilter=__webpack_require__(18)(NEXT_FILTER),nodebackForPromise=__webpack_require__(19),errorObj=util.errorObj,tryCatch=util.tryCatch;return Promise.prototype.toString=function(){return"[object Promise]"},Promise.prototype.caught=Promise.prototype.catch=function(fn){var len=arguments.length;if(len>1){var i,catchInstances=new Array(len-1),j=0;for(i=0;i<len-1;++i){var item=arguments[i];if(!util.isObject(item))return apiRejection("Catch statement predicate: expecting an object but got "+util.classString(item));catchInstances[j++]=item}return catchInstances.length=j,fn=arguments[i],this.then(void 0,catchFilter(catchInstances,fn,this))}return this.then(void 0,fn)},Promise.prototype.reflect=function(){return this._then(reflectHandler,reflectHandler,void 0,this,void 0)},Promise.prototype.then=function(didFulfill,didReject){if(debug.warnings()&&arguments.length>0&&"function"!=typeof didFulfill&&"function"!=typeof didReject){var msg=".then() only accepts functions but was passed: "+util.classString(didFulfill);arguments.length>1&&(msg+=", "+util.classString(didReject)),this._warn(msg)}return this._then(didFulfill,didReject,void 0,void 0,void 0)},Promise.prototype.done=function(didFulfill,didReject){var promise=this._then(didFulfill,didReject,void 0,void 0,void 0);promise._setIsFinal()},Promise.prototype.spread=function(fn){return"function"!=typeof fn?apiRejection("expecting a function but got "+util.classString(fn)):this.all()._then(fn,void 0,void 0,APPLY,void 0)},Promise.prototype.toJSON=function(){var ret={isFulfilled:!1,isRejected:!1,fulfillmentValue:void 0,rejectionReason:void 0};return this.isFulfilled()?(ret.fulfillmentValue=this.value(),ret.isFulfilled=!0):this.isRejected()&&(ret.rejectionReason=this.reason(),ret.isRejected=!0),ret},Promise.prototype.all=function(){return arguments.length>0&&this._warn(".all() was passed arguments but it does not take any"),new PromiseArray(this).promise()},Promise.prototype.error=function(fn){return this.caught(util.originatesFromRejection,fn)},Promise.getNewLibraryCopy=module.exports,Promise.is=function(val){return val instanceof Promise},Promise.fromNode=Promise.fromCallback=function(fn){var ret=new Promise(INTERNAL);ret._captureStackTrace();var multiArgs=arguments.length>1&&!!Object(arguments[1]).multiArgs,result=tryCatch(fn)(nodebackForPromise(ret,multiArgs));return result===errorObj&&ret._rejectCallback(result.e,!0),ret._isFateSealed()||ret._setAsyncGuaranteed(),ret},Promise.all=function(promises){return new PromiseArray(promises).promise()},Promise.cast=function(obj){var ret=tryConvertToPromise(obj);return ret instanceof Promise||(ret=new Promise(INTERNAL),ret._captureStackTrace(),ret._setFulfilled(),ret._rejectionHandler0=obj),ret},Promise.resolve=Promise.fulfilled=Promise.cast,Promise.reject=Promise.rejected=function(reason){var ret=new Promise(INTERNAL);return ret._captureStackTrace(),ret._rejectCallback(reason,!0),ret},Promise.setScheduler=function(fn){if("function"!=typeof fn)throw new TypeError("expecting a function but got "+util.classString(fn));return async.setScheduler(fn)},Promise.prototype._then=function(didFulfill,didReject,_,receiver,internalData){var haveInternalData=void 0!==internalData,promise=haveInternalData?internalData:new Promise(INTERNAL),target=this._target(),bitField=target._bitField;haveInternalData||(promise._propagateFrom(this,3),promise._captureStackTrace(),void 0===receiver&&0!==(2097152&this._bitField)&&(receiver=0!==(50397184&bitField)?this._boundValue():target===this?void 0:this._boundTo),this._fireEvent("promiseChained",this,promise));var domain=getDomain();if(0!==(50397184&bitField)){var handler,value,settler=target._settlePromiseCtx;0!==(33554432&bitField)?(value=target._rejectionHandler0,handler=didFulfill):0!==(16777216&bitField)?(value=target._fulfillmentHandler0,handler=didReject,target._unsetRejectionIsUnhandled()):(settler=target._settlePromiseLateCancellationObserver,value=new CancellationError("late cancellation observer"),target._attachExtraTrace(value),handler=didReject),async.invoke(settler,target,{handler:null===domain?handler:"function"==typeof handler&&util.domainBind(domain,handler),promise:promise,receiver:receiver,value:value})}else target._addCallbacks(didFulfill,didReject,promise,receiver,domain);return promise},Promise.prototype._length=function(){return 65535&this._bitField},Promise.prototype._isFateSealed=function(){return 0!==(117506048&this._bitField)},Promise.prototype._isFollowing=function(){return 67108864===(67108864&this._bitField)},Promise.prototype._setLength=function(len){this._bitField=this._bitField&-65536|65535&len},Promise.prototype._setFulfilled=function(){this._bitField=33554432|this._bitField,this._fireEvent("promiseFulfilled",this)},Promise.prototype._setRejected=function(){this._bitField=16777216|this._bitField,this._fireEvent("promiseRejected",this)},Promise.prototype._setFollowing=function(){this._bitField=67108864|this._bitField,this._fireEvent("promiseResolved",this)},Promise.prototype._setIsFinal=function(){this._bitField=4194304|this._bitField},Promise.prototype._isFinal=function(){return(4194304&this._bitField)>0},Promise.prototype._unsetCancelled=function(){this._bitField=this._bitField&-65537},Promise.prototype._setCancelled=function(){this._bitField=65536|this._bitField,this._fireEvent("promiseCancelled",this)},Promise.prototype._setWillBeCancelled=function(){this._bitField=8388608|this._bitField},Promise.prototype._setAsyncGuaranteed=function(){async.hasCustomScheduler()||(this._bitField=134217728|this._bitField)},Promise.prototype._receiverAt=function(index){var ret=0===index?this._receiver0:this[4*index-4+3];if(ret!==UNDEFINED_BINDING)return void 0===ret&&this._isBound()?this._boundValue():ret},Promise.prototype._promiseAt=function(index){return this[4*index-4+2]},Promise.prototype._fulfillmentHandlerAt=function(index){return this[4*index-4+0]},Promise.prototype._rejectionHandlerAt=function(index){return this[4*index-4+1]},Promise.prototype._boundValue=function(){},Promise.prototype._migrateCallback0=function(follower){var fulfill=(follower._bitField,follower._fulfillmentHandler0),reject=follower._rejectionHandler0,promise=follower._promise0,receiver=follower._receiverAt(0);void 0===receiver&&(receiver=UNDEFINED_BINDING),this._addCallbacks(fulfill,reject,promise,receiver,null)},Promise.prototype._migrateCallbackAt=function(follower,index){var fulfill=follower._fulfillmentHandlerAt(index),reject=follower._rejectionHandlerAt(index),promise=follower._promiseAt(index),receiver=follower._receiverAt(index);void 0===receiver&&(receiver=UNDEFINED_BINDING),this._addCallbacks(fulfill,reject,promise,receiver,null)},Promise.prototype._addCallbacks=function(fulfill,reject,promise,receiver,domain){var index=this._length();if(index>=65531&&(index=0,this._setLength(0)),0===index)this._promise0=promise,this._receiver0=receiver,"function"==typeof fulfill&&(this._fulfillmentHandler0=null===domain?fulfill:util.domainBind(domain,fulfill)),"function"==typeof reject&&(this._rejectionHandler0=null===domain?reject:util.domainBind(domain,reject));else{var base=4*index-4;this[base+2]=promise,this[base+3]=receiver,"function"==typeof fulfill&&(this[base+0]=null===domain?fulfill:util.domainBind(domain,fulfill)),"function"==typeof reject&&(this[base+1]=null===domain?reject:util.domainBind(domain,reject))}return this._setLength(index+1),index},Promise.prototype._proxy=function(proxyable,arg){this._addCallbacks(void 0,void 0,arg,proxyable,null)},Promise.prototype._resolveCallback=function(value,shouldBind){if(0===(117506048&this._bitField)){if(value===this)return this._rejectCallback(makeSelfResolutionError(),!1);var maybePromise=tryConvertToPromise(value,this);if(!(maybePromise instanceof Promise))return this._fulfill(value);shouldBind&&this._propagateFrom(maybePromise,2);var promise=maybePromise._target();if(promise===this)return void this._reject(makeSelfResolutionError());var bitField=promise._bitField;if(0===(50397184&bitField)){var len=this._length();len>0&&promise._migrateCallback0(this);for(var i=1;i<len;++i)promise._migrateCallbackAt(this,i);this._setFollowing(),this._setLength(0),this._setFollowee(promise)}else if(0!==(33554432&bitField))this._fulfill(promise._value());else if(0!==(16777216&bitField))this._reject(promise._reason());else{var reason=new CancellationError("late cancellation observer");promise._attachExtraTrace(reason),this._reject(reason)}}},Promise.prototype._rejectCallback=function(reason,synchronous,ignoreNonErrorWarnings){var trace=util.ensureErrorObject(reason),hasStack=trace===reason;if(!hasStack&&!ignoreNonErrorWarnings&&debug.warnings()){var message="a promise was rejected with a non-error: "+util.classString(reason);this._warn(message,!0)}this._attachExtraTrace(trace,!!synchronous&&hasStack),this._reject(reason)},Promise.prototype._resolveFromExecutor=function(executor){if(executor!==INTERNAL){var promise=this;this._captureStackTrace(),this._pushContext();var synchronous=!0,r=this._execute(executor,function(value){promise._resolveCallback(value)},function(reason){promise._rejectCallback(reason,synchronous)});synchronous=!1,this._popContext(),void 0!==r&&promise._rejectCallback(r,!0)}},Promise.prototype._settlePromiseFromHandler=function(handler,receiver,value,promise){var bitField=promise._bitField;if(0===(65536&bitField)){promise._pushContext();var x;receiver===APPLY?value&&"number"==typeof value.length?x=tryCatch(handler).apply(this._boundValue(),value):(x=errorObj,x.e=new TypeError("cannot .spread() a non-array: "+util.classString(value))):x=tryCatch(handler).call(receiver,value);var promiseCreated=promise._popContext();bitField=promise._bitField,0===(65536&bitField)&&(x===NEXT_FILTER?promise._reject(value):x===errorObj?promise._rejectCallback(x.e,!1):(debug.checkForgottenReturns(x,promiseCreated,"",promise,this),promise._resolveCallback(x)))}},Promise.prototype._target=function(){for(var ret=this;ret._isFollowing();)ret=ret._followee();return ret},Promise.prototype._followee=function(){return this._rejectionHandler0},Promise.prototype._setFollowee=function(promise){this._rejectionHandler0=promise},Promise.prototype._settlePromise=function(promise,handler,receiver,value){var isPromise=promise instanceof Promise,bitField=this._bitField,asyncGuaranteed=0!==(134217728&bitField);0!==(65536&bitField)?(isPromise&&promise._invokeInternalOnCancel(),receiver instanceof PassThroughHandlerContext&&receiver.isFinallyHandler()?(receiver.cancelPromise=promise,tryCatch(handler).call(receiver,value)===errorObj&&promise._reject(errorObj.e)):handler===reflectHandler?promise._fulfill(reflectHandler.call(receiver)):receiver instanceof Proxyable?receiver._promiseCancelled(promise):isPromise||promise instanceof PromiseArray?promise._cancel():receiver.cancel()):"function"==typeof handler?isPromise?(asyncGuaranteed&&promise._setAsyncGuaranteed(),this._settlePromiseFromHandler(handler,receiver,value,promise)):handler.call(receiver,value,promise):receiver instanceof Proxyable?receiver._isResolved()||(0!==(33554432&bitField)?receiver._promiseFulfilled(value,promise):receiver._promiseRejected(value,promise)):isPromise&&(asyncGuaranteed&&promise._setAsyncGuaranteed(),0!==(33554432&bitField)?promise._fulfill(value):promise._reject(value))},Promise.prototype._settlePromiseLateCancellationObserver=function(ctx){var handler=ctx.handler,promise=ctx.promise,receiver=ctx.receiver,value=ctx.value;"function"==typeof handler?promise instanceof Promise?this._settlePromiseFromHandler(handler,receiver,value,promise):handler.call(receiver,value,promise):promise instanceof Promise&&promise._reject(value)},Promise.prototype._settlePromiseCtx=function(ctx){this._settlePromise(ctx.promise,ctx.handler,ctx.receiver,ctx.value)},Promise.prototype._settlePromise0=function(handler,value,bitField){var promise=this._promise0,receiver=this._receiverAt(0);this._promise0=void 0,this._receiver0=void 0,this._settlePromise(promise,handler,receiver,value)},Promise.prototype._clearCallbackDataAtIndex=function(index){var base=4*index-4;this[base+2]=this[base+3]=this[base+0]=this[base+1]=void 0},Promise.prototype._fulfill=function(value){var bitField=this._bitField;if(!((117506048&bitField)>>>16)){if(value===this){var err=makeSelfResolutionError();return this._attachExtraTrace(err),this._reject(err)}this._setFulfilled(),this._rejectionHandler0=value,(65535&bitField)>0&&(0!==(134217728&bitField)?this._settlePromises():async.settlePromises(this),this._dereferenceTrace())}},Promise.prototype._reject=function(reason){var bitField=this._bitField;if(!((117506048&bitField)>>>16))return this._setRejected(),this._fulfillmentHandler0=reason,this._isFinal()?async.fatalError(reason,util.isNode):void((65535&bitField)>0?async.settlePromises(this):this._ensurePossibleRejectionHandled())},Promise.prototype._fulfillPromises=function(len,value){for(var i=1;i<len;i++){var handler=this._fulfillmentHandlerAt(i),promise=this._promiseAt(i),receiver=this._receiverAt(i);this._clearCallbackDataAtIndex(i),this._settlePromise(promise,handler,receiver,value)}},Promise.prototype._rejectPromises=function(len,reason){for(var i=1;i<len;i++){var handler=this._rejectionHandlerAt(i),promise=this._promiseAt(i),receiver=this._receiverAt(i);this._clearCallbackDataAtIndex(i),this._settlePromise(promise,handler,receiver,reason)}},Promise.prototype._settlePromises=function(){var bitField=this._bitField,len=65535&bitField;if(len>0){if(0!==(16842752&bitField)){var reason=this._fulfillmentHandler0;this._settlePromise0(this._rejectionHandler0,reason,bitField),this._rejectPromises(len,reason)}else{var value=this._rejectionHandler0;this._settlePromise0(this._fulfillmentHandler0,value,bitField),this._fulfillPromises(len,value)}this._setLength(0)}this._clearCancellationData()},Promise.prototype._settledValue=function(){var bitField=this._bitField;return 0!==(33554432&bitField)?this._rejectionHandler0:0!==(16777216&bitField)?this._fulfillmentHandler0:void 0},Promise.defer=Promise.pending=function(){debug.deprecated("Promise.defer","new Promise");var promise=new Promise(INTERNAL);return{promise:promise,resolve:deferResolve,reject:deferReject}},util.notEnumerableProp(Promise,"_makeSelfResolutionError",makeSelfResolutionError),__webpack_require__(20)(Promise,INTERNAL,tryConvertToPromise,apiRejection,debug),__webpack_require__(21)(Promise,INTERNAL,tryConvertToPromise,debug),__webpack_require__(22)(Promise,PromiseArray,apiRejection,debug),__webpack_require__(23)(Promise),__webpack_require__(24)(Promise),__webpack_require__(25)(Promise,PromiseArray,tryConvertToPromise,INTERNAL,async,getDomain),Promise.Promise=Promise,Promise.version="3.5.4",__webpack_require__(26)(Promise,PromiseArray,apiRejection,tryConvertToPromise,INTERNAL,debug),__webpack_require__(27)(Promise),__webpack_require__(28)(Promise,apiRejection,tryConvertToPromise,createContext,INTERNAL,debug),__webpack_require__(29)(Promise,INTERNAL,debug),__webpack_require__(30)(Promise,apiRejection,INTERNAL,tryConvertToPromise,Proxyable,debug),__webpack_require__(31)(Promise),__webpack_require__(32)(Promise,INTERNAL),__webpack_require__(33)(Promise,PromiseArray,tryConvertToPromise,apiRejection),__webpack_require__(34)(Promise,INTERNAL,tryConvertToPromise,apiRejection),__webpack_require__(35)(Promise,PromiseArray,apiRejection,tryConvertToPromise,INTERNAL,debug),__webpack_require__(36)(Promise,PromiseArray,debug),__webpack_require__(37)(Promise,PromiseArray,apiRejection),__webpack_require__(38)(Promise,INTERNAL),__webpack_require__(39)(Promise,INTERNAL),__webpack_require__(40)(Promise),util.toFastProperties(Promise),util.toFastProperties(Promise.prototype),fillTypes({a:1}),fillTypes({b:2}),fillTypes({c:3}),fillTypes(1),fillTypes(function(){}),fillTypes(void 0),fillTypes(!1),fillTypes(new Promise(INTERNAL)),debug.setBounds(Async.firstLineError,util.lastLineError),Promise}}).call(exports,__webpack_require__(2))},function(module,exports,__webpack_require__){(function(global,process){"use strict";function tryCatcher(){try{var target=tryCatchTarget;return tryCatchTarget=null,target.apply(this,arguments)}catch(e){return errorObj.e=e,errorObj}}function tryCatch(fn){return tryCatchTarget=fn,tryCatcher}function isPrimitive(val){return null==val||val===!0||val===!1||"string"==typeof val||"number"==typeof val}function isObject(value){return"function"==typeof value||"object"==typeof value&&null!==value}function maybeWrapAsError(maybeError){return isPrimitive(maybeError)?new Error(safeToString(maybeError)):maybeError}function withAppended(target,appendee){var i,len=target.length,ret=new Array(len+1);for(i=0;i<len;++i)ret[i]=target[i];return ret[i]=appendee,ret}function getDataPropertyOrDefault(obj,key,defaultValue){if(!es5.isES5)return{}.hasOwnProperty.call(obj,key)?obj[key]:void 0;var desc=Object.getOwnPropertyDescriptor(obj,key);return null!=desc?null==desc.get&&null==desc.set?desc.value:defaultValue:void 0}function notEnumerableProp(obj,name,value){if(isPrimitive(obj))return obj;var descriptor={value:value,configurable:!0,enumerable:!1,writable:!0};return es5.defineProperty(obj,name,descriptor),obj}function thrower(r){throw r}function isClass(fn){try{if("function"==typeof fn){var keys=es5.names(fn.prototype),hasMethods=es5.isES5&&keys.length>1,hasMethodsOtherThanConstructor=keys.length>0&&!(1===keys.length&&"constructor"===keys[0]),hasThisAssignmentAndStaticMethods=thisAssignmentPattern.test(fn+"")&&es5.names(fn).length>0;if(hasMethods||hasMethodsOtherThanConstructor||hasThisAssignmentAndStaticMethods)return!0}return!1}catch(e){return!1}}function toFastProperties(obj){function FakeConstructor(){}function ic(){return typeof receiver.foo}FakeConstructor.prototype=obj;var receiver=new FakeConstructor;return ic(),ic(),obj}function isIdentifier(str){return rident.test(str)}function filledRange(count,prefix,suffix){for(var ret=new Array(count),i=0;i<count;++i)ret[i]=prefix+i+suffix;return ret}function safeToString(obj){try{return obj+""}catch(e){return"[no string representation]"}}function isError(obj){return obj instanceof Error||null!==obj&&"object"==typeof obj&&"string"==typeof obj.message&&"string"==typeof obj.name}function markAsOriginatingFromRejection(e){try{notEnumerableProp(e,"isOperational",!0)}catch(ignore){}}function originatesFromRejection(e){return null!=e&&(e instanceof Error.__BluebirdErrorTypes__.OperationalError||e.isOperational===!0)}function canAttachTrace(obj){return isError(obj)&&es5.propertyIsWritable(obj,"stack")}function classString(obj){return{}.toString.call(obj)}function copyDescriptors(from,to,filter){for(var keys=es5.names(from),i=0;i<keys.length;++i){var key=keys[i];if(filter(key))try{es5.defineProperty(to,key,es5.getDescriptor(from,key))}catch(ignore){}}}function env(key){return hasEnvVariables?{NODE_ENV:"production"}[key]:void 0}function getNativePromise(){if("function"==typeof Promise)try{var promise=new Promise(function(){});if("[object Promise]"==={}.toString.call(promise))return Promise}catch(e){}}function domainBind(self,cb){return self.bind(cb)}var es5=__webpack_require__(6),canEvaluate="undefined"==typeof navigator,errorObj={e:{}},tryCatchTarget,globalObject="undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:void 0!==this?this:null,inherits=function(Child,Parent){function T(){this.constructor=Child,this.constructor$=Parent;for(var propertyName in Parent.prototype)hasProp.call(Parent.prototype,propertyName)&&"$"!==propertyName.charAt(propertyName.length-1)&&(this[propertyName+"$"]=Parent.prototype[propertyName])}var hasProp={}.hasOwnProperty;return T.prototype=Parent.prototype,Child.prototype=new T,Child.prototype},inheritedDataKeys=function(){var excludedPrototypes=[Array.prototype,Object.prototype,Function.prototype],isExcludedProto=function(val){for(var i=0;i<excludedPrototypes.length;++i)if(excludedPrototypes[i]===val)return!0;return!1};if(es5.isES5){var getKeys=Object.getOwnPropertyNames;return function(obj){for(var ret=[],visitedKeys=Object.create(null);null!=obj&&!isExcludedProto(obj);){var keys;try{keys=getKeys(obj)}catch(e){return ret}for(var i=0;i<keys.length;++i){var key=keys[i];if(!visitedKeys[key]){visitedKeys[key]=!0;var desc=Object.getOwnPropertyDescriptor(obj,key);null!=desc&&null==desc.get&&null==desc.set&&ret.push(key)}}obj=es5.getPrototypeOf(obj)}return ret}}var hasProp={}.hasOwnProperty;return function(obj){if(isExcludedProto(obj))return[];var ret=[];enumeration:for(var key in obj)if(hasProp.call(obj,key))ret.push(key);else{for(var i=0;i<excludedPrototypes.length;++i)if(hasProp.call(excludedPrototypes[i],key))continue enumeration;ret.push(key)}return ret}}(),thisAssignmentPattern=/this\s*\.\s*\S+\s*=/,rident=/^[a-z$_][a-z$_0-9]*$/i,ensureErrorObject=function(){return"stack"in new Error?function(value){return canAttachTrace(value)?value:new Error(safeToString(value))}:function(value){if(canAttachTrace(value))return value;try{throw new Error(safeToString(value))}catch(err){return err}}}(),asArray=function(v){return es5.isArray(v)?v:null};if("undefined"!=typeof Symbol&&Symbol.iterator){var ArrayFrom="function"==typeof Array.from?function(v){return Array.from(v)}:function(v){for(var itResult,ret=[],it=v[Symbol.iterator]();!(itResult=it.next()).done;)ret.push(itResult.value);return ret};asArray=function(v){return es5.isArray(v)?v:null!=v&&"function"==typeof v[Symbol.iterator]?ArrayFrom(v):null}}var isNode="undefined"!=typeof process&&"[object process]"===classString(process).toLowerCase(),hasEnvVariables="undefined"!=typeof process&&!0,ret={isClass:isClass,isIdentifier:isIdentifier,inheritedDataKeys:inheritedDataKeys,getDataPropertyOrDefault:getDataPropertyOrDefault,thrower:thrower,isArray:es5.isArray,asArray:asArray,notEnumerableProp:notEnumerableProp,isPrimitive:isPrimitive,isObject:isObject,isError:isError,
canEvaluate:canEvaluate,errorObj:errorObj,tryCatch:tryCatch,inherits:inherits,withAppended:withAppended,maybeWrapAsError:maybeWrapAsError,toFastProperties:toFastProperties,filledRange:filledRange,toString:safeToString,canAttachTrace:canAttachTrace,ensureErrorObject:ensureErrorObject,originatesFromRejection:originatesFromRejection,markAsOriginatingFromRejection:markAsOriginatingFromRejection,classString:classString,copyDescriptors:copyDescriptors,hasDevTools:"undefined"!=typeof chrome&&chrome&&"function"==typeof chrome.loadTimes,isNode:isNode,hasEnvVariables:hasEnvVariables,env:env,global:globalObject,getNativePromise:getNativePromise,domainBind:domainBind};ret.isRecentNode=ret.isNode&&function(){var version;return process.versions&&process.versions.node?version=process.versions.node.split(".").map(Number):process.version&&(version=process.version.split(".").map(Number)),0===version[0]&&version[1]>10||version[0]>0}(),ret.isNode&&ret.toFastProperties(process);try{throw new Error}catch(e){ret.lastLineError=e}module.exports=ret}).call(exports,function(){return this}(),__webpack_require__(2))},function(module,exports){var isES5=function(){"use strict";return void 0===this}();if(isES5)module.exports={freeze:Object.freeze,defineProperty:Object.defineProperty,getDescriptor:Object.getOwnPropertyDescriptor,keys:Object.keys,names:Object.getOwnPropertyNames,getPrototypeOf:Object.getPrototypeOf,isArray:Array.isArray,isES5:isES5,propertyIsWritable:function(obj,prop){var descriptor=Object.getOwnPropertyDescriptor(obj,prop);return!(descriptor&&!descriptor.writable&&!descriptor.set)}};else{var has={}.hasOwnProperty,str={}.toString,proto={}.constructor.prototype,ObjectKeys=function(o){var ret=[];for(var key in o)has.call(o,key)&&ret.push(key);return ret},ObjectGetDescriptor=function(o,key){return{value:o[key]}},ObjectDefineProperty=function(o,key,desc){return o[key]=desc.value,o},ObjectFreeze=function(obj){return obj},ObjectGetPrototypeOf=function(obj){try{return Object(obj).constructor.prototype}catch(e){return proto}},ArrayIsArray=function(obj){try{return"[object Array]"===str.call(obj)}catch(e){return!1}};module.exports={isArray:ArrayIsArray,keys:ObjectKeys,names:ObjectKeys,defineProperty:ObjectDefineProperty,getDescriptor:ObjectGetDescriptor,freeze:ObjectFreeze,getPrototypeOf:ObjectGetPrototypeOf,isES5:isES5,propertyIsWritable:function(){return!0}}}},function(module,exports,__webpack_require__){(function(process){"use strict";function Async(){this._customScheduler=!1,this._isTickUsed=!1,this._lateQueue=new Queue(16),this._normalQueue=new Queue(16),this._haveDrainedQueues=!1,this._trampolineEnabled=!0;var self=this;this.drainQueues=function(){self._drainQueues()},this._schedule=schedule}function AsyncInvokeLater(fn,receiver,arg){this._lateQueue.push(fn,receiver,arg),this._queueTick()}function AsyncInvoke(fn,receiver,arg){this._normalQueue.push(fn,receiver,arg),this._queueTick()}function AsyncSettlePromises(promise){this._normalQueue._pushOne(promise),this._queueTick()}function _drainQueue(queue){for(;queue.length()>0;)_drainQueueStep(queue)}function _drainQueueStep(queue){var fn=queue.shift();if("function"!=typeof fn)fn._settlePromises();else{var receiver=queue.shift(),arg=queue.shift();fn.call(receiver,arg)}}var firstLineError;try{throw new Error}catch(e){firstLineError=e}var schedule=__webpack_require__(8),Queue=__webpack_require__(11),util=__webpack_require__(5);Async.prototype.setScheduler=function(fn){var prev=this._schedule;return this._schedule=fn,this._customScheduler=!0,prev},Async.prototype.hasCustomScheduler=function(){return this._customScheduler},Async.prototype.enableTrampoline=function(){this._trampolineEnabled=!0},Async.prototype.disableTrampolineIfNecessary=function(){util.hasDevTools&&(this._trampolineEnabled=!1)},Async.prototype.haveItemsQueued=function(){return this._isTickUsed||this._haveDrainedQueues},Async.prototype.fatalError=function(e,isNode){isNode?(process.stderr.write("Fatal "+(e instanceof Error?e.stack:e)+"\n"),process.exit(2)):this.throwLater(e)},Async.prototype.throwLater=function(fn,arg){if(1===arguments.length&&(arg=fn,fn=function(){throw arg}),"undefined"!=typeof setTimeout)setTimeout(function(){fn(arg)},0);else try{this._schedule(function(){fn(arg)})}catch(e){throw new Error("No async scheduler available\n\n    See http://goo.gl/MqrFmX\n")}},util.hasDevTools?(Async.prototype.invokeLater=function(fn,receiver,arg){this._trampolineEnabled?AsyncInvokeLater.call(this,fn,receiver,arg):this._schedule(function(){setTimeout(function(){fn.call(receiver,arg)},100)})},Async.prototype.invoke=function(fn,receiver,arg){this._trampolineEnabled?AsyncInvoke.call(this,fn,receiver,arg):this._schedule(function(){fn.call(receiver,arg)})},Async.prototype.settlePromises=function(promise){this._trampolineEnabled?AsyncSettlePromises.call(this,promise):this._schedule(function(){promise._settlePromises()})}):(Async.prototype.invokeLater=AsyncInvokeLater,Async.prototype.invoke=AsyncInvoke,Async.prototype.settlePromises=AsyncSettlePromises),Async.prototype._drainQueues=function(){_drainQueue(this._normalQueue),this._reset(),this._haveDrainedQueues=!0,_drainQueue(this._lateQueue)},Async.prototype._queueTick=function(){this._isTickUsed||(this._isTickUsed=!0,this._schedule(this.drainQueues))},Async.prototype._reset=function(){this._isTickUsed=!1},module.exports=Async,module.exports.firstLineError=firstLineError}).call(exports,__webpack_require__(2))},function(module,exports,__webpack_require__){(function(global,process,setImmediate){"use strict";var schedule,util=__webpack_require__(5),noAsyncScheduler=function(){throw new Error("No async scheduler available\n\n    See http://goo.gl/MqrFmX\n")},NativePromise=util.getNativePromise();if(util.isNode&&"undefined"==typeof MutationObserver){var GlobalSetImmediate=global.setImmediate,ProcessNextTick=process.nextTick;schedule=util.isRecentNode?function(fn){GlobalSetImmediate.call(global,fn)}:function(fn){ProcessNextTick.call(process,fn)}}else if("function"==typeof NativePromise&&"function"==typeof NativePromise.resolve){var nativePromise=NativePromise.resolve();schedule=function(fn){nativePromise.then(fn)}}else schedule="undefined"==typeof MutationObserver||"undefined"!=typeof window&&window.navigator&&(window.navigator.standalone||window.cordova)?"undefined"!=typeof setImmediate?function(fn){setImmediate(fn)}:"undefined"!=typeof setTimeout?function(fn){setTimeout(fn,0)}:noAsyncScheduler:function(){var div=document.createElement("div"),opts={attributes:!0},toggleScheduled=!1,div2=document.createElement("div"),o2=new MutationObserver(function(){div.classList.toggle("foo"),toggleScheduled=!1});o2.observe(div2,opts);var scheduleToggle=function(){toggleScheduled||(toggleScheduled=!0,div2.classList.toggle("foo"))};return function(fn){var o=new MutationObserver(function(){o.disconnect(),fn()});o.observe(div,opts),scheduleToggle()}}();module.exports=schedule}).call(exports,function(){return this}(),__webpack_require__(2),__webpack_require__(9).setImmediate)},function(module,exports,__webpack_require__){(function(setImmediate,clearImmediate){function Timeout(id,clearFn){this._id=id,this._clearFn=clearFn}var nextTick=__webpack_require__(10).nextTick,apply=Function.prototype.apply,slice=Array.prototype.slice,immediateIds={},nextImmediateId=0;exports.setTimeout=function(){return new Timeout(apply.call(setTimeout,window,arguments),clearTimeout)},exports.setInterval=function(){return new Timeout(apply.call(setInterval,window,arguments),clearInterval)},exports.clearTimeout=exports.clearInterval=function(timeout){timeout.close()},Timeout.prototype.unref=Timeout.prototype.ref=function(){},Timeout.prototype.close=function(){this._clearFn.call(window,this._id)},exports.enroll=function(item,msecs){clearTimeout(item._idleTimeoutId),item._idleTimeout=msecs},exports.unenroll=function(item){clearTimeout(item._idleTimeoutId),item._idleTimeout=-1},exports._unrefActive=exports.active=function(item){clearTimeout(item._idleTimeoutId);var msecs=item._idleTimeout;msecs>=0&&(item._idleTimeoutId=setTimeout(function(){item._onTimeout&&item._onTimeout()},msecs))},exports.setImmediate="function"==typeof setImmediate?setImmediate:function(fn){var id=nextImmediateId++,args=!(arguments.length<2)&&slice.call(arguments,1);return immediateIds[id]=!0,nextTick(function(){immediateIds[id]&&(args?fn.apply(null,args):fn.call(null),exports.clearImmediate(id))}),id},exports.clearImmediate="function"==typeof clearImmediate?clearImmediate:function(id){delete immediateIds[id]}}).call(exports,__webpack_require__(9).setImmediate,__webpack_require__(9).clearImmediate)},2,function(module,exports){"use strict";function arrayMove(src,srcIndex,dst,dstIndex,len){for(var j=0;j<len;++j)dst[j+dstIndex]=src[j+srcIndex],src[j+srcIndex]=void 0}function Queue(capacity){this._capacity=capacity,this._length=0,this._front=0}Queue.prototype._willBeOverCapacity=function(size){return this._capacity<size},Queue.prototype._pushOne=function(arg){var length=this.length();this._checkCapacity(length+1);var i=this._front+length&this._capacity-1;this[i]=arg,this._length=length+1},Queue.prototype.push=function(fn,receiver,arg){var length=this.length()+3;if(this._willBeOverCapacity(length))return this._pushOne(fn),this._pushOne(receiver),void this._pushOne(arg);var j=this._front+length-3;this._checkCapacity(length);var wrapMask=this._capacity-1;this[j+0&wrapMask]=fn,this[j+1&wrapMask]=receiver,this[j+2&wrapMask]=arg,this._length=length},Queue.prototype.shift=function(){var front=this._front,ret=this[front];return this[front]=void 0,this._front=front+1&this._capacity-1,this._length--,ret},Queue.prototype.length=function(){return this._length},Queue.prototype._checkCapacity=function(size){this._capacity<size&&this._resizeTo(this._capacity<<1)},Queue.prototype._resizeTo=function(capacity){var oldCapacity=this._capacity;this._capacity=capacity;var front=this._front,length=this._length,moveItemsCount=front+length&oldCapacity-1;arrayMove(this,0,this,oldCapacity,moveItemsCount)},module.exports=Queue},function(module,exports,__webpack_require__){"use strict";function subError(nameProperty,defaultMessage){function SubError(message){return this instanceof SubError?(notEnumerableProp(this,"message","string"==typeof message?message:defaultMessage),notEnumerableProp(this,"name",nameProperty),void(Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):Error.call(this))):new SubError(message)}return inherits(SubError,Error),SubError}function OperationalError(message){return this instanceof OperationalError?(notEnumerableProp(this,"name","OperationalError"),notEnumerableProp(this,"message",message),this.cause=message,this.isOperational=!0,void(message instanceof Error?(notEnumerableProp(this,"message",message.message),notEnumerableProp(this,"stack",message.stack)):Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor))):new OperationalError(message)}var _TypeError,_RangeError,es5=__webpack_require__(6),Objectfreeze=es5.freeze,util=__webpack_require__(5),inherits=util.inherits,notEnumerableProp=util.notEnumerableProp,Warning=subError("Warning","warning"),CancellationError=subError("CancellationError","cancellation error"),TimeoutError=subError("TimeoutError","timeout error"),AggregateError=subError("AggregateError","aggregate error");try{_TypeError=TypeError,_RangeError=RangeError}catch(e){_TypeError=subError("TypeError","type error"),_RangeError=subError("RangeError","range error")}for(var methods="join pop push shift unshift slice filter forEach some every map indexOf lastIndexOf reduce reduceRight sort reverse".split(" "),i=0;i<methods.length;++i)"function"==typeof Array.prototype[methods[i]]&&(AggregateError.prototype[methods[i]]=Array.prototype[methods[i]]);es5.defineProperty(AggregateError.prototype,"length",{value:0,configurable:!1,writable:!0,enumerable:!0}),AggregateError.prototype.isOperational=!0;var level=0;AggregateError.prototype.toString=function(){var indent=Array(4*level+1).join(" "),ret="\n"+indent+"AggregateError of:\n";level++,indent=Array(4*level+1).join(" ");for(var i=0;i<this.length;++i){for(var str=this[i]===this?"[Circular AggregateError]":this[i]+"",lines=str.split("\n"),j=0;j<lines.length;++j)lines[j]=indent+lines[j];str=lines.join("\n"),ret+=str+"\n"}return level--,ret},inherits(OperationalError,Error);var errorTypes=Error.__BluebirdErrorTypes__;errorTypes||(errorTypes=Objectfreeze({CancellationError:CancellationError,TimeoutError:TimeoutError,OperationalError:OperationalError,RejectionError:OperationalError,AggregateError:AggregateError}),es5.defineProperty(Error,"__BluebirdErrorTypes__",{value:errorTypes,writable:!1,enumerable:!1,configurable:!1})),module.exports={Error:Error,TypeError:_TypeError,RangeError:_RangeError,CancellationError:errorTypes.CancellationError,OperationalError:errorTypes.OperationalError,TimeoutError:errorTypes.TimeoutError,AggregateError:errorTypes.AggregateError,Warning:Warning}},function(module,exports,__webpack_require__){"use strict";module.exports=function(Promise,INTERNAL){function tryConvertToPromise(obj,context){if(isObject(obj)){if(obj instanceof Promise)return obj;var then=getThen(obj);if(then===errorObj){context&&context._pushContext();var ret=Promise.reject(then.e);return context&&context._popContext(),ret}if("function"==typeof then){if(isAnyBluebirdPromise(obj)){var ret=new Promise(INTERNAL);return obj._then(ret._fulfill,ret._reject,void 0,ret,null),ret}return doThenable(obj,then,context)}}return obj}function doGetThen(obj){return obj.then}function getThen(obj){try{return doGetThen(obj)}catch(e){return errorObj.e=e,errorObj}}function isAnyBluebirdPromise(obj){try{return hasProp.call(obj,"_promise0")}catch(e){return!1}}function doThenable(x,then,context){function resolve(value){promise&&(promise._resolveCallback(value),promise=null)}function reject(reason){promise&&(promise._rejectCallback(reason,synchronous,!0),promise=null)}var promise=new Promise(INTERNAL),ret=promise;context&&context._pushContext(),promise._captureStackTrace(),context&&context._popContext();var synchronous=!0,result=util.tryCatch(then).call(x,resolve,reject);return synchronous=!1,promise&&result===errorObj&&(promise._rejectCallback(result.e,!0,!0),promise=null),ret}var util=__webpack_require__(5),errorObj=util.errorObj,isObject=util.isObject,hasProp={}.hasOwnProperty;return tryConvertToPromise}},function(module,exports,__webpack_require__){"use strict";module.exports=function(Promise,INTERNAL,tryConvertToPromise,apiRejection,Proxyable){function toResolutionValue(val){switch(val){case-2:return[];case-3:return{};case-6:return new Map}}function PromiseArray(values){var promise=this._promise=new Promise(INTERNAL);values instanceof Promise&&promise._propagateFrom(values,3),promise._setOnCancel(this),this._values=values,this._length=0,this._totalResolved=0,this._init(void 0,-2)}var util=__webpack_require__(5);util.isArray;return util.inherits(PromiseArray,Proxyable),PromiseArray.prototype.length=function(){return this._length},PromiseArray.prototype.promise=function(){return this._promise},PromiseArray.prototype._init=function init(_,resolveValueIfEmpty){var values=tryConvertToPromise(this._values,this._promise);if(values instanceof Promise){values=values._target();var bitField=values._bitField;if(this._values=values,0===(50397184&bitField))return this._promise._setAsyncGuaranteed(),values._then(init,this._reject,void 0,this,resolveValueIfEmpty);if(0===(33554432&bitField))return 0!==(16777216&bitField)?this._reject(values._reason()):this._cancel();values=values._value()}if(values=util.asArray(values),null===values){var err=apiRejection("expecting an array or an iterable object but got "+util.classString(values)).reason();return void this._promise._rejectCallback(err,!1)}return 0===values.length?void(resolveValueIfEmpty===-5?this._resolveEmptyArray():this._resolve(toResolutionValue(resolveValueIfEmpty))):void this._iterate(values)},PromiseArray.prototype._iterate=function(values){var len=this.getActualLength(values.length);this._length=len,this._values=this.shouldCopyValues()?new Array(len):this._values;for(var result=this._promise,isResolved=!1,bitField=null,i=0;i<len;++i){var maybePromise=tryConvertToPromise(values[i],result);maybePromise instanceof Promise?(maybePromise=maybePromise._target(),bitField=maybePromise._bitField):bitField=null,isResolved?null!==bitField&&maybePromise.suppressUnhandledRejections():null!==bitField?0===(50397184&bitField)?(maybePromise._proxy(this,i),this._values[i]=maybePromise):isResolved=0!==(33554432&bitField)?this._promiseFulfilled(maybePromise._value(),i):0!==(16777216&bitField)?this._promiseRejected(maybePromise._reason(),i):this._promiseCancelled(i):isResolved=this._promiseFulfilled(maybePromise,i)}isResolved||result._setAsyncGuaranteed()},PromiseArray.prototype._isResolved=function(){return null===this._values},PromiseArray.prototype._resolve=function(value){this._values=null,this._promise._fulfill(value)},PromiseArray.prototype._cancel=function(){!this._isResolved()&&this._promise._isCancellable()&&(this._values=null,this._promise._cancel())},PromiseArray.prototype._reject=function(reason){this._values=null,this._promise._rejectCallback(reason,!1)},PromiseArray.prototype._promiseFulfilled=function(value,index){this._values[index]=value;var totalResolved=++this._totalResolved;return totalResolved>=this._length&&(this._resolve(this._values),!0)},PromiseArray.prototype._promiseCancelled=function(){return this._cancel(),!0},PromiseArray.prototype._promiseRejected=function(reason){return this._totalResolved++,this._reject(reason),!0},PromiseArray.prototype._resultCancelled=function(){if(!this._isResolved()){var values=this._values;if(this._cancel(),values instanceof Promise)values.cancel();else for(var i=0;i<values.length;++i)values[i]instanceof Promise&&values[i].cancel()}},PromiseArray.prototype.shouldCopyValues=function(){return!0},PromiseArray.prototype.getActualLength=function(len){return len},PromiseArray}},function(module,exports){"use strict";module.exports=function(Promise){function Context(){this._trace=new Context.CapturedTrace(peekContext())}function createContext(){if(longStackTraces)return new Context}function peekContext(){var lastIndex=contextStack.length-1;if(lastIndex>=0)return contextStack[lastIndex]}var longStackTraces=!1,contextStack=[];return Promise.prototype._promiseCreated=function(){},Promise.prototype._pushContext=function(){},Promise.prototype._popContext=function(){return null},Promise._peekContext=Promise.prototype._peekContext=function(){},Context.prototype._pushContext=function(){void 0!==this._trace&&(this._trace._promiseCreated=null,contextStack.push(this._trace))},Context.prototype._popContext=function(){if(void 0!==this._trace){var trace=contextStack.pop(),ret=trace._promiseCreated;return trace._promiseCreated=null,ret}return null},Context.CapturedTrace=null,Context.create=createContext,Context.deactivateLongStackTraces=function(){},Context.activateLongStackTraces=function(){var Promise_pushContext=Promise.prototype._pushContext,Promise_popContext=Promise.prototype._popContext,Promise_PeekContext=Promise._peekContext,Promise_peekContext=Promise.prototype._peekContext,Promise_promiseCreated=Promise.prototype._promiseCreated;Context.deactivateLongStackTraces=function(){Promise.prototype._pushContext=Promise_pushContext,Promise.prototype._popContext=Promise_popContext,Promise._peekContext=Promise_PeekContext,Promise.prototype._peekContext=Promise_peekContext,Promise.prototype._promiseCreated=Promise_promiseCreated,longStackTraces=!1},longStackTraces=!0,Promise.prototype._pushContext=Context.prototype._pushContext,Promise.prototype._popContext=Context.prototype._popContext,Promise._peekContext=Promise.prototype._peekContext=peekContext,Promise.prototype._promiseCreated=function(){var ctx=this._peekContext();ctx&&null==ctx._promiseCreated&&(ctx._promiseCreated=this)}},Context}},function(module,exports,__webpack_require__){(function(process){"use strict";module.exports=function(Promise,Context){function generatePromiseLifecycleEventObject(name,promise){return{promise:promise}}function defaultFireEvent(){return!1}function cancellationExecute(executor,resolve,reject){var promise=this;try{executor(resolve,reject,function(onCancel){if("function"!=typeof onCancel)throw new TypeError("onCancel must be a function, got: "+util.toString(onCancel));promise._attachCancellationCallback(onCancel)})}catch(e){return e}}function cancellationAttachCancellationCallback(onCancel){if(!this._isCancellable())return this;var previousOnCancel=this._onCancel();void 0!==previousOnCancel?util.isArray(previousOnCancel)?previousOnCancel.push(onCancel):this._setOnCancel([previousOnCancel,onCancel]):this._setOnCancel(onCancel)}function cancellationOnCancel(){return this._onCancelField}function cancellationSetOnCancel(onCancel){this._onCancelField=onCancel}function cancellationClearCancellationData(){this._cancellationParent=void 0,this._onCancelField=void 0}function cancellationPropagateFrom(parent,flags){if(0!==(1&flags)){this._cancellationParent=parent;var branchesRemainingToCancel=parent._branchesRemainingToCancel;void 0===branchesRemainingToCancel&&(branchesRemainingToCancel=0),parent._branchesRemainingToCancel=branchesRemainingToCancel+1}0!==(2&flags)&&parent._isBound()&&this._setBoundTo(parent._boundTo)}function bindingPropagateFrom(parent,flags){0!==(2&flags)&&parent._isBound()&&this._setBoundTo(parent._boundTo)}function boundValueFunction(){var ret=this._boundTo;return void 0!==ret&&ret instanceof Promise?ret.isFulfilled()?ret.value():void 0:ret}function longStackTracesCaptureStackTrace(){this._trace=new CapturedTrace(this._peekContext())}function longStackTracesAttachExtraTrace(error,ignoreSelf){if(canAttachTrace(error)){var trace=this._trace;if(void 0!==trace&&ignoreSelf&&(trace=trace._parent),void 0!==trace)trace.attachExtraTrace(error);else if(!error.__stackCleaned__){var parsed=parseStackAndMessage(error);util.notEnumerableProp(error,"stack",parsed.message+"\n"+parsed.stack.join("\n")),util.notEnumerableProp(error,"__stackCleaned__",!0)}}}function longStackTracesDereferenceTrace(){this._trace=void 0}function checkForgottenReturns(returnValue,promiseCreated,name,promise,parent){if(void 0===returnValue&&null!==promiseCreated&&wForgottenReturn){if(void 0!==parent&&parent._returnedNonUndefined())return;if(0===(65535&promise._bitField))return;name&&(name+=" ");var handlerLine="",creatorLine="";if(promiseCreated._trace){for(var traceLines=promiseCreated._trace.stack.split("\n"),stack=cleanStack(traceLines),i=stack.length-1;i>=0;--i){var line=stack[i];if(!nodeFramePattern.test(line)){var lineMatches=line.match(parseLinePattern);lineMatches&&(handlerLine="at "+lineMatches[1]+":"+lineMatches[2]+":"+lineMatches[3]+" ");break}}if(stack.length>0)for(var firstUserLine=stack[0],i=0;i<traceLines.length;++i)if(traceLines[i]===firstUserLine){i>0&&(creatorLine="\n"+traceLines[i-1]);break}}var msg="a promise was created in a "+name+"handler "+handlerLine+"but was not returned from it, see http://goo.gl/rRqMUw"+creatorLine;promise._warn(msg,!0,promiseCreated)}}function deprecated(name,replacement){var message=name+" is deprecated and will be removed in a future version.";return replacement&&(message+=" Use "+replacement+" instead."),warn(message)}function warn(message,shouldUseOwnTrace,promise){if(config.warnings){var ctx,warning=new Warning(message);if(shouldUseOwnTrace)promise._attachExtraTrace(warning);else if(config.longStackTraces&&(ctx=Promise._peekContext()))ctx.attachExtraTrace(warning);else{var parsed=parseStackAndMessage(warning);warning.stack=parsed.message+"\n"+parsed.stack.join("\n")}activeFireEvent("warning",warning)||formatAndLogError(warning,"",!0)}}function reconstructStack(message,stacks){for(var i=0;i<stacks.length-1;++i)stacks[i].push("From previous event:"),stacks[i]=stacks[i].join("\n");return i<stacks.length&&(stacks[i]=stacks[i].join("\n")),message+"\n"+stacks.join("\n")}function removeDuplicateOrEmptyJumps(stacks){for(var i=0;i<stacks.length;++i)(0===stacks[i].length||i+1<stacks.length&&stacks[i][0]===stacks[i+1][0])&&(stacks.splice(i,1),i--)}function removeCommonRoots(stacks){for(var current=stacks[0],i=1;i<stacks.length;++i){for(var prev=stacks[i],currentLastIndex=current.length-1,currentLastLine=current[currentLastIndex],commonRootMeetPoint=-1,j=prev.length-1;j>=0;--j)if(prev[j]===currentLastLine){commonRootMeetPoint=j;break}for(var j=commonRootMeetPoint;j>=0;--j){var line=prev[j];if(current[currentLastIndex]!==line)break;current.pop(),currentLastIndex--}current=prev}}function cleanStack(stack){for(var ret=[],i=0;i<stack.length;++i){var line=stack[i],isTraceLine="    (No stack trace)"===line||stackFramePattern.test(line),isInternalFrame=isTraceLine&&shouldIgnore(line);isTraceLine&&!isInternalFrame&&(indentStackFrames&&" "!==line.charAt(0)&&(line="    "+line),ret.push(line))}return ret}function stackFramesAsArray(error){for(var stack=error.stack.replace(/\s+$/g,"").split("\n"),i=0;i<stack.length;++i){var line=stack[i];if("    (No stack trace)"===line||stackFramePattern.test(line))break}return i>0&&"SyntaxError"!=error.name&&(stack=stack.slice(i)),stack}function parseStackAndMessage(error){var stack=error.stack,message=error.toString();return stack="string"==typeof stack&&stack.length>0?stackFramesAsArray(error):["    (No stack trace)"],{message:message,stack:"SyntaxError"==error.name?stack:cleanStack(stack)}}function formatAndLogError(error,title,isSoft){if("undefined"!=typeof console){var message;if(util.isObject(error)){var stack=error.stack;message=title+formatStack(stack,error)}else message=title+String(error);"function"==typeof printWarning?printWarning(message,isSoft):"function"!=typeof console.log&&"object"!=typeof console.log||console.log(message)}}function fireRejectionEvent(name,localHandler,reason,promise){var localEventFired=!1;try{"function"==typeof localHandler&&(localEventFired=!0,"rejectionHandled"===name?localHandler(promise):localHandler(reason,promise))}catch(e){async.throwLater(e)}"unhandledRejection"===name?activeFireEvent(name,reason,promise)||localEventFired||formatAndLogError(reason,"Unhandled rejection "):activeFireEvent(name,promise)}function formatNonError(obj){var str;if("function"==typeof obj)str="[function "+(obj.name||"anonymous")+"]";else{str=obj&&"function"==typeof obj.toString?obj.toString():util.toString(obj);var ruselessToString=/\[object [a-zA-Z0-9$_]+\]/;if(ruselessToString.test(str))try{var newStr=JSON.stringify(obj);str=newStr}catch(e){}0===str.length&&(str="(empty array)")}return"(<"+snip(str)+">, no stack trace)"}function snip(str){var maxChars=41;return str.length<maxChars?str:str.substr(0,maxChars-3)+"..."}function longStackTracesIsSupported(){return"function"==typeof captureStackTrace}function parseLineInfo(line){var matches=line.match(parseLineInfoRegex);if(matches)return{fileName:matches[1],line:parseInt(matches[2],10)}}function setBounds(firstLineError,lastLineError){if(longStackTracesIsSupported()){for(var firstFileName,lastFileName,firstStackLines=firstLineError.stack.split("\n"),lastStackLines=lastLineError.stack.split("\n"),firstIndex=-1,lastIndex=-1,i=0;i<firstStackLines.length;++i){var result=parseLineInfo(firstStackLines[i]);if(result){firstFileName=result.fileName,firstIndex=result.line;break}}for(var i=0;i<lastStackLines.length;++i){var result=parseLineInfo(lastStackLines[i]);if(result){lastFileName=result.fileName,lastIndex=result.line;break}}firstIndex<0||lastIndex<0||!firstFileName||!lastFileName||firstFileName!==lastFileName||firstIndex>=lastIndex||(shouldIgnore=function(line){if(bluebirdFramePattern.test(line))return!0;var info=parseLineInfo(line);return!!(info&&info.fileName===firstFileName&&firstIndex<=info.line&&info.line<=lastIndex)})}}function CapturedTrace(parent){this._parent=parent,this._promisesCreated=0;var length=this._length=1+(void 0===parent?0:parent._length);captureStackTrace(this,CapturedTrace),length>32&&this.uncycle()}var unhandledRejectionHandled,possiblyUnhandledRejection,printWarning,getDomain=Promise._getDomain,async=Promise._async,Warning=__webpack_require__(12).Warning,util=__webpack_require__(5),es5=__webpack_require__(6),canAttachTrace=util.canAttachTrace,bluebirdFramePattern=/[\\\/]bluebird[\\\/]js[\\\/](release|debug|instrumented)/,nodeFramePattern=/\((?:timers\.js):\d+:\d+\)/,parseLinePattern=/[\/<\(](.+?):(\d+):(\d+)\)?\s*$/,stackFramePattern=null,formatStack=null,indentStackFrames=!1,debugging=!(0==util.env("BLUEBIRD_DEBUG")||!util.env("BLUEBIRD_DEBUG")&&"development"!==util.env("NODE_ENV")),warnings=!(0==util.env("BLUEBIRD_WARNINGS")||!debugging&&!util.env("BLUEBIRD_WARNINGS")),longStackTraces=!(0==util.env("BLUEBIRD_LONG_STACK_TRACES")||!debugging&&!util.env("BLUEBIRD_LONG_STACK_TRACES")),wForgottenReturn=0!=util.env("BLUEBIRD_W_FORGOTTEN_RETURN")&&(warnings||!!util.env("BLUEBIRD_W_FORGOTTEN_RETURN"));Promise.prototype.suppressUnhandledRejections=function(){var target=this._target();target._bitField=target._bitField&-1048577|524288},Promise.prototype._ensurePossibleRejectionHandled=function(){if(0===(524288&this._bitField)){this._setRejectionIsUnhandled();var self=this;setTimeout(function(){self._notifyUnhandledRejection()},1)}},Promise.prototype._notifyUnhandledRejectionIsHandled=function(){fireRejectionEvent("rejectionHandled",unhandledRejectionHandled,void 0,this)},Promise.prototype._setReturnedNonUndefined=function(){this._bitField=268435456|this._bitField},Promise.prototype._returnedNonUndefined=function(){return 0!==(268435456&this._bitField)},Promise.prototype._notifyUnhandledRejection=function(){if(this._isRejectionUnhandled()){var reason=this._settledValue();this._setUnhandledRejectionIsNotified(),fireRejectionEvent("unhandledRejection",possiblyUnhandledRejection,reason,this)}},Promise.prototype._setUnhandledRejectionIsNotified=function(){this._bitField=262144|this._bitField},Promise.prototype._unsetUnhandledRejectionIsNotified=function(){this._bitField=this._bitField&-262145},Promise.prototype._isUnhandledRejectionNotified=function(){return(262144&this._bitField)>0},Promise.prototype._setRejectionIsUnhandled=function(){this._bitField=1048576|this._bitField},Promise.prototype._unsetRejectionIsUnhandled=function(){this._bitField=this._bitField&-1048577,this._isUnhandledRejectionNotified()&&(this._unsetUnhandledRejectionIsNotified(),this._notifyUnhandledRejectionIsHandled())},Promise.prototype._isRejectionUnhandled=function(){return(1048576&this._bitField)>0},Promise.prototype._warn=function(message,shouldUseOwnTrace,promise){return warn(message,shouldUseOwnTrace,promise||this)},Promise.onPossiblyUnhandledRejection=function(fn){var domain=getDomain();possiblyUnhandledRejection="function"==typeof fn?null===domain?fn:util.domainBind(domain,fn):void 0},Promise.onUnhandledRejectionHandled=function(fn){var domain=getDomain();unhandledRejectionHandled="function"==typeof fn?null===domain?fn:util.domainBind(domain,fn):void 0};var disableLongStackTraces=function(){};Promise.longStackTraces=function(){if(async.haveItemsQueued()&&!config.longStackTraces)throw new Error("cannot enable long stack traces after promises have been created\n\n    See http://goo.gl/MqrFmX\n");if(!config.longStackTraces&&longStackTracesIsSupported()){var Promise_captureStackTrace=Promise.prototype._captureStackTrace,Promise_attachExtraTrace=Promise.prototype._attachExtraTrace,Promise_dereferenceTrace=Promise.prototype._dereferenceTrace;config.longStackTraces=!0,disableLongStackTraces=function(){if(async.haveItemsQueued()&&!config.longStackTraces)throw new Error("cannot enable long stack traces after promises have been created\n\n    See http://goo.gl/MqrFmX\n");Promise.prototype._captureStackTrace=Promise_captureStackTrace,Promise.prototype._attachExtraTrace=Promise_attachExtraTrace,Promise.prototype._dereferenceTrace=Promise_dereferenceTrace,Context.deactivateLongStackTraces(),async.enableTrampoline(),config.longStackTraces=!1},Promise.prototype._captureStackTrace=longStackTracesCaptureStackTrace,
Promise.prototype._attachExtraTrace=longStackTracesAttachExtraTrace,Promise.prototype._dereferenceTrace=longStackTracesDereferenceTrace,Context.activateLongStackTraces(),async.disableTrampolineIfNecessary()}},Promise.hasLongStackTraces=function(){return config.longStackTraces&&longStackTracesIsSupported()};var fireDomEvent=function(){try{if("function"==typeof CustomEvent){var event=new CustomEvent("CustomEvent");return util.global.dispatchEvent(event),function(name,event){var eventData={detail:event,cancelable:!0};es5.defineProperty(eventData,"promise",{value:event.promise}),es5.defineProperty(eventData,"reason",{value:event.reason});var domEvent=new CustomEvent(name.toLowerCase(),eventData);return!util.global.dispatchEvent(domEvent)}}if("function"==typeof Event){var event=new Event("CustomEvent");return util.global.dispatchEvent(event),function(name,event){var domEvent=new Event(name.toLowerCase(),{cancelable:!0});return domEvent.detail=event,es5.defineProperty(domEvent,"promise",{value:event.promise}),es5.defineProperty(domEvent,"reason",{value:event.reason}),!util.global.dispatchEvent(domEvent)}}var event=document.createEvent("CustomEvent");return event.initCustomEvent("testingtheevent",!1,!0,{}),util.global.dispatchEvent(event),function(name,event){var domEvent=document.createEvent("CustomEvent");return domEvent.initCustomEvent(name.toLowerCase(),!1,!0,event),!util.global.dispatchEvent(domEvent)}}catch(e){}return function(){return!1}}(),fireGlobalEvent=function(){return util.isNode?function(){return process.emit.apply(process,arguments)}:util.global?function(name){var methodName="on"+name.toLowerCase(),method=util.global[methodName];return!!method&&(method.apply(util.global,[].slice.call(arguments,1)),!0)}:function(){return!1}}(),eventToObjectGenerator={promiseCreated:generatePromiseLifecycleEventObject,promiseFulfilled:generatePromiseLifecycleEventObject,promiseRejected:generatePromiseLifecycleEventObject,promiseResolved:generatePromiseLifecycleEventObject,promiseCancelled:generatePromiseLifecycleEventObject,promiseChained:function(name,promise,child){return{promise:promise,child:child}},warning:function(name,warning){return{warning:warning}},unhandledRejection:function(name,reason,promise){return{reason:reason,promise:promise}},rejectionHandled:generatePromiseLifecycleEventObject},activeFireEvent=function(name){var globalEventFired=!1;try{globalEventFired=fireGlobalEvent.apply(null,arguments)}catch(e){async.throwLater(e),globalEventFired=!0}var domEventFired=!1;try{domEventFired=fireDomEvent(name,eventToObjectGenerator[name].apply(null,arguments))}catch(e){async.throwLater(e),domEventFired=!0}return domEventFired||globalEventFired};Promise.config=function(opts){if(opts=Object(opts),"longStackTraces"in opts&&(opts.longStackTraces?Promise.longStackTraces():!opts.longStackTraces&&Promise.hasLongStackTraces()&&disableLongStackTraces()),"warnings"in opts){var warningsOption=opts.warnings;config.warnings=!!warningsOption,wForgottenReturn=config.warnings,util.isObject(warningsOption)&&"wForgottenReturn"in warningsOption&&(wForgottenReturn=!!warningsOption.wForgottenReturn)}if("cancellation"in opts&&opts.cancellation&&!config.cancellation){if(async.haveItemsQueued())throw new Error("cannot enable cancellation after promises are in use");Promise.prototype._clearCancellationData=cancellationClearCancellationData,Promise.prototype._propagateFrom=cancellationPropagateFrom,Promise.prototype._onCancel=cancellationOnCancel,Promise.prototype._setOnCancel=cancellationSetOnCancel,Promise.prototype._attachCancellationCallback=cancellationAttachCancellationCallback,Promise.prototype._execute=cancellationExecute,propagateFromFunction=cancellationPropagateFrom,config.cancellation=!0}return"monitoring"in opts&&(opts.monitoring&&!config.monitoring?(config.monitoring=!0,Promise.prototype._fireEvent=activeFireEvent):!opts.monitoring&&config.monitoring&&(config.monitoring=!1,Promise.prototype._fireEvent=defaultFireEvent)),Promise},Promise.prototype._fireEvent=defaultFireEvent,Promise.prototype._execute=function(executor,resolve,reject){try{executor(resolve,reject)}catch(e){return e}},Promise.prototype._onCancel=function(){},Promise.prototype._setOnCancel=function(handler){},Promise.prototype._attachCancellationCallback=function(onCancel){},Promise.prototype._captureStackTrace=function(){},Promise.prototype._attachExtraTrace=function(){},Promise.prototype._dereferenceTrace=function(){},Promise.prototype._clearCancellationData=function(){},Promise.prototype._propagateFrom=function(parent,flags){};var propagateFromFunction=bindingPropagateFrom,shouldIgnore=function(){return!1},parseLineInfoRegex=/[\/<\(]([^:\/]+):(\d+):(?:\d+)\)?\s*$/;util.inherits(CapturedTrace,Error),Context.CapturedTrace=CapturedTrace,CapturedTrace.prototype.uncycle=function(){var length=this._length;if(!(length<2)){for(var nodes=[],stackToIndex={},i=0,node=this;void 0!==node;++i)nodes.push(node),node=node._parent;length=this._length=i;for(var i=length-1;i>=0;--i){var stack=nodes[i].stack;void 0===stackToIndex[stack]&&(stackToIndex[stack]=i)}for(var i=0;i<length;++i){var currentStack=nodes[i].stack,index=stackToIndex[currentStack];if(void 0!==index&&index!==i){index>0&&(nodes[index-1]._parent=void 0,nodes[index-1]._length=1),nodes[i]._parent=void 0,nodes[i]._length=1;var cycleEdgeNode=i>0?nodes[i-1]:this;index<length-1?(cycleEdgeNode._parent=nodes[index+1],cycleEdgeNode._parent.uncycle(),cycleEdgeNode._length=cycleEdgeNode._parent._length+1):(cycleEdgeNode._parent=void 0,cycleEdgeNode._length=1);for(var currentChildLength=cycleEdgeNode._length+1,j=i-2;j>=0;--j)nodes[j]._length=currentChildLength,currentChildLength++;return}}}},CapturedTrace.prototype.attachExtraTrace=function(error){if(!error.__stackCleaned__){this.uncycle();for(var parsed=parseStackAndMessage(error),message=parsed.message,stacks=[parsed.stack],trace=this;void 0!==trace;)stacks.push(cleanStack(trace.stack.split("\n"))),trace=trace._parent;removeCommonRoots(stacks),removeDuplicateOrEmptyJumps(stacks),util.notEnumerableProp(error,"stack",reconstructStack(message,stacks)),util.notEnumerableProp(error,"__stackCleaned__",!0)}};var captureStackTrace=function(){var v8stackFramePattern=/^\s*at\s*/,v8stackFormatter=function(stack,error){return"string"==typeof stack?stack:void 0!==error.name&&void 0!==error.message?error.toString():formatNonError(error)};if("number"==typeof Error.stackTraceLimit&&"function"==typeof Error.captureStackTrace){Error.stackTraceLimit+=6,stackFramePattern=v8stackFramePattern,formatStack=v8stackFormatter;var captureStackTrace=Error.captureStackTrace;return shouldIgnore=function(line){return bluebirdFramePattern.test(line)},function(receiver,ignoreUntil){Error.stackTraceLimit+=6,captureStackTrace(receiver,ignoreUntil),Error.stackTraceLimit-=6}}var err=new Error;if("string"==typeof err.stack&&err.stack.split("\n")[0].indexOf("stackDetection@")>=0)return stackFramePattern=/@/,formatStack=v8stackFormatter,indentStackFrames=!0,function(o){o.stack=(new Error).stack};var hasStackAfterThrow;try{throw new Error}catch(e){hasStackAfterThrow="stack"in e}return"stack"in err||!hasStackAfterThrow||"number"!=typeof Error.stackTraceLimit?(formatStack=function(stack,error){return"string"==typeof stack?stack:"object"!=typeof error&&"function"!=typeof error||void 0===error.name||void 0===error.message?formatNonError(error):error.toString()},null):(stackFramePattern=v8stackFramePattern,formatStack=v8stackFormatter,function(o){Error.stackTraceLimit+=6;try{throw new Error}catch(e){o.stack=e.stack}Error.stackTraceLimit-=6})}([]);"undefined"!=typeof console&&"undefined"!=typeof console.warn&&(printWarning=function(message){console.warn(message)},util.isNode&&process.stderr.isTTY?printWarning=function(message,isSoft){var color=isSoft?"[33m":"[31m";console.warn(color+message+"[0m\n")}:util.isNode||"string"!=typeof(new Error).stack||(printWarning=function(message,isSoft){console.warn("%c"+message,isSoft?"color: darkorange":"color: red")}));var config={warnings:warnings,longStackTraces:!1,cancellation:!1,monitoring:!1};return longStackTraces&&Promise.longStackTraces(),{longStackTraces:function(){return config.longStackTraces},warnings:function(){return config.warnings},cancellation:function(){return config.cancellation},monitoring:function(){return config.monitoring},propagateFromFunction:function(){return propagateFromFunction},boundValueFunction:function(){return boundValueFunction},checkForgottenReturns:checkForgottenReturns,setBounds:setBounds,warn:warn,deprecated:deprecated,CapturedTrace:CapturedTrace,fireDomEvent:fireDomEvent,fireGlobalEvent:fireGlobalEvent}}}).call(exports,__webpack_require__(2))},function(module,exports,__webpack_require__){"use strict";module.exports=function(Promise,tryConvertToPromise,NEXT_FILTER){function PassThroughHandlerContext(promise,type,handler){this.promise=promise,this.type=type,this.handler=handler,this.called=!1,this.cancelPromise=null}function FinallyHandlerCancelReaction(finallyHandler){this.finallyHandler=finallyHandler}function checkCancel(ctx,reason){return null!=ctx.cancelPromise&&(arguments.length>1?ctx.cancelPromise._reject(reason):ctx.cancelPromise._cancel(),ctx.cancelPromise=null,!0)}function succeed(){return finallyHandler.call(this,this.promise._target()._settledValue())}function fail(reason){if(!checkCancel(this,reason))return errorObj.e=reason,errorObj}function finallyHandler(reasonOrValue){var promise=this.promise,handler=this.handler;if(!this.called){this.called=!0;var ret=this.isFinallyHandler()?handler.call(promise._boundValue()):handler.call(promise._boundValue(),reasonOrValue);if(ret===NEXT_FILTER)return ret;if(void 0!==ret){promise._setReturnedNonUndefined();var maybePromise=tryConvertToPromise(ret,promise);if(maybePromise instanceof Promise){if(null!=this.cancelPromise){if(maybePromise._isCancelled()){var reason=new CancellationError("late cancellation observer");return promise._attachExtraTrace(reason),errorObj.e=reason,errorObj}maybePromise.isPending()&&maybePromise._attachCancellationCallback(new FinallyHandlerCancelReaction(this))}return maybePromise._then(succeed,fail,void 0,this,void 0)}}}return promise.isRejected()?(checkCancel(this),errorObj.e=reasonOrValue,errorObj):(checkCancel(this),reasonOrValue)}var util=__webpack_require__(5),CancellationError=Promise.CancellationError,errorObj=util.errorObj,catchFilter=__webpack_require__(18)(NEXT_FILTER);return PassThroughHandlerContext.prototype.isFinallyHandler=function(){return 0===this.type},FinallyHandlerCancelReaction.prototype._resultCancelled=function(){checkCancel(this.finallyHandler)},Promise.prototype._passThrough=function(handler,type,success,fail){return"function"!=typeof handler?this.then():this._then(success,fail,void 0,new PassThroughHandlerContext(this,type,handler),void 0)},Promise.prototype.lastly=Promise.prototype.finally=function(handler){return this._passThrough(handler,0,finallyHandler,finallyHandler)},Promise.prototype.tap=function(handler){return this._passThrough(handler,1,finallyHandler)},Promise.prototype.tapCatch=function(handlerOrPredicate){var len=arguments.length;if(1===len)return this._passThrough(handlerOrPredicate,1,void 0,finallyHandler);var i,catchInstances=new Array(len-1),j=0;for(i=0;i<len-1;++i){var item=arguments[i];if(!util.isObject(item))return Promise.reject(new TypeError("tapCatch statement predicate: expecting an object but got "+util.classString(item)));catchInstances[j++]=item}catchInstances.length=j;var handler=arguments[i];return this._passThrough(catchFilter(catchInstances,handler,this),1,void 0,finallyHandler)},PassThroughHandlerContext}},function(module,exports,__webpack_require__){"use strict";module.exports=function(NEXT_FILTER){function catchFilter(instances,cb,promise){return function(e){var boundTo=promise._boundValue();predicateLoop:for(var i=0;i<instances.length;++i){var item=instances[i];if(item===Error||null!=item&&item.prototype instanceof Error){if(e instanceof item)return tryCatch(cb).call(boundTo,e)}else if("function"==typeof item){var matchesPredicate=tryCatch(item).call(boundTo,e);if(matchesPredicate===errorObj)return matchesPredicate;if(matchesPredicate)return tryCatch(cb).call(boundTo,e)}else if(util.isObject(e)){for(var keys=getKeys(item),j=0;j<keys.length;++j){var key=keys[j];if(item[key]!=e[key])continue predicateLoop}return tryCatch(cb).call(boundTo,e)}}return NEXT_FILTER}}var util=__webpack_require__(5),getKeys=__webpack_require__(6).keys,tryCatch=util.tryCatch,errorObj=util.errorObj;return catchFilter}},function(module,exports,__webpack_require__){"use strict";function isUntypedError(obj){return obj instanceof Error&&es5.getPrototypeOf(obj)===Error.prototype}function wrapAsOperationalError(obj){var ret;if(isUntypedError(obj)){ret=new OperationalError(obj),ret.name=obj.name,ret.message=obj.message,ret.stack=obj.stack;for(var keys=es5.keys(obj),i=0;i<keys.length;++i){var key=keys[i];rErrorKey.test(key)||(ret[key]=obj[key])}return ret}return util.markAsOriginatingFromRejection(obj),obj}function nodebackForPromise(promise,multiArgs){return function(err,value){if(null!==promise){if(err){var wrapped=wrapAsOperationalError(maybeWrapAsError(err));promise._attachExtraTrace(wrapped),promise._reject(wrapped)}else if(multiArgs){for(var $_len=arguments.length,args=new Array(Math.max($_len-1,0)),$_i=1;$_i<$_len;++$_i)args[$_i-1]=arguments[$_i];promise._fulfill(args)}else promise._fulfill(value);promise=null}}}var util=__webpack_require__(5),maybeWrapAsError=util.maybeWrapAsError,errors=__webpack_require__(12),OperationalError=errors.OperationalError,es5=__webpack_require__(6),rErrorKey=/^(?:name|message|stack|cause)$/;module.exports=nodebackForPromise},function(module,exports,__webpack_require__){"use strict";module.exports=function(Promise,INTERNAL,tryConvertToPromise,apiRejection,debug){var util=__webpack_require__(5),tryCatch=util.tryCatch;Promise.method=function(fn){if("function"!=typeof fn)throw new Promise.TypeError("expecting a function but got "+util.classString(fn));return function(){var ret=new Promise(INTERNAL);ret._captureStackTrace(),ret._pushContext();var value=tryCatch(fn).apply(this,arguments),promiseCreated=ret._popContext();return debug.checkForgottenReturns(value,promiseCreated,"Promise.method",ret),ret._resolveFromSyncValue(value),ret}},Promise.attempt=Promise.try=function(fn){if("function"!=typeof fn)return apiRejection("expecting a function but got "+util.classString(fn));var ret=new Promise(INTERNAL);ret._captureStackTrace(),ret._pushContext();var value;if(arguments.length>1){debug.deprecated("calling Promise.try with more than 1 argument");var arg=arguments[1],ctx=arguments[2];value=util.isArray(arg)?tryCatch(fn).apply(ctx,arg):tryCatch(fn).call(ctx,arg)}else value=tryCatch(fn)();var promiseCreated=ret._popContext();return debug.checkForgottenReturns(value,promiseCreated,"Promise.try",ret),ret._resolveFromSyncValue(value),ret},Promise.prototype._resolveFromSyncValue=function(value){value===util.errorObj?this._rejectCallback(value.e,!1):this._resolveCallback(value,!0)}}},function(module,exports){"use strict";module.exports=function(Promise,INTERNAL,tryConvertToPromise,debug){var calledBind=!1,rejectThis=function(_,e){this._reject(e)},targetRejected=function(e,context){context.promiseRejectionQueued=!0,context.bindingPromise._then(rejectThis,rejectThis,null,this,e)},bindingResolved=function(thisArg,context){0===(50397184&this._bitField)&&this._resolveCallback(context.target)},bindingRejected=function(e,context){context.promiseRejectionQueued||this._reject(e)};Promise.prototype.bind=function(thisArg){calledBind||(calledBind=!0,Promise.prototype._propagateFrom=debug.propagateFromFunction(),Promise.prototype._boundValue=debug.boundValueFunction());var maybePromise=tryConvertToPromise(thisArg),ret=new Promise(INTERNAL);ret._propagateFrom(this,1);var target=this._target();if(ret._setBoundTo(maybePromise),maybePromise instanceof Promise){var context={promiseRejectionQueued:!1,promise:ret,target:target,bindingPromise:maybePromise};target._then(INTERNAL,targetRejected,void 0,ret,context),maybePromise._then(bindingResolved,bindingRejected,void 0,ret,context),ret._setOnCancel(maybePromise)}else ret._resolveCallback(target);return ret},Promise.prototype._setBoundTo=function(obj){void 0!==obj?(this._bitField=2097152|this._bitField,this._boundTo=obj):this._bitField=this._bitField&-2097153},Promise.prototype._isBound=function(){return 2097152===(2097152&this._bitField)},Promise.bind=function(thisArg,value){return Promise.resolve(value).bind(thisArg)}}},function(module,exports,__webpack_require__){"use strict";module.exports=function(Promise,PromiseArray,apiRejection,debug){var util=__webpack_require__(5),tryCatch=util.tryCatch,errorObj=util.errorObj,async=Promise._async;Promise.prototype.break=Promise.prototype.cancel=function(){if(!debug.cancellation())return this._warn("cancellation is disabled");for(var promise=this,child=promise;promise._isCancellable();){if(!promise._cancelBy(child)){child._isFollowing()?child._followee().cancel():child._cancelBranched();break}var parent=promise._cancellationParent;if(null==parent||!parent._isCancellable()){promise._isFollowing()?promise._followee().cancel():promise._cancelBranched();break}promise._isFollowing()&&promise._followee().cancel(),promise._setWillBeCancelled(),child=promise,promise=parent}},Promise.prototype._branchHasCancelled=function(){this._branchesRemainingToCancel--},Promise.prototype._enoughBranchesHaveCancelled=function(){return void 0===this._branchesRemainingToCancel||this._branchesRemainingToCancel<=0},Promise.prototype._cancelBy=function(canceller){return canceller===this?(this._branchesRemainingToCancel=0,this._invokeOnCancel(),!0):(this._branchHasCancelled(),!!this._enoughBranchesHaveCancelled()&&(this._invokeOnCancel(),!0))},Promise.prototype._cancelBranched=function(){this._enoughBranchesHaveCancelled()&&this._cancel()},Promise.prototype._cancel=function(){this._isCancellable()&&(this._setCancelled(),async.invoke(this._cancelPromises,this,void 0))},Promise.prototype._cancelPromises=function(){this._length()>0&&this._settlePromises()},Promise.prototype._unsetOnCancel=function(){this._onCancelField=void 0},Promise.prototype._isCancellable=function(){return this.isPending()&&!this._isCancelled()},Promise.prototype.isCancellable=function(){return this.isPending()&&!this.isCancelled()},Promise.prototype._doInvokeOnCancel=function(onCancelCallback,internalOnly){if(util.isArray(onCancelCallback))for(var i=0;i<onCancelCallback.length;++i)this._doInvokeOnCancel(onCancelCallback[i],internalOnly);else if(void 0!==onCancelCallback)if("function"==typeof onCancelCallback){if(!internalOnly){var e=tryCatch(onCancelCallback).call(this._boundValue());e===errorObj&&(this._attachExtraTrace(e.e),async.throwLater(e.e))}}else onCancelCallback._resultCancelled(this)},Promise.prototype._invokeOnCancel=function(){var onCancelCallback=this._onCancel();this._unsetOnCancel(),async.invoke(this._doInvokeOnCancel,this,onCancelCallback)},Promise.prototype._invokeInternalOnCancel=function(){this._isCancellable()&&(this._doInvokeOnCancel(this._onCancel(),!0),this._unsetOnCancel())},Promise.prototype._resultCancelled=function(){this.cancel()}}},function(module,exports){"use strict";module.exports=function(Promise){function returner(){return this.value}function thrower(){throw this.reason}Promise.prototype.return=Promise.prototype.thenReturn=function(value){return value instanceof Promise&&value.suppressUnhandledRejections(),this._then(returner,void 0,void 0,{value:value},void 0)},Promise.prototype.throw=Promise.prototype.thenThrow=function(reason){return this._then(thrower,void 0,void 0,{reason:reason},void 0)},Promise.prototype.catchThrow=function(reason){if(arguments.length<=1)return this._then(void 0,thrower,void 0,{reason:reason},void 0);var _reason=arguments[1],handler=function(){throw _reason};return this.caught(reason,handler)},Promise.prototype.catchReturn=function(value){if(arguments.length<=1)return value instanceof Promise&&value.suppressUnhandledRejections(),this._then(void 0,returner,void 0,{value:value},void 0);var _value=arguments[1];_value instanceof Promise&&_value.suppressUnhandledRejections();var handler=function(){return _value};return this.caught(value,handler)}}},function(module,exports){"use strict";module.exports=function(Promise){function PromiseInspection(promise){void 0!==promise?(promise=promise._target(),this._bitField=promise._bitField,this._settledValueField=promise._isFateSealed()?promise._settledValue():void 0):(this._bitField=0,this._settledValueField=void 0)}PromiseInspection.prototype._settledValue=function(){return this._settledValueField};var value=PromiseInspection.prototype.value=function(){if(!this.isFulfilled())throw new TypeError("cannot get fulfillment value of a non-fulfilled promise\n\n    See http://goo.gl/MqrFmX\n");return this._settledValue()},reason=PromiseInspection.prototype.error=PromiseInspection.prototype.reason=function(){if(!this.isRejected())throw new TypeError("cannot get rejection reason of a non-rejected promise\n\n    See http://goo.gl/MqrFmX\n");return this._settledValue()},isFulfilled=PromiseInspection.prototype.isFulfilled=function(){return 0!==(33554432&this._bitField)},isRejected=PromiseInspection.prototype.isRejected=function(){return 0!==(16777216&this._bitField)},isPending=PromiseInspection.prototype.isPending=function(){return 0===(50397184&this._bitField)},isResolved=PromiseInspection.prototype.isResolved=function(){return 0!==(50331648&this._bitField)};PromiseInspection.prototype.isCancelled=function(){return 0!==(8454144&this._bitField)},Promise.prototype.__isCancelled=function(){return 65536===(65536&this._bitField)},Promise.prototype._isCancelled=function(){return this._target().__isCancelled()},Promise.prototype.isCancelled=function(){return 0!==(8454144&this._target()._bitField)},Promise.prototype.isPending=function(){return isPending.call(this._target())},Promise.prototype.isRejected=function(){return isRejected.call(this._target())},Promise.prototype.isFulfilled=function(){return isFulfilled.call(this._target())},Promise.prototype.isResolved=function(){return isResolved.call(this._target())},Promise.prototype.value=function(){return value.call(this._target())},Promise.prototype.reason=function(){var target=this._target();return target._unsetRejectionIsUnhandled(),reason.call(target)},Promise.prototype._value=function(){return this._settledValue()},Promise.prototype._reason=function(){return this._unsetRejectionIsUnhandled(),this._settledValue()},Promise.PromiseInspection=PromiseInspection}},function(module,exports,__webpack_require__){"use strict";module.exports=function(Promise,PromiseArray,tryConvertToPromise,INTERNAL,async,getDomain){var reject,util=__webpack_require__(5),canEvaluate=util.canEvaluate,tryCatch=util.tryCatch,errorObj=util.errorObj;if(canEvaluate){for(var thenCallback=function(i){return new Function("value","holder","                             \n\t            'use strict';                                                    \n\t            holder.pIndex = value;                                           \n\t            holder.checkFulfillment(this);                                   \n\t            ".replace(/Index/g,i))},promiseSetter=function(i){return new Function("promise","holder","                           \n\t            'use strict';                                                    \n\t            holder.pIndex = promise;                                         \n\t            ".replace(/Index/g,i))},generateHolderClass=function(total){for(var props=new Array(total),i=0;i<props.length;++i)props[i]="this.p"+(i+1);var assignment=props.join(" = ")+" = null;",cancellationCode="var promise;\n"+props.map(function(prop){return"                                                         \n\t                promise = "+prop+";                                      \n\t                if (promise instanceof Promise) {                            \n\t                    promise.cancel();                                        \n\t                }                                                            \n\t            "}).join("\n"),passedArguments=props.join(", "),name="Holder$"+total,code="return function(tryCatch, errorObj, Promise, async) {    \n\t            'use strict';                                                    \n\t            function [TheName](fn) {                                         \n\t                [TheProperties]                                              \n\t                this.fn = fn;                                                \n\t                this.asyncNeeded = true;                                     \n\t                this.now = 0;                                                \n\t            }                                                                \n\t                                                                             \n\t            [TheName].prototype._callFunction = function(promise) {          \n\t                promise._pushContext();                                      \n\t                var ret = tryCatch(this.fn)([ThePassedArguments]);           \n\t                promise._popContext();                                       \n\t                if (ret === errorObj) {                                      \n\t                    promise._rejectCallback(ret.e, false);                   \n\t                } else {                                                     \n\t                    promise._resolveCallback(ret);                           \n\t                }                                                            \n\t            };                                                               \n\t                                                                             \n\t            [TheName].prototype.checkFulfillment = function(promise) {       \n\t                var now = ++this.now;                                        \n\t                if (now === [TheTotal]) {                                    \n\t                    if (this.asyncNeeded) {                                  \n\t                        async.invoke(this._callFunction, this, promise);     \n\t                    } else {                                                 \n\t                        this._callFunction(promise);                         \n\t                    }                                                        \n\t                                                                             \n\t                }                                                            \n\t            };                                                               \n\t                                                                             \n\t            [TheName].prototype._resultCancelled = function() {              \n\t                [CancellationCode]                                           \n\t            };                                                               \n\t                                                                             \n\t            return [TheName];                                                \n\t        }(tryCatch, errorObj, Promise, async);                               \n\t        ";return code=code.replace(/\[TheName\]/g,name).replace(/\[TheTotal\]/g,total).replace(/\[ThePassedArguments\]/g,passedArguments).replace(/\[TheProperties\]/g,assignment).replace(/\[CancellationCode\]/g,cancellationCode),new Function("tryCatch","errorObj","Promise","async",code)(tryCatch,errorObj,Promise,async)},holderClasses=[],thenCallbacks=[],promiseSetters=[],i=0;i<8;++i)holderClasses.push(generateHolderClass(i+1)),thenCallbacks.push(thenCallback(i+1)),promiseSetters.push(promiseSetter(i+1));reject=function(reason){this._reject(reason)}}Promise.join=function(){var fn,last=arguments.length-1;if(last>0&&"function"==typeof arguments[last]&&(fn=arguments[last],last<=8&&canEvaluate)){var ret=new Promise(INTERNAL);ret._captureStackTrace();for(var HolderClass=holderClasses[last-1],holder=new HolderClass(fn),callbacks=thenCallbacks,i=0;i<last;++i){var maybePromise=tryConvertToPromise(arguments[i],ret);if(maybePromise instanceof Promise){maybePromise=maybePromise._target();var bitField=maybePromise._bitField;0===(50397184&bitField)?(maybePromise._then(callbacks[i],reject,void 0,ret,holder),promiseSetters[i](maybePromise,holder),holder.asyncNeeded=!1):0!==(33554432&bitField)?callbacks[i].call(ret,maybePromise._value(),holder):0!==(16777216&bitField)?ret._reject(maybePromise._reason()):ret._cancel()}else callbacks[i].call(ret,maybePromise,holder)}if(!ret._isFateSealed()){if(holder.asyncNeeded){var domain=getDomain();null!==domain&&(holder.fn=util.domainBind(domain,holder.fn))}ret._setAsyncGuaranteed(),ret._setOnCancel(holder)}return ret}for(var $_len=arguments.length,args=new Array($_len),$_i=0;$_i<$_len;++$_i)args[$_i]=arguments[$_i];fn&&args.pop();var ret=new PromiseArray(args).promise();return void 0!==fn?ret.spread(fn):ret}}},function(module,exports,__webpack_require__){"use strict";module.exports=function(Promise,PromiseArray,apiRejection,tryConvertToPromise,INTERNAL,debug){function MappingPromiseArray(promises,fn,limit,_filter){this.constructor$(promises),this._promise._captureStackTrace();var domain=getDomain();this._callback=null===domain?fn:util.domainBind(domain,fn),this._preservedValues=_filter===INTERNAL?new Array(this.length()):null,this._limit=limit,this._inFlight=0,this._queue=[],async.invoke(this._asyncInit,this,void 0)}function map(promises,fn,options,_filter){if("function"!=typeof fn)return apiRejection("expecting a function but got "+util.classString(fn));var limit=0;if(void 0!==options){if("object"!=typeof options||null===options)return Promise.reject(new TypeError("options argument must be an object but it is "+util.classString(options)));if("number"!=typeof options.concurrency)return Promise.reject(new TypeError("'concurrency' must be a number but it is "+util.classString(options.concurrency)));limit=options.concurrency}return limit="number"==typeof limit&&isFinite(limit)&&limit>=1?limit:0,new MappingPromiseArray(promises,fn,limit,_filter).promise()}var getDomain=Promise._getDomain,util=__webpack_require__(5),tryCatch=util.tryCatch,errorObj=util.errorObj,async=Promise._async;util.inherits(MappingPromiseArray,PromiseArray),MappingPromiseArray.prototype._asyncInit=function(){this._init$(void 0,-2)},MappingPromiseArray.prototype._init=function(){},MappingPromiseArray.prototype._promiseFulfilled=function(value,index){var values=this._values,length=this.length(),preservedValues=this._preservedValues,limit=this._limit;if(index<0){if(index=index*-1-1,values[index]=value,limit>=1&&(this._inFlight--,this._drainQueue(),this._isResolved()))return!0}else{if(limit>=1&&this._inFlight>=limit)return values[index]=value,this._queue.push(index),!1;null!==preservedValues&&(preservedValues[index]=value);var promise=this._promise,callback=this._callback,receiver=promise._boundValue();promise._pushContext();var ret=tryCatch(callback).call(receiver,value,index,length),promiseCreated=promise._popContext();if(debug.checkForgottenReturns(ret,promiseCreated,null!==preservedValues?"Promise.filter":"Promise.map",promise),ret===errorObj)return this._reject(ret.e),!0;var maybePromise=tryConvertToPromise(ret,this._promise);if(maybePromise instanceof Promise){maybePromise=maybePromise._target();var bitField=maybePromise._bitField;if(0===(50397184&bitField))return limit>=1&&this._inFlight++,values[index]=maybePromise,maybePromise._proxy(this,(index+1)*-1),!1;if(0===(33554432&bitField))return 0!==(16777216&bitField)?(this._reject(maybePromise._reason()),!0):(this._cancel(),!0);ret=maybePromise._value()}values[index]=ret}var totalResolved=++this._totalResolved;return totalResolved>=length&&(null!==preservedValues?this._filter(values,preservedValues):this._resolve(values),!0)},MappingPromiseArray.prototype._drainQueue=function(){
for(var queue=this._queue,limit=this._limit,values=this._values;queue.length>0&&this._inFlight<limit;){if(this._isResolved())return;var index=queue.pop();this._promiseFulfilled(values[index],index)}},MappingPromiseArray.prototype._filter=function(booleans,values){for(var len=values.length,ret=new Array(len),j=0,i=0;i<len;++i)booleans[i]&&(ret[j++]=values[i]);ret.length=j,this._resolve(ret)},MappingPromiseArray.prototype.preservedValues=function(){return this._preservedValues},Promise.prototype.map=function(fn,options){return map(this,fn,options,null)},Promise.map=function(promises,fn,options,_filter){return map(promises,fn,options,_filter)}}},function(module,exports,__webpack_require__){"use strict";var cr=Object.create;if(cr){var callerCache=cr(null),getterCache=cr(null);callerCache[" size"]=getterCache[" size"]=0}module.exports=function(Promise){function ensureMethod(obj,methodName){var fn;if(null!=obj&&(fn=obj[methodName]),"function"!=typeof fn){var message="Object "+util.classString(obj)+" has no method '"+util.toString(methodName)+"'";throw new Promise.TypeError(message)}return fn}function caller(obj){var methodName=this.pop(),fn=ensureMethod(obj,methodName);return fn.apply(obj,this)}function namedGetter(obj){return obj[this]}function indexedGetter(obj){var index=+this;return index<0&&(index=Math.max(0,index+obj.length)),obj[index]}var getMethodCaller,getGetter,util=__webpack_require__(5),canEvaluate=util.canEvaluate,isIdentifier=util.isIdentifier,makeMethodCaller=function(methodName){return new Function("ensureMethod","                                    \n\t        return function(obj) {                                               \n\t            'use strict'                                                     \n\t            var len = this.length;                                           \n\t            ensureMethod(obj, 'methodName');                                 \n\t            switch(len) {                                                    \n\t                case 1: return obj.methodName(this[0]);                      \n\t                case 2: return obj.methodName(this[0], this[1]);             \n\t                case 3: return obj.methodName(this[0], this[1], this[2]);    \n\t                case 0: return obj.methodName();                             \n\t                default:                                                     \n\t                    return obj.methodName.apply(obj, this);                  \n\t            }                                                                \n\t        };                                                                   \n\t        ".replace(/methodName/g,methodName))(ensureMethod)},makeGetter=function(propertyName){return new Function("obj","                                             \n\t        'use strict';                                                        \n\t        return obj.propertyName;                                             \n\t        ".replace("propertyName",propertyName))},getCompiled=function(name,compiler,cache){var ret=cache[name];if("function"!=typeof ret){if(!isIdentifier(name))return null;if(ret=compiler(name),cache[name]=ret,cache[" size"]++,cache[" size"]>512){for(var keys=Object.keys(cache),i=0;i<256;++i)delete cache[keys[i]];cache[" size"]=keys.length-256}}return ret};getMethodCaller=function(name){return getCompiled(name,makeMethodCaller,callerCache)},getGetter=function(name){return getCompiled(name,makeGetter,getterCache)},Promise.prototype.call=function(methodName){for(var $_len=arguments.length,args=new Array(Math.max($_len-1,0)),$_i=1;$_i<$_len;++$_i)args[$_i-1]=arguments[$_i];if(canEvaluate){var maybeCaller=getMethodCaller(methodName);if(null!==maybeCaller)return this._then(maybeCaller,void 0,void 0,args,void 0)}return args.push(methodName),this._then(caller,void 0,void 0,args,void 0)},Promise.prototype.get=function(propertyName){var getter,isIndex="number"==typeof propertyName;if(isIndex)getter=indexedGetter;else if(canEvaluate){var maybeGetter=getGetter(propertyName);getter=null!==maybeGetter?maybeGetter:namedGetter}else getter=namedGetter;return this._then(getter,void 0,void 0,propertyName,void 0)}}},function(module,exports,__webpack_require__){"use strict";module.exports=function(Promise,apiRejection,tryConvertToPromise,createContext,INTERNAL,debug){function thrower(e){setTimeout(function(){throw e},0)}function castPreservingDisposable(thenable){var maybePromise=tryConvertToPromise(thenable);return maybePromise!==thenable&&"function"==typeof thenable._isDisposable&&"function"==typeof thenable._getDisposer&&thenable._isDisposable()&&maybePromise._setDisposable(thenable._getDisposer()),maybePromise}function dispose(resources,inspection){function iterator(){if(i>=len)return ret._fulfill();var maybePromise=castPreservingDisposable(resources[i++]);if(maybePromise instanceof Promise&&maybePromise._isDisposable()){try{maybePromise=tryConvertToPromise(maybePromise._getDisposer().tryDispose(inspection),resources.promise)}catch(e){return thrower(e)}if(maybePromise instanceof Promise)return maybePromise._then(iterator,thrower,null,null,null)}iterator()}var i=0,len=resources.length,ret=new Promise(INTERNAL);return iterator(),ret}function Disposer(data,promise,context){this._data=data,this._promise=promise,this._context=context}function FunctionDisposer(fn,promise,context){this.constructor$(fn,promise,context)}function maybeUnwrapDisposer(value){return Disposer.isDisposer(value)?(this.resources[this.index]._setDisposable(value),value.promise()):value}function ResourceList(length){this.length=length,this.promise=null,this[length-1]=null}var util=__webpack_require__(5),TypeError=__webpack_require__(12).TypeError,inherits=__webpack_require__(5).inherits,errorObj=util.errorObj,tryCatch=util.tryCatch,NULL={};Disposer.prototype.data=function(){return this._data},Disposer.prototype.promise=function(){return this._promise},Disposer.prototype.resource=function(){return this.promise().isFulfilled()?this.promise().value():NULL},Disposer.prototype.tryDispose=function(inspection){var resource=this.resource(),context=this._context;void 0!==context&&context._pushContext();var ret=resource!==NULL?this.doDispose(resource,inspection):null;return void 0!==context&&context._popContext(),this._promise._unsetDisposable(),this._data=null,ret},Disposer.isDisposer=function(d){return null!=d&&"function"==typeof d.resource&&"function"==typeof d.tryDispose},inherits(FunctionDisposer,Disposer),FunctionDisposer.prototype.doDispose=function(resource,inspection){var fn=this.data();return fn.call(resource,resource,inspection)},ResourceList.prototype._resultCancelled=function(){for(var len=this.length,i=0;i<len;++i){var item=this[i];item instanceof Promise&&item.cancel()}},Promise.using=function(){var len=arguments.length;if(len<2)return apiRejection("you must pass at least 2 arguments to Promise.using");var fn=arguments[len-1];if("function"!=typeof fn)return apiRejection("expecting a function but got "+util.classString(fn));var input,spreadArgs=!0;2===len&&Array.isArray(arguments[0])?(input=arguments[0],len=input.length,spreadArgs=!1):(input=arguments,len--);for(var resources=new ResourceList(len),i=0;i<len;++i){var resource=input[i];if(Disposer.isDisposer(resource)){var disposer=resource;resource=resource.promise(),resource._setDisposable(disposer)}else{var maybePromise=tryConvertToPromise(resource);maybePromise instanceof Promise&&(resource=maybePromise._then(maybeUnwrapDisposer,null,null,{resources:resources,index:i},void 0))}resources[i]=resource}for(var reflectedResources=new Array(resources.length),i=0;i<reflectedResources.length;++i)reflectedResources[i]=Promise.resolve(resources[i]).reflect();var resultPromise=Promise.all(reflectedResources).then(function(inspections){for(var i=0;i<inspections.length;++i){var inspection=inspections[i];if(inspection.isRejected())return errorObj.e=inspection.error(),errorObj;if(!inspection.isFulfilled())return void resultPromise.cancel();inspections[i]=inspection.value()}promise._pushContext(),fn=tryCatch(fn);var ret=spreadArgs?fn.apply(void 0,inspections):fn(inspections),promiseCreated=promise._popContext();return debug.checkForgottenReturns(ret,promiseCreated,"Promise.using",promise),ret}),promise=resultPromise.lastly(function(){var inspection=new Promise.PromiseInspection(resultPromise);return dispose(resources,inspection)});return resources.promise=promise,promise._setOnCancel(resources),promise},Promise.prototype._setDisposable=function(disposer){this._bitField=131072|this._bitField,this._disposer=disposer},Promise.prototype._isDisposable=function(){return(131072&this._bitField)>0},Promise.prototype._getDisposer=function(){return this._disposer},Promise.prototype._unsetDisposable=function(){this._bitField=this._bitField&-131073,this._disposer=void 0},Promise.prototype.disposer=function(fn){if("function"==typeof fn)return new FunctionDisposer(fn,this,createContext());throw new TypeError}}},function(module,exports,__webpack_require__){"use strict";module.exports=function(Promise,INTERNAL,debug){function HandleWrapper(handle){this.handle=handle}function successClear(value){return clearTimeout(this.handle),value}function failureClear(reason){throw clearTimeout(this.handle),reason}var util=__webpack_require__(5),TimeoutError=Promise.TimeoutError;HandleWrapper.prototype._resultCancelled=function(){clearTimeout(this.handle)};var afterValue=function(value){return delay(+this).thenReturn(value)},delay=Promise.delay=function(ms,value){var ret,handle;return void 0!==value?(ret=Promise.resolve(value)._then(afterValue,null,null,ms,void 0),debug.cancellation()&&value instanceof Promise&&ret._setOnCancel(value)):(ret=new Promise(INTERNAL),handle=setTimeout(function(){ret._fulfill()},+ms),debug.cancellation()&&ret._setOnCancel(new HandleWrapper(handle)),ret._captureStackTrace()),ret._setAsyncGuaranteed(),ret};Promise.prototype.delay=function(ms){return delay(ms,this)};var afterTimeout=function(promise,message,parent){var err;err="string"!=typeof message?message instanceof Error?message:new TimeoutError("operation timed out"):new TimeoutError(message),util.markAsOriginatingFromRejection(err),promise._attachExtraTrace(err),promise._reject(err),null!=parent&&parent.cancel()};Promise.prototype.timeout=function(ms,message){ms=+ms;var ret,parent,handleWrapper=new HandleWrapper(setTimeout(function(){ret.isPending()&&afterTimeout(ret,message,parent)},ms));return debug.cancellation()?(parent=this.then(),ret=parent._then(successClear,failureClear,void 0,handleWrapper,void 0),ret._setOnCancel(handleWrapper)):ret=this._then(successClear,failureClear,void 0,handleWrapper,void 0),ret}}},function(module,exports,__webpack_require__){"use strict";module.exports=function(Promise,apiRejection,INTERNAL,tryConvertToPromise,Proxyable,debug){function promiseFromYieldHandler(value,yieldHandlers,traceParent){for(var i=0;i<yieldHandlers.length;++i){traceParent._pushContext();var result=tryCatch(yieldHandlers[i])(value);if(traceParent._popContext(),result===errorObj){traceParent._pushContext();var ret=Promise.reject(errorObj.e);return traceParent._popContext(),ret}var maybePromise=tryConvertToPromise(result,traceParent);if(maybePromise instanceof Promise)return maybePromise}return null}function PromiseSpawn(generatorFunction,receiver,yieldHandler,stack){if(debug.cancellation()){var internal=new Promise(INTERNAL),_finallyPromise=this._finallyPromise=new Promise(INTERNAL);this._promise=internal.lastly(function(){return _finallyPromise}),internal._captureStackTrace(),internal._setOnCancel(this)}else{var promise=this._promise=new Promise(INTERNAL);promise._captureStackTrace()}this._stack=stack,this._generatorFunction=generatorFunction,this._receiver=receiver,this._generator=void 0,this._yieldHandlers="function"==typeof yieldHandler?[yieldHandler].concat(yieldHandlers):yieldHandlers,this._yieldedPromise=null,this._cancellationPhase=!1}var errors=__webpack_require__(12),TypeError=errors.TypeError,util=__webpack_require__(5),errorObj=util.errorObj,tryCatch=util.tryCatch,yieldHandlers=[];util.inherits(PromiseSpawn,Proxyable),PromiseSpawn.prototype._isResolved=function(){return null===this._promise},PromiseSpawn.prototype._cleanup=function(){this._promise=this._generator=null,debug.cancellation()&&null!==this._finallyPromise&&(this._finallyPromise._fulfill(),this._finallyPromise=null)},PromiseSpawn.prototype._promiseCancelled=function(){if(!this._isResolved()){var result,implementsReturn="undefined"!=typeof this._generator.return;if(implementsReturn)this._promise._pushContext(),result=tryCatch(this._generator.return).call(this._generator,void 0),this._promise._popContext();else{var reason=new Promise.CancellationError("generator .return() sentinel");Promise.coroutine.returnSentinel=reason,this._promise._attachExtraTrace(reason),this._promise._pushContext(),result=tryCatch(this._generator.throw).call(this._generator,reason),this._promise._popContext()}this._cancellationPhase=!0,this._yieldedPromise=null,this._continue(result)}},PromiseSpawn.prototype._promiseFulfilled=function(value){this._yieldedPromise=null,this._promise._pushContext();var result=tryCatch(this._generator.next).call(this._generator,value);this._promise._popContext(),this._continue(result)},PromiseSpawn.prototype._promiseRejected=function(reason){this._yieldedPromise=null,this._promise._attachExtraTrace(reason),this._promise._pushContext();var result=tryCatch(this._generator.throw).call(this._generator,reason);this._promise._popContext(),this._continue(result)},PromiseSpawn.prototype._resultCancelled=function(){if(this._yieldedPromise instanceof Promise){var promise=this._yieldedPromise;this._yieldedPromise=null,promise.cancel()}},PromiseSpawn.prototype.promise=function(){return this._promise},PromiseSpawn.prototype._run=function(){this._generator=this._generatorFunction.call(this._receiver),this._receiver=this._generatorFunction=void 0,this._promiseFulfilled(void 0)},PromiseSpawn.prototype._continue=function(result){var promise=this._promise;if(result===errorObj)return this._cleanup(),this._cancellationPhase?promise.cancel():promise._rejectCallback(result.e,!1);var value=result.value;if(result.done===!0)return this._cleanup(),this._cancellationPhase?promise.cancel():promise._resolveCallback(value);var maybePromise=tryConvertToPromise(value,this._promise);if(!(maybePromise instanceof Promise)&&(maybePromise=promiseFromYieldHandler(maybePromise,this._yieldHandlers,this._promise),null===maybePromise))return void this._promiseRejected(new TypeError("A value %s was yielded that could not be treated as a promise\n\n    See http://goo.gl/MqrFmX\n\n".replace("%s",String(value))+"From coroutine:\n"+this._stack.split("\n").slice(1,-7).join("\n")));maybePromise=maybePromise._target();var bitField=maybePromise._bitField;0===(50397184&bitField)?(this._yieldedPromise=maybePromise,maybePromise._proxy(this,null)):0!==(33554432&bitField)?Promise._async.invoke(this._promiseFulfilled,this,maybePromise._value()):0!==(16777216&bitField)?Promise._async.invoke(this._promiseRejected,this,maybePromise._reason()):this._promiseCancelled()},Promise.coroutine=function(generatorFunction,options){if("function"!=typeof generatorFunction)throw new TypeError("generatorFunction must be a function\n\n    See http://goo.gl/MqrFmX\n");var yieldHandler=Object(options).yieldHandler,PromiseSpawn$=PromiseSpawn,stack=(new Error).stack;return function(){var generator=generatorFunction.apply(this,arguments),spawn=new PromiseSpawn$(void 0,void 0,yieldHandler,stack),ret=spawn.promise();return spawn._generator=generator,spawn._promiseFulfilled(void 0),ret}},Promise.coroutine.addYieldHandler=function(fn){if("function"!=typeof fn)throw new TypeError("expecting a function but got "+util.classString(fn));yieldHandlers.push(fn)},Promise.spawn=function(generatorFunction){if(debug.deprecated("Promise.spawn()","Promise.coroutine()"),"function"!=typeof generatorFunction)return apiRejection("generatorFunction must be a function\n\n    See http://goo.gl/MqrFmX\n");var spawn=new PromiseSpawn(generatorFunction,this),ret=spawn.promise();return spawn._run(Promise.spawn),ret}}},function(module,exports,__webpack_require__){"use strict";module.exports=function(Promise){function spreadAdapter(val,nodeback){var promise=this;if(!util.isArray(val))return successAdapter.call(promise,val,nodeback);var ret=tryCatch(nodeback).apply(promise._boundValue(),[null].concat(val));ret===errorObj&&async.throwLater(ret.e)}function successAdapter(val,nodeback){var promise=this,receiver=promise._boundValue(),ret=void 0===val?tryCatch(nodeback).call(receiver,null):tryCatch(nodeback).call(receiver,null,val);ret===errorObj&&async.throwLater(ret.e)}function errorAdapter(reason,nodeback){var promise=this;if(!reason){var newReason=new Error(reason+"");newReason.cause=reason,reason=newReason}var ret=tryCatch(nodeback).call(promise._boundValue(),reason);ret===errorObj&&async.throwLater(ret.e)}var util=__webpack_require__(5),async=Promise._async,tryCatch=util.tryCatch,errorObj=util.errorObj;Promise.prototype.asCallback=Promise.prototype.nodeify=function(nodeback,options){if("function"==typeof nodeback){var adapter=successAdapter;void 0!==options&&Object(options).spread&&(adapter=spreadAdapter),this._then(adapter,errorAdapter,void 0,this,nodeback)}return this}}},function(module,exports,__webpack_require__){"use strict";module.exports=function(Promise,INTERNAL){function propsFilter(key){return!noCopyPropsPattern.test(key)}function isPromisified(fn){try{return fn.__isPromisified__===!0}catch(e){return!1}}function hasPromisified(obj,key,suffix){var val=util.getDataPropertyOrDefault(obj,key+suffix,defaultPromisified);return!!val&&isPromisified(val)}function checkValid(ret,suffix,suffixRegexp){for(var i=0;i<ret.length;i+=2){var key=ret[i];if(suffixRegexp.test(key))for(var keyWithoutAsyncSuffix=key.replace(suffixRegexp,""),j=0;j<ret.length;j+=2)if(ret[j]===keyWithoutAsyncSuffix)throw new TypeError("Cannot promisify an API that has normal methods with '%s'-suffix\n\n    See http://goo.gl/MqrFmX\n".replace("%s",suffix))}}function promisifiableMethods(obj,suffix,suffixRegexp,filter){for(var keys=util.inheritedDataKeys(obj),ret=[],i=0;i<keys.length;++i){var key=keys[i],value=obj[key],passesDefaultFilter=filter===defaultFilter||defaultFilter(key,value,obj);"function"!=typeof value||isPromisified(value)||hasPromisified(obj,key,suffix)||!filter(key,value,obj,passesDefaultFilter)||ret.push(key,value)}return checkValid(ret,suffix,suffixRegexp),ret}function makeNodePromisifiedClosure(callback,receiver,_,fn,__,multiArgs){function promisified(){var _receiver=receiver;receiver===THIS&&(_receiver=this);var promise=new Promise(INTERNAL);promise._captureStackTrace();var cb="string"==typeof method&&this!==defaultThis?this[method]:callback,fn=nodebackForPromise(promise,multiArgs);try{cb.apply(_receiver,withAppended(arguments,fn))}catch(e){promise._rejectCallback(maybeWrapAsError(e),!0,!0)}return promise._isFateSealed()||promise._setAsyncGuaranteed(),promise}var defaultThis=function(){return this}(),method=callback;return"string"==typeof method&&(callback=fn),util.notEnumerableProp(promisified,"__isPromisified__",!0),promisified}function promisifyAll(obj,suffix,filter,promisifier,multiArgs){for(var suffixRegexp=new RegExp(escapeIdentRegex(suffix)+"$"),methods=promisifiableMethods(obj,suffix,suffixRegexp,filter),i=0,len=methods.length;i<len;i+=2){var key=methods[i],fn=methods[i+1],promisifiedKey=key+suffix;if(promisifier===makeNodePromisified)obj[promisifiedKey]=makeNodePromisified(key,THIS,key,fn,suffix,multiArgs);else{var promisified=promisifier(fn,function(){return makeNodePromisified(key,THIS,key,fn,suffix,multiArgs)});util.notEnumerableProp(promisified,"__isPromisified__",!0),obj[promisifiedKey]=promisified}}return util.toFastProperties(obj),obj}function promisify(callback,receiver,multiArgs){return makeNodePromisified(callback,receiver,void 0,callback,null,multiArgs)}var makeNodePromisifiedEval,THIS={},util=__webpack_require__(5),nodebackForPromise=__webpack_require__(19),withAppended=util.withAppended,maybeWrapAsError=util.maybeWrapAsError,canEvaluate=util.canEvaluate,TypeError=__webpack_require__(12).TypeError,defaultSuffix="Async",defaultPromisified={__isPromisified__:!0},noCopyProps=["arity","length","name","arguments","caller","callee","prototype","__isPromisified__"],noCopyPropsPattern=new RegExp("^(?:"+noCopyProps.join("|")+")$"),defaultFilter=function(name){return util.isIdentifier(name)&&"_"!==name.charAt(0)&&"constructor"!==name},escapeIdentRegex=function(str){return str.replace(/([$])/,"\\$")},switchCaseArgumentOrder=function(likelyArgumentCount){for(var ret=[likelyArgumentCount],min=Math.max(0,likelyArgumentCount-1-3),i=likelyArgumentCount-1;i>=min;--i)ret.push(i);for(var i=likelyArgumentCount+1;i<=3;++i)ret.push(i);return ret},argumentSequence=function(argumentCount){return util.filledRange(argumentCount,"_arg","")},parameterDeclaration=function(parameterCount){return util.filledRange(Math.max(parameterCount,3),"_arg","")},parameterCount=function(fn){return"number"==typeof fn.length?Math.max(Math.min(fn.length,1024),0):0};makeNodePromisifiedEval=function(callback,receiver,originalName,fn,_,multiArgs){function generateCallForArgumentCount(count){var ret,args=argumentSequence(count).join(", "),comma=count>0?", ":"";return ret=shouldProxyThis?"ret = callback.call(this, {{args}}, nodeback); break;\n":void 0===receiver?"ret = callback({{args}}, nodeback); break;\n":"ret = callback.call(receiver, {{args}}, nodeback); break;\n",ret.replace("{{args}}",args).replace(", ",comma)}function generateArgumentSwitchCase(){for(var ret="",i=0;i<argumentOrder.length;++i)ret+="case "+argumentOrder[i]+":"+generateCallForArgumentCount(argumentOrder[i]);return ret+="                                                             \n\t        default:                                                             \n\t            var args = new Array(len + 1);                                   \n\t            var i = 0;                                                       \n\t            for (var i = 0; i < len; ++i) {                                  \n\t               args[i] = arguments[i];                                       \n\t            }                                                                \n\t            args[i] = nodeback;                                              \n\t            [CodeForCall]                                                    \n\t            break;                                                           \n\t        ".replace("[CodeForCall]",shouldProxyThis?"ret = callback.apply(this, args);\n":"ret = callback.apply(receiver, args);\n")}var newParameterCount=Math.max(0,parameterCount(fn)-1),argumentOrder=switchCaseArgumentOrder(newParameterCount),shouldProxyThis="string"==typeof callback||receiver===THIS,getFunctionCode="string"==typeof callback?"this != null ? this['"+callback+"'] : fn":"fn",body="'use strict';                                                \n\t        var ret = function (Parameters) {                                    \n\t            'use strict';                                                    \n\t            var len = arguments.length;                                      \n\t            var promise = new Promise(INTERNAL);                             \n\t            promise._captureStackTrace();                                    \n\t            var nodeback = nodebackForPromise(promise, "+multiArgs+");   \n\t            var ret;                                                         \n\t            var callback = tryCatch([GetFunctionCode]);                      \n\t            switch(len) {                                                    \n\t                [CodeForSwitchCase]                                          \n\t            }                                                                \n\t            if (ret === errorObj) {                                          \n\t                promise._rejectCallback(maybeWrapAsError(ret.e), true, true);\n\t            }                                                                \n\t            if (!promise._isFateSealed()) promise._setAsyncGuaranteed();     \n\t            return promise;                                                  \n\t        };                                                                   \n\t        notEnumerableProp(ret, '__isPromisified__', true);                   \n\t        return ret;                                                          \n\t    ".replace("[CodeForSwitchCase]",generateArgumentSwitchCase()).replace("[GetFunctionCode]",getFunctionCode);return body=body.replace("Parameters",parameterDeclaration(newParameterCount)),new Function("Promise","fn","receiver","withAppended","maybeWrapAsError","nodebackForPromise","tryCatch","errorObj","notEnumerableProp","INTERNAL",body)(Promise,fn,receiver,withAppended,maybeWrapAsError,nodebackForPromise,util.tryCatch,util.errorObj,util.notEnumerableProp,INTERNAL)};var makeNodePromisified=canEvaluate?makeNodePromisifiedEval:makeNodePromisifiedClosure;Promise.promisify=function(fn,options){if("function"!=typeof fn)throw new TypeError("expecting a function but got "+util.classString(fn));if(isPromisified(fn))return fn;options=Object(options);var receiver=void 0===options.context?THIS:options.context,multiArgs=!!options.multiArgs,ret=promisify(fn,receiver,multiArgs);return util.copyDescriptors(fn,ret,propsFilter),ret},Promise.promisifyAll=function(target,options){if("function"!=typeof target&&"object"!=typeof target)throw new TypeError("the target of promisifyAll must be an object or a function\n\n    See http://goo.gl/MqrFmX\n");options=Object(options);var multiArgs=!!options.multiArgs,suffix=options.suffix;"string"!=typeof suffix&&(suffix=defaultSuffix);var filter=options.filter;"function"!=typeof filter&&(filter=defaultFilter);var promisifier=options.promisifier;if("function"!=typeof promisifier&&(promisifier=makeNodePromisified),!util.isIdentifier(suffix))throw new RangeError("suffix must be a valid identifier\n\n    See http://goo.gl/MqrFmX\n");for(var keys=util.inheritedDataKeys(target),i=0;i<keys.length;++i){var value=target[keys[i]];"constructor"!==keys[i]&&util.isClass(value)&&(promisifyAll(value.prototype,suffix,filter,promisifier,multiArgs),promisifyAll(value,suffix,filter,promisifier,multiArgs))}return promisifyAll(target,suffix,filter,promisifier,multiArgs)}}},function(module,exports,__webpack_require__){"use strict";module.exports=function(Promise,PromiseArray,tryConvertToPromise,apiRejection){function PropertiesPromiseArray(obj){var entries,isMap=!1;if(void 0!==Es6Map&&obj instanceof Es6Map)entries=mapToEntries(obj),isMap=!0;else{var keys=es5.keys(obj),len=keys.length;entries=new Array(2*len);for(var i=0;i<len;++i){var key=keys[i];entries[i]=obj[key],entries[i+len]=key}}this.constructor$(entries),this._isMap=isMap,this._init$(void 0,isMap?-6:-3)}function props(promises){var ret,castValue=tryConvertToPromise(promises);return isObject(castValue)?(ret=castValue instanceof Promise?castValue._then(Promise.props,void 0,void 0,void 0,void 0):new PropertiesPromiseArray(castValue).promise(),castValue instanceof Promise&&ret._propagateFrom(castValue,2),ret):apiRejection("cannot await properties of a non-object\n\n    See http://goo.gl/MqrFmX\n")}var Es6Map,util=__webpack_require__(5),isObject=util.isObject,es5=__webpack_require__(6);"function"==typeof Map&&(Es6Map=Map);var mapToEntries=function(){function extractEntry(value,key){this[index]=value,this[index+size]=key,index++}var index=0,size=0;return function(map){size=map.size,index=0;var ret=new Array(2*map.size);return map.forEach(extractEntry,ret),ret}}(),entriesToMap=function(entries){for(var ret=new Es6Map,length=entries.length/2|0,i=0;i<length;++i){var key=entries[length+i],value=entries[i];ret.set(key,value)}return ret};util.inherits(PropertiesPromiseArray,PromiseArray),PropertiesPromiseArray.prototype._init=function(){},PropertiesPromiseArray.prototype._promiseFulfilled=function(value,index){this._values[index]=value;var totalResolved=++this._totalResolved;if(totalResolved>=this._length){var val;if(this._isMap)val=entriesToMap(this._values);else{val={};for(var keyOffset=this.length(),i=0,len=this.length();i<len;++i)val[this._values[i+keyOffset]]=this._values[i]}return this._resolve(val),!0}return!1},PropertiesPromiseArray.prototype.shouldCopyValues=function(){return!1},PropertiesPromiseArray.prototype.getActualLength=function(len){return len>>1},Promise.prototype.props=function(){return props(this)},Promise.props=function(promises){return props(promises)}}},function(module,exports,__webpack_require__){"use strict";module.exports=function(Promise,INTERNAL,tryConvertToPromise,apiRejection){function race(promises,parent){var maybePromise=tryConvertToPromise(promises);if(maybePromise instanceof Promise)return raceLater(maybePromise);if(promises=util.asArray(promises),null===promises)return apiRejection("expecting an array or an iterable object but got "+util.classString(promises));var ret=new Promise(INTERNAL);void 0!==parent&&ret._propagateFrom(parent,3);for(var fulfill=ret._fulfill,reject=ret._reject,i=0,len=promises.length;i<len;++i){var val=promises[i];(void 0!==val||i in promises)&&Promise.cast(val)._then(fulfill,reject,void 0,ret,null)}return ret}var util=__webpack_require__(5),raceLater=function(promise){return promise.then(function(array){return race(array,promise)})};Promise.race=function(promises){return race(promises,void 0)},Promise.prototype.race=function(){return race(this,void 0)}}},function(module,exports,__webpack_require__){"use strict";module.exports=function(Promise,PromiseArray,apiRejection,tryConvertToPromise,INTERNAL,debug){function ReductionPromiseArray(promises,fn,initialValue,_each){this.constructor$(promises);var domain=getDomain();this._fn=null===domain?fn:util.domainBind(domain,fn),void 0!==initialValue&&(initialValue=Promise.resolve(initialValue),initialValue._attachCancellationCallback(this)),this._initialValue=initialValue,this._currentCancellable=null,_each===INTERNAL?this._eachValues=Array(this._length):0===_each?this._eachValues=null:this._eachValues=void 0,this._promise._captureStackTrace(),this._init$(void 0,-5)}function completed(valueOrReason,array){this.isFulfilled()?array._resolve(valueOrReason):array._reject(valueOrReason)}function reduce(promises,fn,initialValue,_each){if("function"!=typeof fn)return apiRejection("expecting a function but got "+util.classString(fn));var array=new ReductionPromiseArray(promises,fn,initialValue,_each);return array.promise()}function gotAccum(accum){this.accum=accum,this.array._gotAccum(accum);var value=tryConvertToPromise(this.value,this.array._promise);return value instanceof Promise?(this.array._currentCancellable=value,value._then(gotValue,void 0,void 0,this,void 0)):gotValue.call(this,value)}function gotValue(value){var array=this.array,promise=array._promise,fn=tryCatch(array._fn);promise._pushContext();var ret;ret=void 0!==array._eachValues?fn.call(promise._boundValue(),value,this.index,this.length):fn.call(promise._boundValue(),this.accum,value,this.index,this.length),ret instanceof Promise&&(array._currentCancellable=ret);var promiseCreated=promise._popContext();return debug.checkForgottenReturns(ret,promiseCreated,void 0!==array._eachValues?"Promise.each":"Promise.reduce",promise),ret}var getDomain=Promise._getDomain,util=__webpack_require__(5),tryCatch=util.tryCatch;util.inherits(ReductionPromiseArray,PromiseArray),ReductionPromiseArray.prototype._gotAccum=function(accum){void 0!==this._eachValues&&null!==this._eachValues&&accum!==INTERNAL&&this._eachValues.push(accum)},ReductionPromiseArray.prototype._eachComplete=function(value){return null!==this._eachValues&&this._eachValues.push(value),this._eachValues},ReductionPromiseArray.prototype._init=function(){},ReductionPromiseArray.prototype._resolveEmptyArray=function(){this._resolve(void 0!==this._eachValues?this._eachValues:this._initialValue);
},ReductionPromiseArray.prototype.shouldCopyValues=function(){return!1},ReductionPromiseArray.prototype._resolve=function(value){this._promise._resolveCallback(value),this._values=null},ReductionPromiseArray.prototype._resultCancelled=function(sender){return sender===this._initialValue?this._cancel():void(this._isResolved()||(this._resultCancelled$(),this._currentCancellable instanceof Promise&&this._currentCancellable.cancel(),this._initialValue instanceof Promise&&this._initialValue.cancel()))},ReductionPromiseArray.prototype._iterate=function(values){this._values=values;var value,i,length=values.length;if(void 0!==this._initialValue?(value=this._initialValue,i=0):(value=Promise.resolve(values[0]),i=1),this._currentCancellable=value,!value.isRejected())for(;i<length;++i){var ctx={accum:null,value:values[i],index:i,length:length,array:this};value=value._then(gotAccum,void 0,void 0,ctx,void 0)}void 0!==this._eachValues&&(value=value._then(this._eachComplete,void 0,void 0,this,void 0)),value._then(completed,completed,void 0,value,this)},Promise.prototype.reduce=function(fn,initialValue){return reduce(this,fn,initialValue,null)},Promise.reduce=function(promises,fn,initialValue,_each){return reduce(promises,fn,initialValue,_each)}}},function(module,exports,__webpack_require__){"use strict";module.exports=function(Promise,PromiseArray,debug){function SettledPromiseArray(values){this.constructor$(values)}var PromiseInspection=Promise.PromiseInspection,util=__webpack_require__(5);util.inherits(SettledPromiseArray,PromiseArray),SettledPromiseArray.prototype._promiseResolved=function(index,inspection){this._values[index]=inspection;var totalResolved=++this._totalResolved;return totalResolved>=this._length&&(this._resolve(this._values),!0)},SettledPromiseArray.prototype._promiseFulfilled=function(value,index){var ret=new PromiseInspection;return ret._bitField=33554432,ret._settledValueField=value,this._promiseResolved(index,ret)},SettledPromiseArray.prototype._promiseRejected=function(reason,index){var ret=new PromiseInspection;return ret._bitField=16777216,ret._settledValueField=reason,this._promiseResolved(index,ret)},Promise.settle=function(promises){return debug.deprecated(".settle()",".reflect()"),new SettledPromiseArray(promises).promise()},Promise.prototype.settle=function(){return Promise.settle(this)}}},function(module,exports,__webpack_require__){"use strict";module.exports=function(Promise,PromiseArray,apiRejection){function SomePromiseArray(values){this.constructor$(values),this._howMany=0,this._unwrap=!1,this._initialized=!1}function some(promises,howMany){if((0|howMany)!==howMany||howMany<0)return apiRejection("expecting a positive integer\n\n    See http://goo.gl/MqrFmX\n");var ret=new SomePromiseArray(promises),promise=ret.promise();return ret.setHowMany(howMany),ret.init(),promise}var util=__webpack_require__(5),RangeError=__webpack_require__(12).RangeError,AggregateError=__webpack_require__(12).AggregateError,isArray=util.isArray,CANCELLATION={};util.inherits(SomePromiseArray,PromiseArray),SomePromiseArray.prototype._init=function(){if(this._initialized){if(0===this._howMany)return void this._resolve([]);this._init$(void 0,-5);var isArrayResolved=isArray(this._values);!this._isResolved()&&isArrayResolved&&this._howMany>this._canPossiblyFulfill()&&this._reject(this._getRangeError(this.length()))}},SomePromiseArray.prototype.init=function(){this._initialized=!0,this._init()},SomePromiseArray.prototype.setUnwrap=function(){this._unwrap=!0},SomePromiseArray.prototype.howMany=function(){return this._howMany},SomePromiseArray.prototype.setHowMany=function(count){this._howMany=count},SomePromiseArray.prototype._promiseFulfilled=function(value){return this._addFulfilled(value),this._fulfilled()===this.howMany()&&(this._values.length=this.howMany(),1===this.howMany()&&this._unwrap?this._resolve(this._values[0]):this._resolve(this._values),!0)},SomePromiseArray.prototype._promiseRejected=function(reason){return this._addRejected(reason),this._checkOutcome()},SomePromiseArray.prototype._promiseCancelled=function(){return this._values instanceof Promise||null==this._values?this._cancel():(this._addRejected(CANCELLATION),this._checkOutcome())},SomePromiseArray.prototype._checkOutcome=function(){if(this.howMany()>this._canPossiblyFulfill()){for(var e=new AggregateError,i=this.length();i<this._values.length;++i)this._values[i]!==CANCELLATION&&e.push(this._values[i]);return e.length>0?this._reject(e):this._cancel(),!0}return!1},SomePromiseArray.prototype._fulfilled=function(){return this._totalResolved},SomePromiseArray.prototype._rejected=function(){return this._values.length-this.length()},SomePromiseArray.prototype._addRejected=function(reason){this._values.push(reason)},SomePromiseArray.prototype._addFulfilled=function(value){this._values[this._totalResolved++]=value},SomePromiseArray.prototype._canPossiblyFulfill=function(){return this.length()-this._rejected()},SomePromiseArray.prototype._getRangeError=function(count){var message="Input array must contain at least "+this._howMany+" items but contains only "+count+" items";return new RangeError(message)},SomePromiseArray.prototype._resolveEmptyArray=function(){this._reject(this._getRangeError(0))},Promise.some=function(promises,howMany){return some(promises,howMany)},Promise.prototype.some=function(howMany){return some(this,howMany)},Promise._SomePromiseArray=SomePromiseArray}},function(module,exports){"use strict";module.exports=function(Promise,INTERNAL){var PromiseMap=Promise.map;Promise.prototype.filter=function(fn,options){return PromiseMap(this,fn,options,INTERNAL)},Promise.filter=function(promises,fn,options){return PromiseMap(promises,fn,options,INTERNAL)}}},function(module,exports){"use strict";module.exports=function(Promise,INTERNAL){function promiseAllThis(){return PromiseAll(this)}function PromiseMapSeries(promises,fn){return PromiseReduce(promises,fn,INTERNAL,INTERNAL)}var PromiseReduce=Promise.reduce,PromiseAll=Promise.all;Promise.prototype.each=function(fn){return PromiseReduce(this,fn,INTERNAL,0)._then(promiseAllThis,void 0,void 0,this,void 0)},Promise.prototype.mapSeries=function(fn){return PromiseReduce(this,fn,INTERNAL,INTERNAL)},Promise.each=function(promises,fn){return PromiseReduce(promises,fn,INTERNAL,0)._then(promiseAllThis,void 0,void 0,promises,void 0)},Promise.mapSeries=PromiseMapSeries}},function(module,exports){"use strict";module.exports=function(Promise){function any(promises){var ret=new SomePromiseArray(promises),promise=ret.promise();return ret.setHowMany(1),ret.setUnwrap(),ret.init(),promise}var SomePromiseArray=Promise._SomePromiseArray;Promise.any=function(promises){return any(promises)},Promise.prototype.any=function(){return any(this)}}},function(module,exports,__webpack_require__){"use strict";function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(exports,"__esModule",{value:!0});var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(obj){return typeof obj}:function(obj){return obj&&"function"==typeof Symbol&&obj.constructor===Symbol&&obj!==Symbol.prototype?"symbol":typeof obj},_CB=__webpack_require__(1),_CB2=_interopRequireDefault(_CB);__webpack_require__(42);if("undefined"==typeof localStorage||null===localStorage)var localStorage=__webpack_require__(45);if(_CB2.default.toJSON=function(thisObj){try{if(thisObj.constructor===Array){for(var i=0;i<thisObj.length;i++)thisObj[i]=_CB2.default.toJSON(thisObj[i]);return thisObj}var id=null,columnName=null,tableName=null,latitude=null,longitude=null;thisObj instanceof _CB2.default.CloudGeoPoint&&(latitude=thisObj.document.latitude,longitude=thisObj.document.longitude),thisObj instanceof _CB2.default.CloudFile&&(id=thisObj.document._id),thisObj instanceof _CB2.default.Column&&(columnName=thisObj.document.name),thisObj instanceof _CB2.default.CloudTable&&(tableName=thisObj.document.name);var obj=_CB2.default._clone(thisObj,id,longitude,latitude,tableName||columnName);if(!obj instanceof _CB2.default.CloudObject||!obj instanceof _CB2.default.CloudFile||!obj instanceof _CB2.default.CloudGeoPoint||!obj instanceof _CB2.default.CloudTable||!obj instanceof _CB2.default.Column)throw"Data passed is not an instance of CloudObject or CloudFile or CloudGeoPoint";if(obj instanceof _CB2.default.Column)return obj.document;if(obj instanceof _CB2.default.CloudGeoPoint)return obj.document;var doc=obj.document;for(var key in doc)if(doc[key]instanceof _CB2.default.CloudObject||doc[key]instanceof _CB2.default.CloudFile||doc[key]instanceof _CB2.default.CloudGeoPoint||doc[key]instanceof _CB2.default.Column)doc[key]=_CB2.default.toJSON(doc[key]);else if("ACL"===key){var acl=doc[key].document;doc[key]=acl}else if(doc[key]instanceof Array&&doc[key][0]&&(doc[key][0]instanceof _CB2.default.CloudObject||doc[key][0]instanceof _CB2.default.CloudFile||doc[key][0]instanceof _CB2.default.CloudGeoPoint||doc[key][0]instanceof _CB2.default.Column)){for(var arr=[],i=0;i<doc[key].length;i++)arr.push(_CB2.default.toJSON(doc[key][i]));doc[key]=arr}return doc}catch(error){return console.error(error,thisObj),null}},_CB2.default.fromJSON=function(data,thisObj){try{if(!data||""===data)return null;if(data instanceof Array){if(data[0]&&data[0]instanceof Object){for(var arr=[],i=0;i<data.length;i++)obj=_CB2.default.fromJSON(data[i]),arr.push(obj);return arr}return data}if(data instanceof Object&&data._type){var document={};for(var key in data)data[key]instanceof Array?document[key]=_CB2.default.fromJSON(data[key]):data[key]instanceof Object?"ACL"===key?(document[key]=new _CB2.default.ACL,document[key].document=data[key]):data[key]._type?thisObj?document[key]=_CB2.default.fromJSON(data[key],thisObj.get(key)):document[key]=_CB2.default.fromJSON(data[key]):document[key]=data[key]:document[key]=data[key];var id=thisObj;if(thisObj instanceof Object&&(id=thisObj._id||thisObj.id),thisObj&&data._id!==id)thisObj.document=document;else{var id=null,latitude=null,longitude=null,name=null;"file"===document._type&&(id=document._id),"point"===document._type&&(latitude=document.latitude,longitude=document.longitude),"table"===document._type&&(name=document.name),"column"===document._type&&(name=document.name),"queue"===document._type&&(name=document.name),"cache"===document._type&&(name=document.name);var obj=_CB2.default._getObjectByType(document._type,id,longitude,latitude,name);obj.document=document,thisObj=obj}return(thisObj instanceof _CB2.default.CloudObject||thisObj instanceof _CB2.default.CloudUser||thisObj instanceof _CB2.default.CloudRole||thisObj instanceof _CB2.default.CloudFile)&&thisObj.document.ACL&&(thisObj.document.ACL.parent=thisObj),thisObj}return data}catch(error){return console.error(error,data.name),null}},_CB2.default._getObjectByType=function(type,id,longitude,latitude,name){var obj=null;return"custom"===type&&(obj=new _CB2.default.CloudObject),"role"===type&&(obj=new _CB2.default.CloudRole),"user"===type&&(obj=new _CB2.default.CloudUser),"file"===type&&(obj=new _CB2.default.CloudFile(id)),"point"===type&&(obj=new _CB2.default.CloudGeoPoint(0,0),obj.document.latitude=Number(latitude),obj.document.longitude=Number(longitude)),"table"===type&&(obj=new _CB2.default.CloudTable(name)),"column"===type&&(obj=new _CB2.default.Column(name)),obj},_CB2.default._validate=function(){if(!_CB2.default.appId)throw"AppID is null. Please use CB.CloudApp.init to initialize your app.";if(!_CB2.default.appKey)throw"AppKey is null. Please use CB.CloudApp.init to initialize your app."},_CB2.default._clone=function(obj,id,longitude,latitude,name){var n_obj={};if(obj.document._type&&"point"!=obj.document._type){n_obj=_CB2.default._getObjectByType(obj.document._type,id,longitude,latitude,name);var doc=obj.document,doc2={};for(var key in doc)doc[key]instanceof _CB2.default.CloudFile?doc2[key]=_CB2.default._clone(doc[key],doc[key].document._id):doc[key]instanceof _CB2.default.CloudObject?doc2[key]=_CB2.default._clone(doc[key],null):doc[key]instanceof _CB2.default.CloudGeoPoint?doc2[key]=_CB2.default._clone(doc[key],null):doc2[key]=doc[key]}else if(obj instanceof _CB2.default.CloudGeoPoint)return n_obj=new _CB2.default.CloudGeoPoint(obj.get("longitude"),obj.get("latitude"));return n_obj.document=doc2,n_obj},_CB2.default._request=function(method,url,params,isServiceUrl,isFile,progressCallback){if(_CB2.default._validate(),!_CB2.default.CloudApp._isConnected)throw"Your CloudApp is disconnected. Please use CB.CloudApp.connect() and try again.";var Axios,def=new _CB2.default.Promise,headers={},axiosRetry=__webpack_require__(46);if(Axios=__webpack_require__(_CB2.default._isNode?49:50),!isServiceUrl){var ssid=_CB2.default._getSessionId();null!=ssid&&(headers.sessionID=ssid)}return params&&"object"!=("undefined"==typeof params?"undefined":_typeof(params))&&(params=JSON.parse(params)),axiosRetry(Axios,{retryDelay:axiosRetry.exponentialDelay}),Axios({method:method,url:url,data:params,headers:headers,onUploadProgress:function(event){if(event.lengthComputable){var percentComplete=event.loaded/event.total;progressCallback&&progressCallback(percentComplete)}}}).then(function(res){if(!isServiceUrl){var sessionID=res.headers.sessionid;sessionID?localStorage.setItem("sessionID",sessionID):localStorage.removeItem("sessionID")}def.resolve(JSON.stringify(res.data))},function(err){def.reject(err)}),def.promise},_CB2.default._getSessionId=function(){return localStorage.getItem("sessionID")},_CB2.default._columnValidation=function(column,cloudtable){var defaultColumn=["id","createdAt","updatedAt","ACL"];"user"==cloudtable.document.type?defaultColumn.concat(["username","email","password","roles"]):"role"==cloudtable.document.type&&defaultColumn.push("name");var index=defaultColumn.indexOf(column.name.toLowerCase());return index===-1},_CB2.default._tableValidation=function(tableName){if(!tableName)throw"table name cannot be empty";if(!isNaN(tableName[0]))throw"table name should not start with a number";if(!tableName.match(/^\S+$/))throw"table name should not contain spaces";var pattern=new RegExp(/[~`!#$%\^&*+=\-\[\]\\';,\/{}|\\":<>\?]/);if(pattern.test(tableName))throw"table not shoul not contain special characters"},_CB2.default._modified=function(thisObj,columnName){thisObj.document._isModified=!0,thisObj.document._modifiedColumns?thisObj.document._modifiedColumns.indexOf(columnName)===-1&&thisObj.document._modifiedColumns.push(columnName):(thisObj.document._modifiedColumns=[],thisObj.document._modifiedColumns.push(columnName))},_CB2.default._columnNameValidation=function(columnName){if(!columnName)throw"table name cannot be empty";if(!isNaN(columnName[0]))throw"column name should not start with a number";if(!columnName.match(/^\S+$/))throw"column name should not contain spaces";var pattern=new RegExp(/[~`!#$%\^&*+=\-\[\]\\';,\/{}|\\":<>\?]/);if(pattern.test(columnName))throw"column name not should not contain special characters"},_CB2.default._columnDataTypeValidation=function(dataType){if(!dataType)throw"data type cannot be empty";var dataTypeList=["Text","Email","URL","Number","Boolean","DateTime","GeoPoint","File","List","Relation","Object","EncryptedText"],index=dataTypeList.indexOf(dataType);if(index<0)throw"invalid data type"},_CB2.default._defaultColumns=function(type){var id=new _CB2.default.Column("id");id.dataType="Id",id.required=!0,id.unique=!0,id.document.isDeletable=!1,id.document.isEditable=!1;var expires=new _CB2.default.Column("expires");expires.dataType="DateTime",expires.document.isDeletable=!1,expires.document.isEditable=!1;var createdAt=new _CB2.default.Column("createdAt");createdAt.dataType="DateTime",createdAt.required=!0,createdAt.document.isDeletable=!1,createdAt.document.isEditable=!1;var updatedAt=new _CB2.default.Column("updatedAt");updatedAt.dataType="DateTime",updatedAt.required=!0,updatedAt.document.isDeletable=!1,updatedAt.document.isEditable=!1;var ACL=new _CB2.default.Column("ACL");ACL.dataType="ACL",ACL.required=!0,ACL.document.isDeletable=!1,ACL.document.isEditable=!1;var col=[id,expires,updatedAt,createdAt,ACL];if("custom"===type)return col;if("user"===type){var username=new _CB2.default.Column("username");username.dataType="Text",username.required=!1,username.unique=!0,username.document.isDeletable=!1,username.document.isEditable=!1;var email=new _CB2.default.Column("email");email.dataType="Email",email.unique=!0,email.document.isDeletable=!1,email.document.isEditable=!1;var password=new _CB2.default.Column("password");password.dataType="EncryptedText",password.required=!1,password.document.isDeletable=!1,password.document.isEditable=!1;var roles=new _CB2.default.Column("roles");roles.dataType="List",roles.relatedTo="Role",roles.relatedToType="role",roles.document.relationType="table",roles.document.isDeletable=!1,roles.document.isEditable=!1;var socialAuth=new _CB2.default.Column("socialAuth");socialAuth.dataType="List",socialAuth.relatedTo="Object",socialAuth.required=!1,socialAuth.document.isDeletable=!1,socialAuth.document.isEditable=!1;var verified=new _CB2.default.Column("verified");return verified.dataType="Boolean",verified.required=!1,verified.document.isDeletable=!1,verified.document.isEditable=!1,col.push(username),col.push(roles),col.push(password),col.push(email),col.push(socialAuth),col.push(verified),col}if("role"===type){var name=new _CB2.default.Column("name");return name.dataType="Text",name.unique=!0,name.required=!0,name.document.isDeletable=!1,name.document.isEditable=!1,col.push(name),col}if("device"===type){var channels=new _CB2.default.Column("channels");channels.dataType="List",channels.relatedTo="Text",channels.document.isDeletable=!1,channels.document.isEditable=!1;var deviceToken=new _CB2.default.Column("deviceToken");deviceToken.dataType="Text",deviceToken.unique=!0,deviceToken.document.isDeletable=!1,deviceToken.document.isEditable=!1;var deviceOS=new _CB2.default.Column("deviceOS");deviceOS.dataType="Text",deviceOS.document.isDeletable=!1,deviceOS.document.isEditable=!1;var timezone=new _CB2.default.Column("timezone");timezone.dataType="Text",timezone.document.isDeletable=!1,timezone.document.isEditable=!1;var metadata=new _CB2.default.Column("metadata");return metadata.dataType="Object",metadata.document.isDeletable=!1,metadata.document.isEditable=!1,col.push(channels),col.push(deviceToken),col.push(deviceOS),col.push(timezone),col.push(metadata),col}},_CB2.default._fileCheck=function(obj){var deferred=new _CB2.default.Promise,promises=[];for(var key in obj.document)if(obj.document[key]instanceof Array&&obj.document[key][0]instanceof _CB2.default.CloudFile)for(var i=0;i<obj.document[key].length;i++)obj.document[key][i].id||promises.push(obj.document[key][i].save());else obj.document[key]instanceof Object&&obj.document[key]instanceof _CB2.default.CloudFile&&(obj.document[key].id||promises.push(obj.document[key].save()));return promises.length>0?_CB2.default.Promise.all(promises).then(function(){var res=arguments,j=0;for(var key in obj.document)if(obj.document[key]instanceof Array&&obj.document[key][0]instanceof _CB2.default.CloudFile)for(var i=0;i<obj.document[key].length;i++)obj.document[key][i].id||(obj.document[key][i]=res[j],j+=1);else obj.document[key]instanceof Object&&obj.document[key]instanceof _CB2.default.CloudFile&&(obj.document[key].id||(obj.document[key]=res[j],j+=1));deferred.resolve(obj)},function(err){deferred.reject(err)}):deferred.resolve(obj),deferred.promise},_CB2.default._bulkObjFileCheck=function(array){for(var deferred=new _CB2.default.Promise,promises=[],i=0;i<array.length;i++)promises.push(_CB2.default._fileCheck(array[i]));return _CB2.default.Promise.all(promises).then(function(){deferred.resolve(arguments)},function(err){deferred.reject(err)}),deferred.promise},_CB2.default._generateHash=function(){for(var hash="",possible="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",i=0;i<8;i++)hash+=possible.charAt(Math.floor(Math.random()*possible.length));return hash},_CB2.default._isJsonString=function(str){try{JSON.parse(str)}catch(e){return!1}return!0},_CB2.default._isJsonObject=function(obj){try{JSON.stringify(obj)}catch(e){return!1}return!0},_CB2.default._getCookie=function(name){if("undefined"!=typeof Storage){if(new Date(localStorage.getItem(name+"_expires"))>new Date)return localStorage.getItem(name);_CB2.default._deleteCookie(name)}else if("undefined"!=typeof document){for(var name=name+"=",ca=document.cookie.split(";"),i=0;i<ca.length;i++){for(var c=ca[i];" "==c.charAt(0);)c=c.substring(1);if(0==c.indexOf(name))return c.substring(name.length,c.length)}return""}},_CB2.default._deleteCookie=function(name){if("undefined"!=typeof Storage)localStorage.removeItem(name),localStorage.removeItem(name+"_expires");else if("undefined"!=typeof document){var d=new Date;d.setTime(d.getTime()+0);var expires="expires="+d.toUTCString();document.cookie=name+"=NaN"+expires}},_CB2.default._createCookie=function(name,content,expires){var d=new Date;if(d.setTime(d.getTime()+expires),"undefined"!=typeof Storage)localStorage.setItem(name,content.toString()),localStorage.setItem(name+"_expires",d);else if("undefined"!=typeof document){var expires="expires="+d.toUTCString();document.cookie=+name+"="+content.toString()+"; "+expires}},_CB2.default._getQuerystringByKey=function(key){key=key.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");var regex=new RegExp("[\\?&]"+key+"=([^&#]*)"),results=regex.exec(location.search);return null===results?"":decodeURIComponent(results[1].replace(/\+/g," "))},"undefined"!=typeof location&&location.search){var cbtoken=_CB2.default._getQuerystringByKey("cbtoken");cbtoken&&""!==cbtoken&&localStorage.setItem("sessionID",cbtoken)}_CB2.default._getThisBrowserName=function(){if("undefined"!=typeof window){var userAgent="navigator"in window&&"userAgent"in navigator&&navigator.userAgent.toLowerCase()||"",vendor="navigator"in window&&"vendor"in navigator&&navigator.vendor.toLowerCase()||"",is=("navigator"in window&&"appVersion"in navigator&&navigator.appVersion.toLowerCase()||"",{});return is.chrome=function(){return/chrome|chromium/i.test(userAgent)&&/google inc/.test(vendor)},is.firefox=function(){return/firefox/i.test(userAgent)},is.edge=function(){return/edge/i.test(userAgent)},is.ie=function(version){return version?version>=11?"ActiveXObject"in window:new RegExp("msie "+version).test(userAgent):/msie/i.test(userAgent)||"ActiveXObject"in window},is.opera=function(){return/^Opera\//.test(userAgent)||/\x20OPR\//.test(userAgent)},is.safari=function(){return/safari/i.test(userAgent)&&/apple computer/i.test(vendor)},is.chrome()?"chrome":is.firefox()?"firefox":is.edge()?"edge":is.ie()?"ie":is.opera()?"opera":is.safari()?"safari":"unidentified"}},exports.default=!0},function(module,exports,__webpack_require__){(function(global,process){function inspect(obj,opts){var ctx={seen:[],stylize:stylizeNoColor};return arguments.length>=3&&(ctx.depth=arguments[2]),arguments.length>=4&&(ctx.colors=arguments[3]),isBoolean(opts)?ctx.showHidden=opts:opts&&exports._extend(ctx,opts),isUndefined(ctx.showHidden)&&(ctx.showHidden=!1),isUndefined(ctx.depth)&&(ctx.depth=2),isUndefined(ctx.colors)&&(ctx.colors=!1),isUndefined(ctx.customInspect)&&(ctx.customInspect=!0),ctx.colors&&(ctx.stylize=stylizeWithColor),formatValue(ctx,obj,ctx.depth)}function stylizeWithColor(str,styleType){var style=inspect.styles[styleType];return style?"["+inspect.colors[style][0]+"m"+str+"["+inspect.colors[style][1]+"m":str}function stylizeNoColor(str,styleType){return str}function arrayToHash(array){var hash={};return array.forEach(function(val,idx){hash[val]=!0}),hash}function formatValue(ctx,value,recurseTimes){if(ctx.customInspect&&value&&isFunction(value.inspect)&&value.inspect!==exports.inspect&&(!value.constructor||value.constructor.prototype!==value)){var ret=value.inspect(recurseTimes,ctx);return isString(ret)||(ret=formatValue(ctx,ret,recurseTimes)),ret}var primitive=formatPrimitive(ctx,value);if(primitive)return primitive;var keys=Object.keys(value),visibleKeys=arrayToHash(keys);if(ctx.showHidden&&(keys=Object.getOwnPropertyNames(value)),isError(value)&&(keys.indexOf("message")>=0||keys.indexOf("description")>=0))return formatError(value);if(0===keys.length){if(isFunction(value)){var name=value.name?": "+value.name:"";return ctx.stylize("[Function"+name+"]","special")}if(isRegExp(value))return ctx.stylize(RegExp.prototype.toString.call(value),"regexp");if(isDate(value))return ctx.stylize(Date.prototype.toString.call(value),"date");if(isError(value))return formatError(value)}var base="",array=!1,braces=["{","}"];if(isArray(value)&&(array=!0,braces=["[","]"]),isFunction(value)){var n=value.name?": "+value.name:"";base=" [Function"+n+"]"}if(isRegExp(value)&&(base=" "+RegExp.prototype.toString.call(value)),isDate(value)&&(base=" "+Date.prototype.toUTCString.call(value)),isError(value)&&(base=" "+formatError(value)),0===keys.length&&(!array||0==value.length))return braces[0]+base+braces[1];if(recurseTimes<0)return isRegExp(value)?ctx.stylize(RegExp.prototype.toString.call(value),"regexp"):ctx.stylize("[Object]","special");ctx.seen.push(value);var output;return output=array?formatArray(ctx,value,recurseTimes,visibleKeys,keys):keys.map(function(key){return formatProperty(ctx,value,recurseTimes,visibleKeys,key,array)}),ctx.seen.pop(),reduceToSingleString(output,base,braces)}function formatPrimitive(ctx,value){if(isUndefined(value))return ctx.stylize("undefined","undefined");if(isString(value)){var simple="'"+JSON.stringify(value).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return ctx.stylize(simple,"string")}return isNumber(value)?ctx.stylize(""+value,"number"):isBoolean(value)?ctx.stylize(""+value,"boolean"):isNull(value)?ctx.stylize("null","null"):void 0}function formatError(value){return"["+Error.prototype.toString.call(value)+"]"}function formatArray(ctx,value,recurseTimes,visibleKeys,keys){for(var output=[],i=0,l=value.length;i<l;++i)hasOwnProperty(value,String(i))?output.push(formatProperty(ctx,value,recurseTimes,visibleKeys,String(i),!0)):output.push("");return keys.forEach(function(key){key.match(/^\d+$/)||output.push(formatProperty(ctx,value,recurseTimes,visibleKeys,key,!0))}),output}function formatProperty(ctx,value,recurseTimes,visibleKeys,key,array){var name,str,desc;if(desc=Object.getOwnPropertyDescriptor(value,key)||{value:value[key]},desc.get?str=desc.set?ctx.stylize("[Getter/Setter]","special"):ctx.stylize("[Getter]","special"):desc.set&&(str=ctx.stylize("[Setter]","special")),hasOwnProperty(visibleKeys,key)||(name="["+key+"]"),str||(ctx.seen.indexOf(desc.value)<0?(str=isNull(recurseTimes)?formatValue(ctx,desc.value,null):formatValue(ctx,desc.value,recurseTimes-1),str.indexOf("\n")>-1&&(str=array?str.split("\n").map(function(line){return"  "+line}).join("\n").substr(2):"\n"+str.split("\n").map(function(line){return"   "+line}).join("\n"))):str=ctx.stylize("[Circular]","special")),isUndefined(name)){if(array&&key.match(/^\d+$/))return str;name=JSON.stringify(""+key),name.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(name=name.substr(1,name.length-2),name=ctx.stylize(name,"name")):(name=name.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),name=ctx.stylize(name,"string"))}return name+": "+str}function reduceToSingleString(output,base,braces){var numLinesEst=0,length=output.reduce(function(prev,cur){return numLinesEst++,cur.indexOf("\n")>=0&&numLinesEst++,prev+cur.replace(/\u001b\[\d\d?m/g,"").length+1},0);return length>60?braces[0]+(""===base?"":base+"\n ")+" "+output.join(",\n  ")+" "+braces[1]:braces[0]+base+" "+output.join(", ")+" "+braces[1]}function isArray(ar){return Array.isArray(ar)}function isBoolean(arg){return"boolean"==typeof arg}function isNull(arg){return null===arg}function isNullOrUndefined(arg){return null==arg}function isNumber(arg){return"number"==typeof arg}function isString(arg){return"string"==typeof arg}function isSymbol(arg){return"symbol"==typeof arg}function isUndefined(arg){return void 0===arg}function isRegExp(re){return isObject(re)&&"[object RegExp]"===objectToString(re)}function isObject(arg){return"object"==typeof arg&&null!==arg}function isDate(d){return isObject(d)&&"[object Date]"===objectToString(d)}function isError(e){return isObject(e)&&("[object Error]"===objectToString(e)||e instanceof Error)}function isFunction(arg){return"function"==typeof arg}function isPrimitive(arg){return null===arg||"boolean"==typeof arg||"number"==typeof arg||"string"==typeof arg||"symbol"==typeof arg||"undefined"==typeof arg}function objectToString(o){return Object.prototype.toString.call(o)}function pad(n){return n<10?"0"+n.toString(10):n.toString(10)}function timestamp(){var d=new Date,time=[pad(d.getHours()),pad(d.getMinutes()),pad(d.getSeconds())].join(":");return[d.getDate(),months[d.getMonth()],time].join(" ")}function hasOwnProperty(obj,prop){return Object.prototype.hasOwnProperty.call(obj,prop)}var formatRegExp=/%[sdj%]/g;exports.format=function(f){if(!isString(f)){for(var objects=[],i=0;i<arguments.length;i++)objects.push(inspect(arguments[i]));return objects.join(" ")}for(var i=1,args=arguments,len=args.length,str=String(f).replace(formatRegExp,function(x){if("%%"===x)return"%";if(i>=len)return x;switch(x){case"%s":return String(args[i++]);case"%d":return Number(args[i++]);case"%j":try{return JSON.stringify(args[i++])}catch(_){return"[Circular]"}default:return x}}),x=args[i];i<len;x=args[++i])str+=isNull(x)||!isObject(x)?" "+x:" "+inspect(x);return str},exports.deprecate=function(fn,msg){function deprecated(){if(!warned){if(process.throwDeprecation)throw new Error(msg);process.traceDeprecation?console.trace(msg):console.error(msg),warned=!0}return fn.apply(this,arguments)}if(isUndefined(global.process))return function(){return exports.deprecate(fn,msg).apply(this,arguments)};if(process.noDeprecation===!0)return fn;var warned=!1;return deprecated};var debugEnviron,debugs={};exports.debuglog=function(set){if(isUndefined(debugEnviron)&&(debugEnviron={NODE_ENV:"production"}.NODE_DEBUG||""),set=set.toUpperCase(),!debugs[set])if(new RegExp("\\b"+set+"\\b","i").test(debugEnviron)){var pid=process.pid;debugs[set]=function(){var msg=exports.format.apply(exports,arguments);console.error("%s %d: %s",set,pid,msg)}}else debugs[set]=function(){};return debugs[set]},exports.inspect=inspect,inspect.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},inspect.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},exports.isArray=isArray,exports.isBoolean=isBoolean,exports.isNull=isNull,exports.isNullOrUndefined=isNullOrUndefined,exports.isNumber=isNumber,exports.isString=isString,exports.isSymbol=isSymbol,exports.isUndefined=isUndefined,exports.isRegExp=isRegExp,exports.isObject=isObject,exports.isDate=isDate,exports.isError=isError,exports.isFunction=isFunction,exports.isPrimitive=isPrimitive,exports.isBuffer=__webpack_require__(43);var months=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];exports.log=function(){console.log("%s - %s",timestamp(),exports.format.apply(exports,arguments))},exports.inherits=__webpack_require__(44),exports._extend=function(origin,add){if(!add||!isObject(add))return origin;for(var keys=Object.keys(add),i=keys.length;i--;)origin[keys[i]]=add[keys[i]];return origin}}).call(exports,function(){return this}(),__webpack_require__(2))},function(module,exports){module.exports=function(arg){return arg&&"object"==typeof arg&&"function"==typeof arg.copy&&"function"==typeof arg.fill&&"function"==typeof arg.readUInt8}},function(module,exports){"function"==typeof Object.create?module.exports=function(ctor,superCtor){ctor.super_=superCtor,ctor.prototype=Object.create(superCtor.prototype,{constructor:{value:ctor,enumerable:!1,writable:!0,configurable:!0}})}:module.exports=function(ctor,superCtor){ctor.super_=superCtor;
var TempCtor=function(){};TempCtor.prototype=superCtor.prototype,ctor.prototype=new TempCtor,ctor.prototype.constructor=ctor}},function(module,exports){(function(global){!function(){"use strict";function LocalStorage(){}var db;db=LocalStorage,db.prototype.getItem=function(key){return this.hasOwnProperty(key)?String(this[key]):null},db.prototype.setItem=function(key,val){this[key]=String(val)},db.prototype.removeItem=function(key){delete this[key]},db.prototype.clear=function(){var self=this;Object.keys(self).forEach(function(key){self[key]=void 0,delete self[key]})},db.prototype.key=function(i){return i=i||0,Object.keys(this)[i]},db.prototype.__defineGetter__("length",function(){return Object.keys(this).length}),global.localStorage?module.exports=localStorage:module.exports=new LocalStorage}()}).call(exports,function(){return this}())},function(module,exports,__webpack_require__){module.exports=__webpack_require__(47).default},function(module,exports,__webpack_require__){"use strict";function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function isNetworkError(error){return!error.response&&Boolean(error.code)&&"ECONNABORTED"!==error.code&&(0,_isRetryAllowed2.default)(error)}function isRetryableError(error){return"ECONNABORTED"!==error.code&&(!error.response||error.response.status>=500&&error.response.status<=599)}function isSafeRequestError(error){return!!error.config&&(isRetryableError(error)&&SAFE_HTTP_METHODS.indexOf(error.config.method)!==-1)}function isIdempotentRequestError(error){return!!error.config&&(isRetryableError(error)&&IDEMPOTENT_HTTP_METHODS.indexOf(error.config.method)!==-1)}function isNetworkOrIdempotentRequestError(error){return isNetworkError(error)||isIdempotentRequestError(error)}function noDelay(){return 0}function exponentialDelay(){var retryNumber=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,delay=100*Math.pow(2,retryNumber),randomSum=.2*delay*Math.random();return delay+randomSum}function getCurrentState(config){var currentState=config[namespace]||{};return currentState.retryCount=currentState.retryCount||0,config[namespace]=currentState,currentState}function getRequestOptions(config,defaultOptions){return Object.assign({},defaultOptions,config[namespace])}function fixConfig(axios,config){axios.defaults.agent===config.agent&&delete config.agent,axios.defaults.httpAgent===config.httpAgent&&delete config.httpAgent,axios.defaults.httpsAgent===config.httpsAgent&&delete config.httpsAgent}function axiosRetry(axios,defaultOptions){axios.interceptors.request.use(function(config){var currentState=getCurrentState(config);return currentState.lastRequestTime=Date.now(),config}),axios.interceptors.response.use(null,function(error){var config=error.config;if(!config)return Promise.reject(error);var _getRequestOptions=getRequestOptions(config,defaultOptions),_getRequestOptions$re=_getRequestOptions.retries,retries=void 0===_getRequestOptions$re?3:_getRequestOptions$re,_getRequestOptions$re2=_getRequestOptions.retryCondition,retryCondition=void 0===_getRequestOptions$re2?isNetworkOrIdempotentRequestError:_getRequestOptions$re2,_getRequestOptions$re3=_getRequestOptions.retryDelay,retryDelay=void 0===_getRequestOptions$re3?noDelay:_getRequestOptions$re3,_getRequestOptions$sh=_getRequestOptions.shouldResetTimeout,shouldResetTimeout=void 0!==_getRequestOptions$sh&&_getRequestOptions$sh,currentState=getCurrentState(config),shouldRetry=retryCondition(error)&&currentState.retryCount<retries;if(shouldRetry){currentState.retryCount+=1;var delay=retryDelay(currentState.retryCount,error);if(fixConfig(axios,config),!shouldResetTimeout&&config.timeout&&currentState.lastRequestTime){var lastRequestDuration=Date.now()-currentState.lastRequestTime;config.timeout=Math.max(config.timeout-lastRequestDuration-delay,1)}return config.transformRequest=[function(data){return data}],new Promise(function(resolve){return setTimeout(function(){return resolve(axios(config))},delay)})}return Promise.reject(error)})}Object.defineProperty(exports,"__esModule",{value:!0}),exports.isNetworkError=isNetworkError,exports.isRetryableError=isRetryableError,exports.isSafeRequestError=isSafeRequestError,exports.isIdempotentRequestError=isIdempotentRequestError,exports.isNetworkOrIdempotentRequestError=isNetworkOrIdempotentRequestError,exports.exponentialDelay=exponentialDelay,exports.default=axiosRetry;var _isRetryAllowed=__webpack_require__(48),_isRetryAllowed2=_interopRequireDefault(_isRetryAllowed),namespace="axios-retry",SAFE_HTTP_METHODS=["get","head","options"],IDEMPOTENT_HTTP_METHODS=SAFE_HTTP_METHODS.concat(["put","delete"]);axiosRetry.isNetworkError=isNetworkError,axiosRetry.isSafeRequestError=isSafeRequestError,axiosRetry.isIdempotentRequestError=isIdempotentRequestError,axiosRetry.isNetworkOrIdempotentRequestError=isNetworkOrIdempotentRequestError,axiosRetry.exponentialDelay=exponentialDelay,axiosRetry.isRetryableError=isRetryableError},function(module,exports){"use strict";var WHITELIST=["ETIMEDOUT","ECONNRESET","EADDRINUSE","ESOCKETTIMEDOUT","ECONNREFUSED","EPIPE"],BLACKLIST=["ENOTFOUND","ENETUNREACH","UNABLE_TO_GET_ISSUER_CERT","UNABLE_TO_GET_CRL","UNABLE_TO_DECRYPT_CERT_SIGNATURE","UNABLE_TO_DECRYPT_CRL_SIGNATURE","UNABLE_TO_DECODE_ISSUER_PUBLIC_KEY","CERT_SIGNATURE_FAILURE","CRL_SIGNATURE_FAILURE","CERT_NOT_YET_VALID","CERT_HAS_EXPIRED","CRL_NOT_YET_VALID","CRL_HAS_EXPIRED","ERROR_IN_CERT_NOT_BEFORE_FIELD","ERROR_IN_CERT_NOT_AFTER_FIELD","ERROR_IN_CRL_LAST_UPDATE_FIELD","ERROR_IN_CRL_NEXT_UPDATE_FIELD","OUT_OF_MEM","DEPTH_ZERO_SELF_SIGNED_CERT","SELF_SIGNED_CERT_IN_CHAIN","UNABLE_TO_GET_ISSUER_CERT_LOCALLY","UNABLE_TO_VERIFY_LEAF_SIGNATURE","CERT_CHAIN_TOO_LONG","CERT_REVOKED","INVALID_CA","PATH_LENGTH_EXCEEDED","INVALID_PURPOSE","CERT_UNTRUSTED","CERT_REJECTED"];module.exports=function(err){return!err||!err.code||(WHITELIST.indexOf(err.code)!==-1||BLACKLIST.indexOf(err.code)===-1)}},function(module,exports){module.exports=__WEBPACK_EXTERNAL_MODULE_49__},function(module,exports,__webpack_require__){module.exports=__webpack_require__(51)},function(module,exports,__webpack_require__){"use strict";function createInstance(defaultConfig){var context=new Axios(defaultConfig),instance=bind(Axios.prototype.request,context);return utils.extend(instance,Axios.prototype,context),utils.extend(instance,context),instance}var utils=__webpack_require__(52),bind=__webpack_require__(53),Axios=__webpack_require__(54),axios=createInstance();axios.Axios=Axios,axios.create=function(defaultConfig){return createInstance(defaultConfig)},axios.all=function(promises){return Promise.all(promises)},axios.spread=__webpack_require__(71),module.exports=axios,module.exports.default=axios},function(module,exports,__webpack_require__){"use strict";function isArray(val){return"[object Array]"===toString.call(val)}function isArrayBuffer(val){return"[object ArrayBuffer]"===toString.call(val)}function isFormData(val){return"undefined"!=typeof FormData&&val instanceof FormData}function isArrayBufferView(val){var result;return result="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(val):val&&val.buffer&&val.buffer instanceof ArrayBuffer}function isString(val){return"string"==typeof val}function isNumber(val){return"number"==typeof val}function isUndefined(val){return"undefined"==typeof val}function isObject(val){return null!==val&&"object"==typeof val}function isDate(val){return"[object Date]"===toString.call(val)}function isFile(val){return"[object File]"===toString.call(val)}function isBlob(val){return"[object Blob]"===toString.call(val)}function isFunction(val){return"[object Function]"===toString.call(val)}function isStream(val){return isObject(val)&&isFunction(val.pipe)}function isURLSearchParams(val){return"undefined"!=typeof URLSearchParams&&val instanceof URLSearchParams}function trim(str){return str.replace(/^\s*/,"").replace(/\s*$/,"")}function isStandardBrowserEnv(){return"undefined"!=typeof window&&"undefined"!=typeof document&&"function"==typeof document.createElement}function forEach(obj,fn){if(null!==obj&&"undefined"!=typeof obj)if("object"==typeof obj||isArray(obj)||(obj=[obj]),isArray(obj))for(var i=0,l=obj.length;i<l;i++)fn.call(null,obj[i],i,obj);else for(var key in obj)obj.hasOwnProperty(key)&&fn.call(null,obj[key],key,obj)}function merge(){function assignValue(val,key){"object"==typeof result[key]&&"object"==typeof val?result[key]=merge(result[key],val):result[key]=val}for(var result={},i=0,l=arguments.length;i<l;i++)forEach(arguments[i],assignValue);return result}function extend(a,b,thisArg){return forEach(b,function(val,key){thisArg&&"function"==typeof val?a[key]=bind(val,thisArg):a[key]=val}),a}var bind=__webpack_require__(53),toString=Object.prototype.toString;module.exports={isArray:isArray,isArrayBuffer:isArrayBuffer,isFormData:isFormData,isArrayBufferView:isArrayBufferView,isString:isString,isNumber:isNumber,isObject:isObject,isUndefined:isUndefined,isDate:isDate,isFile:isFile,isBlob:isBlob,isFunction:isFunction,isStream:isStream,isURLSearchParams:isURLSearchParams,isStandardBrowserEnv:isStandardBrowserEnv,forEach:forEach,merge:merge,extend:extend,trim:trim}},function(module,exports){"use strict";module.exports=function(fn,thisArg){return function(){for(var args=new Array(arguments.length),i=0;i<args.length;i++)args[i]=arguments[i];return fn.apply(thisArg,args)}}},function(module,exports,__webpack_require__){"use strict";function Axios(defaultConfig){this.defaults=utils.merge(defaults,defaultConfig),this.interceptors={request:new InterceptorManager,response:new InterceptorManager}}var defaults=__webpack_require__(55),utils=__webpack_require__(52),InterceptorManager=__webpack_require__(57),dispatchRequest=__webpack_require__(58),isAbsoluteURL=__webpack_require__(69),combineURLs=__webpack_require__(70);Axios.prototype.request=function(config){"string"==typeof config&&(config=utils.merge({url:arguments[0]},arguments[1])),config=utils.merge(defaults,this.defaults,{method:"get"},config),config.baseURL&&!isAbsoluteURL(config.url)&&(config.url=combineURLs(config.baseURL,config.url));var chain=[dispatchRequest,void 0],promise=Promise.resolve(config);for(this.interceptors.request.forEach(function(interceptor){chain.unshift(interceptor.fulfilled,interceptor.rejected)}),this.interceptors.response.forEach(function(interceptor){chain.push(interceptor.fulfilled,interceptor.rejected)});chain.length;)promise=promise.then(chain.shift(),chain.shift());return promise},utils.forEach(["delete","get","head"],function(method){Axios.prototype[method]=function(url,config){return this.request(utils.merge(config||{},{method:method,url:url}))}}),utils.forEach(["post","put","patch"],function(method){Axios.prototype[method]=function(url,data,config){return this.request(utils.merge(config||{},{method:method,url:url,data:data}))}}),module.exports=Axios},function(module,exports,__webpack_require__){"use strict";function setContentTypeIfUnset(headers,value){!utils.isUndefined(headers)&&utils.isUndefined(headers["Content-Type"])&&(headers["Content-Type"]=value)}var utils=__webpack_require__(52),normalizeHeaderName=__webpack_require__(56),PROTECTION_PREFIX=/^\)\]\}',?\n/,DEFAULT_CONTENT_TYPE={"Content-Type":"application/x-www-form-urlencoded"};module.exports={transformRequest:[function(data,headers){return normalizeHeaderName(headers,"Content-Type"),utils.isFormData(data)||utils.isArrayBuffer(data)||utils.isStream(data)||utils.isFile(data)||utils.isBlob(data)?data:utils.isArrayBufferView(data)?data.buffer:utils.isURLSearchParams(data)?(setContentTypeIfUnset(headers,"application/x-www-form-urlencoded;charset=utf-8"),data.toString()):utils.isObject(data)?(setContentTypeIfUnset(headers,"application/json;charset=utf-8"),JSON.stringify(data)):data}],transformResponse:[function(data){if("string"==typeof data){data=data.replace(PROTECTION_PREFIX,"");try{data=JSON.parse(data)}catch(e){}}return data}],headers:{common:{Accept:"application/json, text/plain, */*"},patch:utils.merge(DEFAULT_CONTENT_TYPE),post:utils.merge(DEFAULT_CONTENT_TYPE),put:utils.merge(DEFAULT_CONTENT_TYPE)},timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(status){return status>=200&&status<300}}},function(module,exports,__webpack_require__){"use strict";var utils=__webpack_require__(52);module.exports=function(headers,normalizedName){utils.forEach(headers,function(value,name){name!==normalizedName&&name.toUpperCase()===normalizedName.toUpperCase()&&(headers[normalizedName]=value,delete headers[name])})}},function(module,exports,__webpack_require__){"use strict";function InterceptorManager(){this.handlers=[]}var utils=__webpack_require__(52);InterceptorManager.prototype.use=function(fulfilled,rejected){return this.handlers.push({fulfilled:fulfilled,rejected:rejected}),this.handlers.length-1},InterceptorManager.prototype.eject=function(id){this.handlers[id]&&(this.handlers[id]=null)},InterceptorManager.prototype.forEach=function(fn){utils.forEach(this.handlers,function(h){null!==h&&fn(h)})},module.exports=InterceptorManager},function(module,exports,__webpack_require__){(function(process){"use strict";var utils=__webpack_require__(52),transformData=__webpack_require__(59);module.exports=function(config){config.headers=config.headers||{},config.data=transformData(config.data,config.headers,config.transformRequest),config.headers=utils.merge(config.headers.common||{},config.headers[config.method]||{},config.headers||{}),utils.forEach(["delete","get","head","post","put","patch","common"],function(method){delete config.headers[method]});var adapter;return"function"==typeof config.adapter?adapter=config.adapter:"undefined"!=typeof XMLHttpRequest?adapter=__webpack_require__(60):"undefined"!=typeof process&&(adapter=__webpack_require__(60)),Promise.resolve(config).then(adapter).then(function(response){return response.data=transformData(response.data,response.headers,config.transformResponse),response},function(error){return error&&error.response&&(error.response.data=transformData(error.response.data,error.response.headers,config.transformResponse)),Promise.reject(error)})}}).call(exports,__webpack_require__(2))},function(module,exports,__webpack_require__){"use strict";var utils=__webpack_require__(52);module.exports=function(data,headers,fns){return utils.forEach(fns,function(fn){data=fn(data,headers)}),data}},function(module,exports,__webpack_require__){"use strict";var utils=__webpack_require__(52),settle=__webpack_require__(61),buildURL=__webpack_require__(64),parseHeaders=__webpack_require__(65),isURLSameOrigin=__webpack_require__(66),createError=__webpack_require__(62),btoa="undefined"!=typeof window&&window.btoa||__webpack_require__(67);module.exports=function(config){return new Promise(function(resolve,reject){var requestData=config.data,requestHeaders=config.headers;utils.isFormData(requestData)&&delete requestHeaders["Content-Type"];var request=new XMLHttpRequest,loadEvent="onreadystatechange",xDomain=!1;if("undefined"==typeof window||!window.XDomainRequest||"withCredentials"in request||isURLSameOrigin(config.url)||(request=new window.XDomainRequest,loadEvent="onload",xDomain=!0,request.onprogress=function(){},request.ontimeout=function(){}),config.auth){var username=config.auth.username||"",password=config.auth.password||"";requestHeaders.Authorization="Basic "+btoa(username+":"+password)}if(request.open(config.method.toUpperCase(),buildURL(config.url,config.params,config.paramsSerializer),!0),request.timeout=config.timeout,request[loadEvent]=function(){if(request&&(4===request.readyState||xDomain)&&0!==request.status){var responseHeaders="getAllResponseHeaders"in request?parseHeaders(request.getAllResponseHeaders()):null,responseData=config.responseType&&"text"!==config.responseType?request.response:request.responseText,response={data:responseData,status:1223===request.status?204:request.status,statusText:1223===request.status?"No Content":request.statusText,headers:responseHeaders,config:config,request:request};settle(resolve,reject,response),request=null}},request.onerror=function(){reject(createError("Network Error",config)),request=null},request.ontimeout=function(){reject(createError("timeout of "+config.timeout+"ms exceeded",config,"ECONNABORTED")),request=null},utils.isStandardBrowserEnv()){var cookies=__webpack_require__(68),xsrfValue=(config.withCredentials||isURLSameOrigin(config.url))&&config.xsrfCookieName?cookies.read(config.xsrfCookieName):void 0;xsrfValue&&(requestHeaders[config.xsrfHeaderName]=xsrfValue)}if("setRequestHeader"in request&&utils.forEach(requestHeaders,function(val,key){"undefined"==typeof requestData&&"content-type"===key.toLowerCase()?delete requestHeaders[key]:request.setRequestHeader(key,val)}),config.withCredentials&&(request.withCredentials=!0),config.responseType)try{request.responseType=config.responseType}catch(e){if("json"!==request.responseType)throw e}"function"==typeof config.onDownloadProgress&&request.addEventListener("progress",config.onDownloadProgress),"function"==typeof config.onUploadProgress&&request.upload&&request.upload.addEventListener("progress",config.onUploadProgress),void 0===requestData&&(requestData=null),request.send(requestData)})}},function(module,exports,__webpack_require__){"use strict";var createError=__webpack_require__(62);module.exports=function(resolve,reject,response){var validateStatus=response.config.validateStatus;response.status&&validateStatus&&!validateStatus(response.status)?reject(createError("Request failed with status code "+response.status,response.config,null,response)):resolve(response)}},function(module,exports,__webpack_require__){"use strict";var enhanceError=__webpack_require__(63);module.exports=function(message,config,code,response){var error=new Error(message);return enhanceError(error,config,code,response)}},function(module,exports){"use strict";module.exports=function(error,config,code,response){return error.config=config,code&&(error.code=code),error.response=response,error}},function(module,exports,__webpack_require__){"use strict";function encode(val){return encodeURIComponent(val).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var utils=__webpack_require__(52);module.exports=function(url,params,paramsSerializer){if(!params)return url;var serializedParams;if(paramsSerializer)serializedParams=paramsSerializer(params);else if(utils.isURLSearchParams(params))serializedParams=params.toString();else{var parts=[];utils.forEach(params,function(val,key){null!==val&&"undefined"!=typeof val&&(utils.isArray(val)&&(key+="[]"),utils.isArray(val)||(val=[val]),utils.forEach(val,function(v){utils.isDate(v)?v=v.toISOString():utils.isObject(v)&&(v=JSON.stringify(v)),parts.push(encode(key)+"="+encode(v))}))}),serializedParams=parts.join("&")}return serializedParams&&(url+=(url.indexOf("?")===-1?"?":"&")+serializedParams),url}},function(module,exports,__webpack_require__){"use strict";var utils=__webpack_require__(52);module.exports=function(headers){var key,val,i,parsed={};return headers?(utils.forEach(headers.split("\n"),function(line){i=line.indexOf(":"),key=utils.trim(line.substr(0,i)).toLowerCase(),val=utils.trim(line.substr(i+1)),key&&(parsed[key]=parsed[key]?parsed[key]+", "+val:val)}),parsed):parsed}},function(module,exports,__webpack_require__){"use strict";var utils=__webpack_require__(52);module.exports=utils.isStandardBrowserEnv()?function(){function resolveURL(url){var href=url;return msie&&(urlParsingNode.setAttribute("href",href),href=urlParsingNode.href),urlParsingNode.setAttribute("href",href),{href:urlParsingNode.href,protocol:urlParsingNode.protocol?urlParsingNode.protocol.replace(/:$/,""):"",host:urlParsingNode.host,search:urlParsingNode.search?urlParsingNode.search.replace(/^\?/,""):"",hash:urlParsingNode.hash?urlParsingNode.hash.replace(/^#/,""):"",hostname:urlParsingNode.hostname,port:urlParsingNode.port,pathname:"/"===urlParsingNode.pathname.charAt(0)?urlParsingNode.pathname:"/"+urlParsingNode.pathname}}var originURL,msie=/(msie|trident)/i.test(navigator.userAgent),urlParsingNode=document.createElement("a");return originURL=resolveURL(window.location.href),function(requestURL){var parsed=utils.isString(requestURL)?resolveURL(requestURL):requestURL;return parsed.protocol===originURL.protocol&&parsed.host===originURL.host}}():function(){return function(){return!0}}()},function(module,exports){"use strict";function E(){this.message="String contains an invalid character"}function btoa(input){for(var block,charCode,str=String(input),output="",idx=0,map=chars;str.charAt(0|idx)||(map="=",idx%1);output+=map.charAt(63&block>>8-idx%1*8)){if(charCode=str.charCodeAt(idx+=.75),charCode>255)throw new E;block=block<<8|charCode}return output}var chars="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";E.prototype=new Error,E.prototype.code=5,E.prototype.name="InvalidCharacterError",module.exports=btoa},function(module,exports,__webpack_require__){"use strict";var utils=__webpack_require__(52);module.exports=utils.isStandardBrowserEnv()?function(){return{write:function(name,value,expires,path,domain,secure){var cookie=[];cookie.push(name+"="+encodeURIComponent(value)),utils.isNumber(expires)&&cookie.push("expires="+new Date(expires).toGMTString()),utils.isString(path)&&cookie.push("path="+path),utils.isString(domain)&&cookie.push("domain="+domain),secure===!0&&cookie.push("secure"),document.cookie=cookie.join("; ")},read:function(name){var match=document.cookie.match(new RegExp("(^|;\\s*)("+name+")=([^;]*)"));return match?decodeURIComponent(match[3]):null},remove:function(name){this.write(name,"",Date.now()-864e5)}}}():function(){return{write:function(){},read:function(){return null},remove:function(){}}}()},function(module,exports){"use strict";module.exports=function(url){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(url)}},function(module,exports){"use strict";module.exports=function(baseURL,relativeURL){return baseURL.replace(/\/+$/,"")+"/"+relativeURL.replace(/^\/+/,"")}},function(module,exports){"use strict";module.exports=function(callback){return function(arr){return callback.apply(null,arr)}}},function(module,exports,__webpack_require__){"use strict";function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor))throw new TypeError("Cannot call a class as a function")}function _confirmConnection(callback){var URL=_CB2.default.apiUrl+"/status";_CB2.default._request("GET",URL).then(function(res){_CB2.default.CloudApp._isConnected=!0},function(err){_CB2.default.CloudApp._isConnected=!1})}function stripTrailingSlash(url){return"/"==url[url.length-1]&&(url=url.split(""),url.splice(-1,1),url=url.join("")),url}function getUrlFromUri(url){var socketRelativeUrl=url;return socketRelativeUrl=socketRelativeUrl.replace("://",""),socketRelativeUrl=socketRelativeUrl.split("/"),socketRelativeUrl=socketRelativeUrl.filter(function(x){return x}),socketRelativeUrl.length>1?(socketRelativeUrl.splice(0,1,""),url=socketRelativeUrl.join("/")):url="",url}function getUrlWithoutNsc(uri,url){return""==url?uri:uri.replace(url,"")}Object.defineProperty(exports,"__esModule",{value:!0});var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(obj){return typeof obj}:function(obj){return obj&&"function"==typeof Symbol&&obj.constructor===Symbol&&obj!==Symbol.prototype?"symbol":typeof obj},_createClass=function(){function defineProperties(target,props){for(var i=0;i<props.length;i++){var descriptor=props[i];descriptor.enumerable=descriptor.enumerable||!1,descriptor.configurable=!0,"value"in descriptor&&(descriptor.writable=!0),Object.defineProperty(target,descriptor.key,descriptor)}}return function(Constructor,protoProps,staticProps){return protoProps&&defineProperties(Constructor.prototype,protoProps),staticProps&&defineProperties(Constructor,staticProps),Constructor}}(),_localforage=__webpack_require__(73),_CB=(_interopRequireDefault(_localforage),__webpack_require__(1)),_CB2=_interopRequireDefault(_CB),CloudApp=function(){function CloudApp(){_classCallCheck(this,CloudApp),this._isConnected=!1}return _createClass(CloudApp,[{key:"init",value:function(serverUrl,applicationId,applicationKey,opts){if(applicationKey?_CB2.default.apiUrl=stripTrailingSlash(serverUrl):(applicationKey=applicationId,applicationId=serverUrl),"object"===("undefined"==typeof applicationKey?"undefined":_typeof(applicationKey))&&(opts=applicationKey,applicationKey=applicationId,applicationId=serverUrl),_CB2.default.appId=applicationId,_CB2.default.appKey=applicationKey,opts&&opts.disableRealtime===!0)_CB2.default._isRealtimeDisabled=!0;else{var socketRelativeUrl=getUrlFromUri(_CB2.default.apiUrl),urlWithoutNamespace=getUrlWithoutNsc(_CB2.default.apiUrl,socketRelativeUrl);_CB2.default._isNode?(_CB2.default.io=__webpack_require__(74),_CB2.default.Socket=_CB2.default.io(urlWithoutNamespace,{jsonp:!1,transports:["websocket"],path:socketRelativeUrl+"/socket.io"})):(_CB2.default.io=__webpack_require__(75),_CB2.default.Socket=_CB2.default.io(urlWithoutNamespace,{path:socketRelativeUrl+"/socket.io"}))}_CB2.default.CloudApp._isConnected=!0,_confirmConnection(),_CB2.default._isRealtimeDisabled||(this.onConnect(function(){_CB2.default.CloudApp._isConnected=!0,_CB2.default.CloudObject.sync()}),this.onDisconnect(function(){_CB2.default.CloudApp._isConnected=!1}))}},{key:"onConnect",value:function(functionToFire){if(_CB2.default._validate(),!_CB2.default.Socket)throw"Socket couldn't be found. Init app first.";_CB2.default.Socket.on("connect",functionToFire)}},{key:"onDisconnect",value:function(functionToFire){if(_CB2.default._validate(),!_CB2.default.Socket)throw"Socket couldn't be found. Init app first.";_CB2.default.Socket.on("disconnect",functionToFire)}},{key:"connect",value:function(){if(_CB2.default._validate(),!_CB2.default.Socket)throw"Socket couldn't be found. Init app first.";_CB2.default.Socket.connect(),this._isConnected=!0}},{key:"disconnect",value:function(){if(_CB2.default._validate(),!_CB2.default.Socket)throw"Socket couldn't be found. Init app first.";_CB2.default.Socket.emit("socket-disconnect",_CB2.default.appId),this._isConnected=!1}}]),CloudApp}();Object.defineProperty(CloudApp.prototype,"isConnected",{get:function(){return this._isConnected}}),_CB2.default.CloudApp=new CloudApp,exports.default=CloudApp},function(module,exports){!function(f){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=f();else if("function"==typeof define&&define.amd)define([],f);else{var g;g="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,g.localforage=f()}}(function(){return function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a="function"==typeof require&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error("Cannot find module '"+o+"'");throw f.code="MODULE_NOT_FOUND",f}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}for(var i="function"==typeof require&&require,o=0;o<r.length;o++)s(r[o]);return s}({1:[function(_dereq_,module,exports){(function(global){"use strict";function nextTick(){draining=!0;for(var i,oldQueue,len=queue.length;len;){for(oldQueue=queue,queue=[],i=-1;++i<len;)oldQueue[i]();len=queue.length}draining=!1}function immediate(task){1!==queue.push(task)||draining||scheduleDrain()}var scheduleDrain,Mutation=global.MutationObserver||global.WebKitMutationObserver;if(Mutation){var called=0,observer=new Mutation(nextTick),element=global.document.createTextNode("");observer.observe(element,{characterData:!0}),scheduleDrain=function(){element.data=called=++called%2}}else if(global.setImmediate||"undefined"==typeof global.MessageChannel)scheduleDrain="document"in global&&"onreadystatechange"in global.document.createElement("script")?function(){var scriptEl=global.document.createElement("script");scriptEl.onreadystatechange=function(){nextTick(),scriptEl.onreadystatechange=null,scriptEl.parentNode.removeChild(scriptEl),scriptEl=null},global.document.documentElement.appendChild(scriptEl)}:function(){setTimeout(nextTick,0)};else{var channel=new global.MessageChannel;channel.port1.onmessage=nextTick,scheduleDrain=function(){channel.port2.postMessage(0)}}var draining,queue=[];module.exports=immediate}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],2:[function(_dereq_,module,exports){"use strict";function INTERNAL(){}function Promise(resolver){if("function"!=typeof resolver)throw new TypeError("resolver must be a function");this.state=PENDING,this.queue=[],this.outcome=void 0,resolver!==INTERNAL&&safelyResolveThenable(this,resolver)}function QueueItem(promise,onFulfilled,onRejected){this.promise=promise,"function"==typeof onFulfilled&&(this.onFulfilled=onFulfilled,this.callFulfilled=this.otherCallFulfilled),"function"==typeof onRejected&&(this.onRejected=onRejected,this.callRejected=this.otherCallRejected)}function unwrap(promise,func,value){immediate(function(){var returnValue;try{returnValue=func(value)}catch(e){return handlers.reject(promise,e)}returnValue===promise?handlers.reject(promise,new TypeError("Cannot resolve promise with itself")):handlers.resolve(promise,returnValue)})}function getThen(obj){var then=obj&&obj.then;if(obj&&("object"==typeof obj||"function"==typeof obj)&&"function"==typeof then)return function(){then.apply(obj,arguments)}}function safelyResolveThenable(self,thenable){function onError(value){called||(called=!0,handlers.reject(self,value))}function onSuccess(value){called||(called=!0,handlers.resolve(self,value))}function tryToUnwrap(){thenable(onSuccess,onError)}var called=!1,result=tryCatch(tryToUnwrap);"error"===result.status&&onError(result.value)}function tryCatch(func,value){var out={};try{out.value=func(value),out.status="success"}catch(e){out.status="error",out.value=e}return out}function resolve(value){return value instanceof this?value:handlers.resolve(new this(INTERNAL),value)}function reject(reason){var promise=new this(INTERNAL);return handlers.reject(promise,reason)}function all(iterable){function allResolver(value,i){function resolveFromAll(outValue){values[i]=outValue,++resolved!==len||called||(called=!0,handlers.resolve(promise,values))}self.resolve(value).then(resolveFromAll,function(error){called||(called=!0,handlers.reject(promise,error))})}var self=this;if("[object Array]"!==Object.prototype.toString.call(iterable))return this.reject(new TypeError("must be an array"));var len=iterable.length,called=!1;if(!len)return this.resolve([]);for(var values=new Array(len),resolved=0,i=-1,promise=new this(INTERNAL);++i<len;)allResolver(iterable[i],i);return promise}function race(iterable){function resolver(value){self.resolve(value).then(function(response){called||(called=!0,handlers.resolve(promise,response))},function(error){called||(called=!0,handlers.reject(promise,error))})}var self=this;if("[object Array]"!==Object.prototype.toString.call(iterable))return this.reject(new TypeError("must be an array"));var len=iterable.length,called=!1;if(!len)return this.resolve([]);for(var i=-1,promise=new this(INTERNAL);++i<len;)resolver(iterable[i]);return promise}var immediate=_dereq_(1),handlers={},REJECTED=["REJECTED"],FULFILLED=["FULFILLED"],PENDING=["PENDING"];module.exports=Promise,Promise.prototype.catch=function(onRejected){return this.then(null,onRejected)},Promise.prototype.then=function(onFulfilled,onRejected){if("function"!=typeof onFulfilled&&this.state===FULFILLED||"function"!=typeof onRejected&&this.state===REJECTED)return this;var promise=new this.constructor(INTERNAL);if(this.state!==PENDING){var resolver=this.state===FULFILLED?onFulfilled:onRejected;unwrap(promise,resolver,this.outcome)}else this.queue.push(new QueueItem(promise,onFulfilled,onRejected));
return promise},QueueItem.prototype.callFulfilled=function(value){handlers.resolve(this.promise,value)},QueueItem.prototype.otherCallFulfilled=function(value){unwrap(this.promise,this.onFulfilled,value)},QueueItem.prototype.callRejected=function(value){handlers.reject(this.promise,value)},QueueItem.prototype.otherCallRejected=function(value){unwrap(this.promise,this.onRejected,value)},handlers.resolve=function(self,value){var result=tryCatch(getThen,value);if("error"===result.status)return handlers.reject(self,result.value);var thenable=result.value;if(thenable)safelyResolveThenable(self,thenable);else{self.state=FULFILLED,self.outcome=value;for(var i=-1,len=self.queue.length;++i<len;)self.queue[i].callFulfilled(value)}return self},handlers.reject=function(self,error){self.state=REJECTED,self.outcome=error;for(var i=-1,len=self.queue.length;++i<len;)self.queue[i].callRejected(error);return self},Promise.resolve=resolve,Promise.reject=reject,Promise.all=all,Promise.race=race},{1:1}],3:[function(_dereq_,module,exports){(function(global){"use strict";"function"!=typeof global.Promise&&(global.Promise=_dereq_(2))}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{2:2}],4:[function(_dereq_,module,exports){"use strict";function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor))throw new TypeError("Cannot call a class as a function")}function getIDB(){try{if("undefined"!=typeof indexedDB)return indexedDB;if("undefined"!=typeof webkitIndexedDB)return webkitIndexedDB;if("undefined"!=typeof mozIndexedDB)return mozIndexedDB;if("undefined"!=typeof OIndexedDB)return OIndexedDB;if("undefined"!=typeof msIndexedDB)return msIndexedDB}catch(e){return}}function isIndexedDBValid(){try{if(!idb)return!1;var isSafari="undefined"!=typeof openDatabase&&/(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)&&!/BlackBerry/.test(navigator.platform),hasFetch="function"==typeof fetch&&fetch.toString().indexOf("[native code")!==-1;return(!isSafari||hasFetch)&&"undefined"!=typeof indexedDB&&"undefined"!=typeof IDBKeyRange}catch(e){return!1}}function createBlob(parts,properties){parts=parts||[],properties=properties||{};try{return new Blob(parts,properties)}catch(e){if("TypeError"!==e.name)throw e;for(var Builder="undefined"!=typeof BlobBuilder?BlobBuilder:"undefined"!=typeof MSBlobBuilder?MSBlobBuilder:"undefined"!=typeof MozBlobBuilder?MozBlobBuilder:WebKitBlobBuilder,builder=new Builder,i=0;i<parts.length;i+=1)builder.append(parts[i]);return builder.getBlob(properties.type)}}function executeCallback(promise,callback){callback&&promise.then(function(result){callback(null,result)},function(error){callback(error)})}function executeTwoCallbacks(promise,callback,errorCallback){"function"==typeof callback&&promise.then(callback),"function"==typeof errorCallback&&promise.catch(errorCallback)}function normalizeKey(key){return"string"!=typeof key&&(console.warn(key+" used as a key, but it is not a string."),key=String(key)),key}function getCallback(){if(arguments.length&&"function"==typeof arguments[arguments.length-1])return arguments[arguments.length-1]}function _binStringToArrayBuffer(bin){for(var length=bin.length,buf=new ArrayBuffer(length),arr=new Uint8Array(buf),i=0;i<length;i++)arr[i]=bin.charCodeAt(i);return buf}function _checkBlobSupportWithoutCaching(idb){return new Promise$1(function(resolve){var txn=idb.transaction(DETECT_BLOB_SUPPORT_STORE,READ_WRITE),blob=createBlob([""]);txn.objectStore(DETECT_BLOB_SUPPORT_STORE).put(blob,"key"),txn.onabort=function(e){e.preventDefault(),e.stopPropagation(),resolve(!1)},txn.oncomplete=function(){var matchedChrome=navigator.userAgent.match(/Chrome\/(\d+)/),matchedEdge=navigator.userAgent.match(/Edge\//);resolve(matchedEdge||!matchedChrome||parseInt(matchedChrome[1],10)>=43)}}).catch(function(){return!1})}function _checkBlobSupport(idb){return"boolean"==typeof supportsBlobs?Promise$1.resolve(supportsBlobs):_checkBlobSupportWithoutCaching(idb).then(function(value){return supportsBlobs=value})}function _deferReadiness(dbInfo){var dbContext=dbContexts[dbInfo.name],deferredOperation={};deferredOperation.promise=new Promise$1(function(resolve,reject){deferredOperation.resolve=resolve,deferredOperation.reject=reject}),dbContext.deferredOperations.push(deferredOperation),dbContext.dbReady?dbContext.dbReady=dbContext.dbReady.then(function(){return deferredOperation.promise}):dbContext.dbReady=deferredOperation.promise}function _advanceReadiness(dbInfo){var dbContext=dbContexts[dbInfo.name],deferredOperation=dbContext.deferredOperations.pop();if(deferredOperation)return deferredOperation.resolve(),deferredOperation.promise}function _rejectReadiness(dbInfo,err){var dbContext=dbContexts[dbInfo.name],deferredOperation=dbContext.deferredOperations.pop();if(deferredOperation)return deferredOperation.reject(err),deferredOperation.promise}function _getConnection(dbInfo,upgradeNeeded){return new Promise$1(function(resolve,reject){if(dbContexts[dbInfo.name]=dbContexts[dbInfo.name]||createDbContext(),dbInfo.db){if(!upgradeNeeded)return resolve(dbInfo.db);_deferReadiness(dbInfo),dbInfo.db.close()}var dbArgs=[dbInfo.name];upgradeNeeded&&dbArgs.push(dbInfo.version);var openreq=idb.open.apply(idb,dbArgs);upgradeNeeded&&(openreq.onupgradeneeded=function(e){var db=openreq.result;try{db.createObjectStore(dbInfo.storeName),e.oldVersion<=1&&db.createObjectStore(DETECT_BLOB_SUPPORT_STORE)}catch(ex){if("ConstraintError"!==ex.name)throw ex;console.warn('The database "'+dbInfo.name+'" has been upgraded from version '+e.oldVersion+" to version "+e.newVersion+', but the storage "'+dbInfo.storeName+'" already exists.')}}),openreq.onerror=function(e){e.preventDefault(),reject(openreq.error)},openreq.onsuccess=function(){resolve(openreq.result),_advanceReadiness(dbInfo)}})}function _getOriginalConnection(dbInfo){return _getConnection(dbInfo,!1)}function _getUpgradedConnection(dbInfo){return _getConnection(dbInfo,!0)}function _isUpgradeNeeded(dbInfo,defaultVersion){if(!dbInfo.db)return!0;var isNewStore=!dbInfo.db.objectStoreNames.contains(dbInfo.storeName),isDowngrade=dbInfo.version<dbInfo.db.version,isUpgrade=dbInfo.version>dbInfo.db.version;if(isDowngrade&&(dbInfo.version!==defaultVersion&&console.warn('The database "'+dbInfo.name+"\" can't be downgraded from version "+dbInfo.db.version+" to version "+dbInfo.version+"."),dbInfo.version=dbInfo.db.version),isUpgrade||isNewStore){if(isNewStore){var incVersion=dbInfo.db.version+1;incVersion>dbInfo.version&&(dbInfo.version=incVersion)}return!0}return!1}function _encodeBlob(blob){return new Promise$1(function(resolve,reject){var reader=new FileReader;reader.onerror=reject,reader.onloadend=function(e){var base64=btoa(e.target.result||"");resolve({__local_forage_encoded_blob:!0,data:base64,type:blob.type})},reader.readAsBinaryString(blob)})}function _decodeBlob(encodedBlob){var arrayBuff=_binStringToArrayBuffer(atob(encodedBlob.data));return createBlob([arrayBuff],{type:encodedBlob.type})}function _isEncodedBlob(value){return value&&value.__local_forage_encoded_blob}function _fullyReady(callback){var self=this,promise=self._initReady().then(function(){var dbContext=dbContexts[self._dbInfo.name];if(dbContext&&dbContext.dbReady)return dbContext.dbReady});return executeTwoCallbacks(promise,callback,callback),promise}function _tryReconnect(dbInfo){_deferReadiness(dbInfo);for(var dbContext=dbContexts[dbInfo.name],forages=dbContext.forages,i=0;i<forages.length;i++){var forage=forages[i];forage._dbInfo.db&&(forage._dbInfo.db.close(),forage._dbInfo.db=null)}return dbInfo.db=null,_getOriginalConnection(dbInfo).then(function(db){return dbInfo.db=db,_isUpgradeNeeded(dbInfo)?_getUpgradedConnection(dbInfo):db}).then(function(db){dbInfo.db=dbContext.db=db;for(var i=0;i<forages.length;i++)forages[i]._dbInfo.db=db}).catch(function(err){throw _rejectReadiness(dbInfo,err),err})}function createTransaction(dbInfo,mode,callback,retries){void 0===retries&&(retries=1);try{var tx=dbInfo.db.transaction(dbInfo.storeName,mode);callback(null,tx)}catch(err){if(retries>0&&(!dbInfo.db||"InvalidStateError"===err.name||"NotFoundError"===err.name))return Promise$1.resolve().then(function(){if(!dbInfo.db||"NotFoundError"===err.name&&!dbInfo.db.objectStoreNames.contains(dbInfo.storeName)&&dbInfo.version<=dbInfo.db.version)return dbInfo.db&&(dbInfo.version=dbInfo.db.version+1),_getUpgradedConnection(dbInfo)}).then(function(){return _tryReconnect(dbInfo).then(function(){createTransaction(dbInfo,mode,callback,retries-1)})}).catch(callback);callback(err)}}function createDbContext(){return{forages:[],db:null,dbReady:null,deferredOperations:[]}}function _initStorage(options){function ignoreErrors(){return Promise$1.resolve()}var self=this,dbInfo={db:null};if(options)for(var i in options)dbInfo[i]=options[i];var dbContext=dbContexts[dbInfo.name];dbContext||(dbContext=createDbContext(),dbContexts[dbInfo.name]=dbContext),dbContext.forages.push(self),self._initReady||(self._initReady=self.ready,self.ready=_fullyReady);for(var initPromises=[],j=0;j<dbContext.forages.length;j++){var forage=dbContext.forages[j];forage!==self&&initPromises.push(forage._initReady().catch(ignoreErrors))}var forages=dbContext.forages.slice(0);return Promise$1.all(initPromises).then(function(){return dbInfo.db=dbContext.db,_getOriginalConnection(dbInfo)}).then(function(db){return dbInfo.db=db,_isUpgradeNeeded(dbInfo,self._defaultConfig.version)?_getUpgradedConnection(dbInfo):db}).then(function(db){dbInfo.db=dbContext.db=db,self._dbInfo=dbInfo;for(var k=0;k<forages.length;k++){var forage=forages[k];forage!==self&&(forage._dbInfo.db=dbInfo.db,forage._dbInfo.version=dbInfo.version)}})}function getItem(key,callback){var self=this;key=normalizeKey(key);var promise=new Promise$1(function(resolve,reject){self.ready().then(function(){createTransaction(self._dbInfo,READ_ONLY,function(err,transaction){if(err)return reject(err);try{var store=transaction.objectStore(self._dbInfo.storeName),req=store.get(key);req.onsuccess=function(){var value=req.result;void 0===value&&(value=null),_isEncodedBlob(value)&&(value=_decodeBlob(value)),resolve(value)},req.onerror=function(){reject(req.error)}}catch(e){reject(e)}})}).catch(reject)});return executeCallback(promise,callback),promise}function iterate(iterator,callback){var self=this,promise=new Promise$1(function(resolve,reject){self.ready().then(function(){createTransaction(self._dbInfo,READ_ONLY,function(err,transaction){if(err)return reject(err);try{var store=transaction.objectStore(self._dbInfo.storeName),req=store.openCursor(),iterationNumber=1;req.onsuccess=function(){var cursor=req.result;if(cursor){var value=cursor.value;_isEncodedBlob(value)&&(value=_decodeBlob(value));var result=iterator(value,cursor.key,iterationNumber++);void 0!==result?resolve(result):cursor.continue()}else resolve()},req.onerror=function(){reject(req.error)}}catch(e){reject(e)}})}).catch(reject)});return executeCallback(promise,callback),promise}function setItem(key,value,callback){var self=this;key=normalizeKey(key);var promise=new Promise$1(function(resolve,reject){var dbInfo;self.ready().then(function(){return dbInfo=self._dbInfo,"[object Blob]"===toString.call(value)?_checkBlobSupport(dbInfo.db).then(function(blobSupport){return blobSupport?value:_encodeBlob(value)}):value}).then(function(value){createTransaction(self._dbInfo,READ_WRITE,function(err,transaction){if(err)return reject(err);try{var store=transaction.objectStore(self._dbInfo.storeName);null===value&&(value=void 0);var req=store.put(value,key);transaction.oncomplete=function(){void 0===value&&(value=null),resolve(value)},transaction.onabort=transaction.onerror=function(){var err=req.error?req.error:req.transaction.error;reject(err)}}catch(e){reject(e)}})}).catch(reject)});return executeCallback(promise,callback),promise}function removeItem(key,callback){var self=this;key=normalizeKey(key);var promise=new Promise$1(function(resolve,reject){self.ready().then(function(){createTransaction(self._dbInfo,READ_WRITE,function(err,transaction){if(err)return reject(err);try{var store=transaction.objectStore(self._dbInfo.storeName),req=store.delete(key);transaction.oncomplete=function(){resolve()},transaction.onerror=function(){reject(req.error)},transaction.onabort=function(){var err=req.error?req.error:req.transaction.error;reject(err)}}catch(e){reject(e)}})}).catch(reject)});return executeCallback(promise,callback),promise}function clear(callback){var self=this,promise=new Promise$1(function(resolve,reject){self.ready().then(function(){createTransaction(self._dbInfo,READ_WRITE,function(err,transaction){if(err)return reject(err);try{var store=transaction.objectStore(self._dbInfo.storeName),req=store.clear();transaction.oncomplete=function(){resolve()},transaction.onabort=transaction.onerror=function(){var err=req.error?req.error:req.transaction.error;reject(err)}}catch(e){reject(e)}})}).catch(reject)});return executeCallback(promise,callback),promise}function length(callback){var self=this,promise=new Promise$1(function(resolve,reject){self.ready().then(function(){createTransaction(self._dbInfo,READ_ONLY,function(err,transaction){if(err)return reject(err);try{var store=transaction.objectStore(self._dbInfo.storeName),req=store.count();req.onsuccess=function(){resolve(req.result)},req.onerror=function(){reject(req.error)}}catch(e){reject(e)}})}).catch(reject)});return executeCallback(promise,callback),promise}function key(n,callback){var self=this,promise=new Promise$1(function(resolve,reject){return n<0?void resolve(null):void self.ready().then(function(){createTransaction(self._dbInfo,READ_ONLY,function(err,transaction){if(err)return reject(err);try{var store=transaction.objectStore(self._dbInfo.storeName),advanced=!1,req=store.openCursor();req.onsuccess=function(){var cursor=req.result;return cursor?void(0===n?resolve(cursor.key):advanced?resolve(cursor.key):(advanced=!0,cursor.advance(n))):void resolve(null)},req.onerror=function(){reject(req.error)}}catch(e){reject(e)}})}).catch(reject)});return executeCallback(promise,callback),promise}function keys(callback){var self=this,promise=new Promise$1(function(resolve,reject){self.ready().then(function(){createTransaction(self._dbInfo,READ_ONLY,function(err,transaction){if(err)return reject(err);try{var store=transaction.objectStore(self._dbInfo.storeName),req=store.openCursor(),keys=[];req.onsuccess=function(){var cursor=req.result;return cursor?(keys.push(cursor.key),void cursor.continue()):void resolve(keys)},req.onerror=function(){reject(req.error)}}catch(e){reject(e)}})}).catch(reject)});return executeCallback(promise,callback),promise}function dropInstance(options,callback){callback=getCallback.apply(this,arguments);var currentConfig=this.config();options="function"!=typeof options&&options||{},options.name||(options.name=options.name||currentConfig.name,options.storeName=options.storeName||currentConfig.storeName);var promise,self=this;if(options.name){var isCurrentDb=options.name===currentConfig.name&&self._dbInfo.db,dbPromise=isCurrentDb?Promise$1.resolve(self._dbInfo.db):_getOriginalConnection(options).then(function(db){var dbContext=dbContexts[options.name],forages=dbContext.forages;dbContext.db=db;for(var i=0;i<forages.length;i++)forages[i]._dbInfo.db=db;return db});promise=options.storeName?dbPromise.then(function(db){if(db.objectStoreNames.contains(options.storeName)){var newVersion=db.version+1;_deferReadiness(options);var dbContext=dbContexts[options.name],forages=dbContext.forages;db.close();for(var i=0;i<forages.length;i++){var forage=forages[i];forage._dbInfo.db=null,forage._dbInfo.version=newVersion}var dropObjectPromise=new Promise$1(function(resolve,reject){var req=idb.open(options.name,newVersion);req.onerror=function(err){var db=req.result;db.close(),reject(err)},req.onupgradeneeded=function(){var db=req.result;db.deleteObjectStore(options.storeName)},req.onsuccess=function(){var db=req.result;db.close(),resolve(db)}});return dropObjectPromise.then(function(db){dbContext.db=db;for(var j=0;j<forages.length;j++){var _forage2=forages[j];_forage2._dbInfo.db=db,_advanceReadiness(_forage2._dbInfo)}}).catch(function(err){throw(_rejectReadiness(options,err)||Promise$1.resolve()).catch(function(){}),err})}}):dbPromise.then(function(db){_deferReadiness(options);var dbContext=dbContexts[options.name],forages=dbContext.forages;db.close();for(var i=0;i<forages.length;i++){var forage=forages[i];forage._dbInfo.db=null}var dropDBPromise=new Promise$1(function(resolve,reject){var req=idb.deleteDatabase(options.name);req.onerror=req.onblocked=function(err){var db=req.result;db&&db.close(),reject(err)},req.onsuccess=function(){var db=req.result;db&&db.close(),resolve(db)}});return dropDBPromise.then(function(db){dbContext.db=db;for(var i=0;i<forages.length;i++){var _forage=forages[i];_advanceReadiness(_forage._dbInfo)}}).catch(function(err){throw(_rejectReadiness(options,err)||Promise$1.resolve()).catch(function(){}),err})})}else promise=Promise$1.reject("Invalid arguments");return executeCallback(promise,callback),promise}function isWebSQLValid(){return"function"==typeof openDatabase}function stringToBuffer(serializedString){var i,encoded1,encoded2,encoded3,encoded4,bufferLength=.75*serializedString.length,len=serializedString.length,p=0;"="===serializedString[serializedString.length-1]&&(bufferLength--,"="===serializedString[serializedString.length-2]&&bufferLength--);var buffer=new ArrayBuffer(bufferLength),bytes=new Uint8Array(buffer);for(i=0;i<len;i+=4)encoded1=BASE_CHARS.indexOf(serializedString[i]),encoded2=BASE_CHARS.indexOf(serializedString[i+1]),encoded3=BASE_CHARS.indexOf(serializedString[i+2]),encoded4=BASE_CHARS.indexOf(serializedString[i+3]),bytes[p++]=encoded1<<2|encoded2>>4,bytes[p++]=(15&encoded2)<<4|encoded3>>2,bytes[p++]=(3&encoded3)<<6|63&encoded4;return buffer}function bufferToString(buffer){var i,bytes=new Uint8Array(buffer),base64String="";for(i=0;i<bytes.length;i+=3)base64String+=BASE_CHARS[bytes[i]>>2],base64String+=BASE_CHARS[(3&bytes[i])<<4|bytes[i+1]>>4],base64String+=BASE_CHARS[(15&bytes[i+1])<<2|bytes[i+2]>>6],base64String+=BASE_CHARS[63&bytes[i+2]];return bytes.length%3===2?base64String=base64String.substring(0,base64String.length-1)+"=":bytes.length%3===1&&(base64String=base64String.substring(0,base64String.length-2)+"=="),base64String}function serialize(value,callback){var valueType="";if(value&&(valueType=toString$1.call(value)),value&&("[object ArrayBuffer]"===valueType||value.buffer&&"[object ArrayBuffer]"===toString$1.call(value.buffer))){var buffer,marker=SERIALIZED_MARKER;value instanceof ArrayBuffer?(buffer=value,marker+=TYPE_ARRAYBUFFER):(buffer=value.buffer,"[object Int8Array]"===valueType?marker+=TYPE_INT8ARRAY:"[object Uint8Array]"===valueType?marker+=TYPE_UINT8ARRAY:"[object Uint8ClampedArray]"===valueType?marker+=TYPE_UINT8CLAMPEDARRAY:"[object Int16Array]"===valueType?marker+=TYPE_INT16ARRAY:"[object Uint16Array]"===valueType?marker+=TYPE_UINT16ARRAY:"[object Int32Array]"===valueType?marker+=TYPE_INT32ARRAY:"[object Uint32Array]"===valueType?marker+=TYPE_UINT32ARRAY:"[object Float32Array]"===valueType?marker+=TYPE_FLOAT32ARRAY:"[object Float64Array]"===valueType?marker+=TYPE_FLOAT64ARRAY:callback(new Error("Failed to get type for BinaryArray"))),callback(marker+bufferToString(buffer))}else if("[object Blob]"===valueType){var fileReader=new FileReader;fileReader.onload=function(){var str=BLOB_TYPE_PREFIX+value.type+"~"+bufferToString(this.result);callback(SERIALIZED_MARKER+TYPE_BLOB+str)},fileReader.readAsArrayBuffer(value)}else try{callback(JSON.stringify(value))}catch(e){console.error("Couldn't convert value into a JSON string: ",value),callback(null,e)}}function deserialize(value){if(value.substring(0,SERIALIZED_MARKER_LENGTH)!==SERIALIZED_MARKER)return JSON.parse(value);var blobType,serializedString=value.substring(TYPE_SERIALIZED_MARKER_LENGTH),type=value.substring(SERIALIZED_MARKER_LENGTH,TYPE_SERIALIZED_MARKER_LENGTH);if(type===TYPE_BLOB&&BLOB_TYPE_PREFIX_REGEX.test(serializedString)){var matcher=serializedString.match(BLOB_TYPE_PREFIX_REGEX);blobType=matcher[1],serializedString=serializedString.substring(matcher[0].length)}var buffer=stringToBuffer(serializedString);switch(type){case TYPE_ARRAYBUFFER:return buffer;case TYPE_BLOB:return createBlob([buffer],{type:blobType});case TYPE_INT8ARRAY:return new Int8Array(buffer);case TYPE_UINT8ARRAY:return new Uint8Array(buffer);case TYPE_UINT8CLAMPEDARRAY:return new Uint8ClampedArray(buffer);case TYPE_INT16ARRAY:return new Int16Array(buffer);case TYPE_UINT16ARRAY:return new Uint16Array(buffer);case TYPE_INT32ARRAY:return new Int32Array(buffer);case TYPE_UINT32ARRAY:return new Uint32Array(buffer);case TYPE_FLOAT32ARRAY:return new Float32Array(buffer);case TYPE_FLOAT64ARRAY:return new Float64Array(buffer);default:throw new Error("Unkown type: "+type)}}function createDbTable(t,dbInfo,callback,errorCallback){t.executeSql("CREATE TABLE IF NOT EXISTS "+dbInfo.storeName+" (id INTEGER PRIMARY KEY, key unique, value)",[],callback,errorCallback)}function _initStorage$1(options){var self=this,dbInfo={db:null};if(options)for(var i in options)dbInfo[i]="string"!=typeof options[i]?options[i].toString():options[i];var dbInfoPromise=new Promise$1(function(resolve,reject){try{dbInfo.db=openDatabase(dbInfo.name,String(dbInfo.version),dbInfo.description,dbInfo.size)}catch(e){return reject(e)}dbInfo.db.transaction(function(t){createDbTable(t,dbInfo,function(){self._dbInfo=dbInfo,resolve()},function(t,error){reject(error)})},reject)});return dbInfo.serializer=localforageSerializer,dbInfoPromise}function tryExecuteSql(t,dbInfo,sqlStatement,args,callback,errorCallback){t.executeSql(sqlStatement,args,callback,function(t,error){error.code===error.SYNTAX_ERR?t.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name = ?",[dbInfo.storeName],function(t,results){results.rows.length?errorCallback(t,error):createDbTable(t,dbInfo,function(){t.executeSql(sqlStatement,args,callback,errorCallback)},errorCallback)},errorCallback):errorCallback(t,error)},errorCallback)}function getItem$1(key,callback){var self=this;key=normalizeKey(key);var promise=new Promise$1(function(resolve,reject){self.ready().then(function(){var dbInfo=self._dbInfo;dbInfo.db.transaction(function(t){tryExecuteSql(t,dbInfo,"SELECT * FROM "+dbInfo.storeName+" WHERE key = ? LIMIT 1",[key],function(t,results){var result=results.rows.length?results.rows.item(0).value:null;result&&(result=dbInfo.serializer.deserialize(result)),resolve(result)},function(t,error){reject(error)})})}).catch(reject)});return executeCallback(promise,callback),promise}function iterate$1(iterator,callback){var self=this,promise=new Promise$1(function(resolve,reject){self.ready().then(function(){var dbInfo=self._dbInfo;dbInfo.db.transaction(function(t){tryExecuteSql(t,dbInfo,"SELECT * FROM "+dbInfo.storeName,[],function(t,results){for(var rows=results.rows,length=rows.length,i=0;i<length;i++){var item=rows.item(i),result=item.value;if(result&&(result=dbInfo.serializer.deserialize(result)),result=iterator(result,item.key,i+1),void 0!==result)return void resolve(result)}resolve()},function(t,error){reject(error)})})}).catch(reject)});return executeCallback(promise,callback),promise}function _setItem(key,value,callback,retriesLeft){var self=this;key=normalizeKey(key);var promise=new Promise$1(function(resolve,reject){self.ready().then(function(){void 0===value&&(value=null);var originalValue=value,dbInfo=self._dbInfo;dbInfo.serializer.serialize(value,function(value,error){error?reject(error):dbInfo.db.transaction(function(t){tryExecuteSql(t,dbInfo,"INSERT OR REPLACE INTO "+dbInfo.storeName+" (key, value) VALUES (?, ?)",[key,value],function(){resolve(originalValue)},function(t,error){reject(error)})},function(sqlError){if(sqlError.code===sqlError.QUOTA_ERR){if(retriesLeft>0)return void resolve(_setItem.apply(self,[key,originalValue,callback,retriesLeft-1]));reject(sqlError)}})})}).catch(reject)});return executeCallback(promise,callback),promise}function setItem$1(key,value,callback){return _setItem.apply(this,[key,value,callback,1])}function removeItem$1(key,callback){var self=this;key=normalizeKey(key);var promise=new Promise$1(function(resolve,reject){self.ready().then(function(){var dbInfo=self._dbInfo;dbInfo.db.transaction(function(t){tryExecuteSql(t,dbInfo,"DELETE FROM "+dbInfo.storeName+" WHERE key = ?",[key],function(){resolve()},function(t,error){reject(error)})})}).catch(reject)});return executeCallback(promise,callback),promise}function clear$1(callback){var self=this,promise=new Promise$1(function(resolve,reject){self.ready().then(function(){var dbInfo=self._dbInfo;dbInfo.db.transaction(function(t){tryExecuteSql(t,dbInfo,"DELETE FROM "+dbInfo.storeName,[],function(){resolve()},function(t,error){reject(error)})})}).catch(reject)});return executeCallback(promise,callback),promise}function length$1(callback){var self=this,promise=new Promise$1(function(resolve,reject){self.ready().then(function(){var dbInfo=self._dbInfo;dbInfo.db.transaction(function(t){tryExecuteSql(t,dbInfo,"SELECT COUNT(key) as c FROM "+dbInfo.storeName,[],function(t,results){var result=results.rows.item(0).c;resolve(result)},function(t,error){reject(error)})})}).catch(reject)});return executeCallback(promise,callback),promise}function key$1(n,callback){var self=this,promise=new Promise$1(function(resolve,reject){self.ready().then(function(){var dbInfo=self._dbInfo;dbInfo.db.transaction(function(t){tryExecuteSql(t,dbInfo,"SELECT key FROM "+dbInfo.storeName+" WHERE id = ? LIMIT 1",[n+1],function(t,results){var result=results.rows.length?results.rows.item(0).key:null;resolve(result)},function(t,error){reject(error)})})}).catch(reject)});return executeCallback(promise,callback),promise}function keys$1(callback){var self=this,promise=new Promise$1(function(resolve,reject){self.ready().then(function(){var dbInfo=self._dbInfo;dbInfo.db.transaction(function(t){tryExecuteSql(t,dbInfo,"SELECT key FROM "+dbInfo.storeName,[],function(t,results){for(var keys=[],i=0;i<results.rows.length;i++)keys.push(results.rows.item(i).key);resolve(keys)},function(t,error){reject(error)})})}).catch(reject)});return executeCallback(promise,callback),promise}function getAllStoreNames(db){return new Promise$1(function(resolve,reject){db.transaction(function(t){t.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name <> '__WebKitDatabaseInfoTable__'",[],function(t,results){for(var storeNames=[],i=0;i<results.rows.length;i++)storeNames.push(results.rows.item(i).name);resolve({db:db,storeNames:storeNames})},function(t,error){reject(error)})},function(sqlError){reject(sqlError)})})}function dropInstance$1(options,callback){callback=getCallback.apply(this,arguments);var currentConfig=this.config();options="function"!=typeof options&&options||{},options.name||(options.name=options.name||currentConfig.name,options.storeName=options.storeName||currentConfig.storeName);var promise,self=this;return promise=options.name?new Promise$1(function(resolve){var db;db=options.name===currentConfig.name?self._dbInfo.db:openDatabase(options.name,"","",0),resolve(options.storeName?{db:db,storeNames:[options.storeName]}:getAllStoreNames(db))}).then(function(operationInfo){return new Promise$1(function(resolve,reject){operationInfo.db.transaction(function(t){function dropTable(storeName){return new Promise$1(function(resolve,reject){t.executeSql("DROP TABLE IF EXISTS "+storeName,[],function(){resolve()},function(t,error){reject(error)})})}for(var operations=[],i=0,len=operationInfo.storeNames.length;i<len;i++)operations.push(dropTable(operationInfo.storeNames[i]));Promise$1.all(operations).then(function(){resolve()}).catch(function(e){reject(e)})},function(sqlError){reject(sqlError)})})}):Promise$1.reject("Invalid arguments"),executeCallback(promise,callback),promise}function isLocalStorageValid(){try{return"undefined"!=typeof localStorage&&"setItem"in localStorage&&!!localStorage.setItem}catch(e){return!1}}function _getKeyPrefix(options,defaultConfig){var keyPrefix=options.name+"/";return options.storeName!==defaultConfig.storeName&&(keyPrefix+=options.storeName+"/"),keyPrefix}function checkIfLocalStorageThrows(){var localStorageTestKey="_localforage_support_test";try{return localStorage.setItem(localStorageTestKey,!0),localStorage.removeItem(localStorageTestKey),!1}catch(e){return!0}}function _isLocalStorageUsable(){return!checkIfLocalStorageThrows()||localStorage.length>0}function _initStorage$2(options){var self=this,dbInfo={};if(options)for(var i in options)dbInfo[i]=options[i];return dbInfo.keyPrefix=_getKeyPrefix(options,self._defaultConfig),_isLocalStorageUsable()?(self._dbInfo=dbInfo,dbInfo.serializer=localforageSerializer,Promise$1.resolve()):Promise$1.reject()}function clear$2(callback){var self=this,promise=self.ready().then(function(){for(var keyPrefix=self._dbInfo.keyPrefix,i=localStorage.length-1;i>=0;i--){var key=localStorage.key(i);0===key.indexOf(keyPrefix)&&localStorage.removeItem(key)}});return executeCallback(promise,callback),promise}function getItem$2(key,callback){var self=this;key=normalizeKey(key);var promise=self.ready().then(function(){var dbInfo=self._dbInfo,result=localStorage.getItem(dbInfo.keyPrefix+key);return result&&(result=dbInfo.serializer.deserialize(result)),result});return executeCallback(promise,callback),promise}function iterate$2(iterator,callback){var self=this,promise=self.ready().then(function(){for(var dbInfo=self._dbInfo,keyPrefix=dbInfo.keyPrefix,keyPrefixLength=keyPrefix.length,length=localStorage.length,iterationNumber=1,i=0;i<length;i++){var key=localStorage.key(i);if(0===key.indexOf(keyPrefix)){var value=localStorage.getItem(key);if(value&&(value=dbInfo.serializer.deserialize(value)),value=iterator(value,key.substring(keyPrefixLength),iterationNumber++),void 0!==value)return value}}});return executeCallback(promise,callback),promise}function key$2(n,callback){var self=this,promise=self.ready().then(function(){var result,dbInfo=self._dbInfo;try{result=localStorage.key(n)}catch(error){result=null}return result&&(result=result.substring(dbInfo.keyPrefix.length)),result});return executeCallback(promise,callback),promise}function keys$2(callback){var self=this,promise=self.ready().then(function(){for(var dbInfo=self._dbInfo,length=localStorage.length,keys=[],i=0;i<length;i++){var itemKey=localStorage.key(i);0===itemKey.indexOf(dbInfo.keyPrefix)&&keys.push(itemKey.substring(dbInfo.keyPrefix.length))}return keys});return executeCallback(promise,callback),promise}function length$2(callback){var self=this,promise=self.keys().then(function(keys){return keys.length});return executeCallback(promise,callback),promise}function removeItem$2(key,callback){var self=this;key=normalizeKey(key);var promise=self.ready().then(function(){var dbInfo=self._dbInfo;localStorage.removeItem(dbInfo.keyPrefix+key)});return executeCallback(promise,callback),promise}function setItem$2(key,value,callback){var self=this;key=normalizeKey(key);var promise=self.ready().then(function(){void 0===value&&(value=null);var originalValue=value;return new Promise$1(function(resolve,reject){var dbInfo=self._dbInfo;dbInfo.serializer.serialize(value,function(value,error){if(error)reject(error);else try{localStorage.setItem(dbInfo.keyPrefix+key,value),resolve(originalValue)}catch(e){"QuotaExceededError"!==e.name&&"NS_ERROR_DOM_QUOTA_REACHED"!==e.name||reject(e),reject(e)}})})});return executeCallback(promise,callback),promise}function dropInstance$2(options,callback){if(callback=getCallback.apply(this,arguments),options="function"!=typeof options&&options||{},!options.name){var currentConfig=this.config();options.name=options.name||currentConfig.name,options.storeName=options.storeName||currentConfig.storeName}var promise,self=this;return promise=options.name?new Promise$1(function(resolve){resolve(options.storeName?_getKeyPrefix(options,self._defaultConfig):options.name+"/");
}).then(function(keyPrefix){for(var i=localStorage.length-1;i>=0;i--){var key=localStorage.key(i);0===key.indexOf(keyPrefix)&&localStorage.removeItem(key)}}):Promise$1.reject("Invalid arguments"),executeCallback(promise,callback),promise}function callWhenReady(localForageInstance,libraryMethod){localForageInstance[libraryMethod]=function(){var _args=arguments;return localForageInstance.ready().then(function(){return localForageInstance[libraryMethod].apply(localForageInstance,_args)})}}function extend(){for(var i=1;i<arguments.length;i++){var arg=arguments[i];if(arg)for(var _key in arg)arg.hasOwnProperty(_key)&&(isArray(arg[_key])?arguments[0][_key]=arg[_key].slice():arguments[0][_key]=arg[_key])}return arguments[0]}var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(obj){return typeof obj}:function(obj){return obj&&"function"==typeof Symbol&&obj.constructor===Symbol&&obj!==Symbol.prototype?"symbol":typeof obj},idb=getIDB();"undefined"==typeof Promise&&_dereq_(3);var Promise$1=Promise,DETECT_BLOB_SUPPORT_STORE="local-forage-detect-blob-support",supportsBlobs=void 0,dbContexts={},toString=Object.prototype.toString,READ_ONLY="readonly",READ_WRITE="readwrite",asyncStorage={_driver:"asyncStorage",_initStorage:_initStorage,_support:isIndexedDBValid(),iterate:iterate,getItem:getItem,setItem:setItem,removeItem:removeItem,clear:clear,length:length,key:key,keys:keys,dropInstance:dropInstance},BASE_CHARS="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",BLOB_TYPE_PREFIX="~~local_forage_type~",BLOB_TYPE_PREFIX_REGEX=/^~~local_forage_type~([^~]+)~/,SERIALIZED_MARKER="__lfsc__:",SERIALIZED_MARKER_LENGTH=SERIALIZED_MARKER.length,TYPE_ARRAYBUFFER="arbf",TYPE_BLOB="blob",TYPE_INT8ARRAY="si08",TYPE_UINT8ARRAY="ui08",TYPE_UINT8CLAMPEDARRAY="uic8",TYPE_INT16ARRAY="si16",TYPE_INT32ARRAY="si32",TYPE_UINT16ARRAY="ur16",TYPE_UINT32ARRAY="ui32",TYPE_FLOAT32ARRAY="fl32",TYPE_FLOAT64ARRAY="fl64",TYPE_SERIALIZED_MARKER_LENGTH=SERIALIZED_MARKER_LENGTH+TYPE_ARRAYBUFFER.length,toString$1=Object.prototype.toString,localforageSerializer={serialize:serialize,deserialize:deserialize,stringToBuffer:stringToBuffer,bufferToString:bufferToString},webSQLStorage={_driver:"webSQLStorage",_initStorage:_initStorage$1,_support:isWebSQLValid(),iterate:iterate$1,getItem:getItem$1,setItem:setItem$1,removeItem:removeItem$1,clear:clear$1,length:length$1,key:key$1,keys:keys$1,dropInstance:dropInstance$1},localStorageWrapper={_driver:"localStorageWrapper",_initStorage:_initStorage$2,_support:isLocalStorageValid(),iterate:iterate$2,getItem:getItem$2,setItem:setItem$2,removeItem:removeItem$2,clear:clear$2,length:length$2,key:key$2,keys:keys$2,dropInstance:dropInstance$2},sameValue=function(x,y){return x===y||"number"==typeof x&&"number"==typeof y&&isNaN(x)&&isNaN(y)},includes=function(array,searchElement){for(var len=array.length,i=0;i<len;){if(sameValue(array[i],searchElement))return!0;i++}return!1},isArray=Array.isArray||function(arg){return"[object Array]"===Object.prototype.toString.call(arg)},DefinedDrivers={},DriverSupport={},DefaultDrivers={INDEXEDDB:asyncStorage,WEBSQL:webSQLStorage,LOCALSTORAGE:localStorageWrapper},DefaultDriverOrder=[DefaultDrivers.INDEXEDDB._driver,DefaultDrivers.WEBSQL._driver,DefaultDrivers.LOCALSTORAGE._driver],OptionalDriverMethods=["dropInstance"],LibraryMethods=["clear","getItem","iterate","key","keys","length","removeItem","setItem"].concat(OptionalDriverMethods),DefaultConfig={description:"",driver:DefaultDriverOrder.slice(),name:"localforage",size:4980736,storeName:"keyvaluepairs",version:1},LocalForage=function(){function LocalForage(options){_classCallCheck(this,LocalForage);for(var driverTypeKey in DefaultDrivers)if(DefaultDrivers.hasOwnProperty(driverTypeKey)){var driver=DefaultDrivers[driverTypeKey],driverName=driver._driver;this[driverTypeKey]=driverName,DefinedDrivers[driverName]||this.defineDriver(driver)}this._defaultConfig=extend({},DefaultConfig),this._config=extend({},this._defaultConfig,options),this._driverSet=null,this._initDriver=null,this._ready=!1,this._dbInfo=null,this._wrapLibraryMethodsWithReady(),this.setDriver(this._config.driver).catch(function(){})}return LocalForage.prototype.config=function(options){if("object"===("undefined"==typeof options?"undefined":_typeof(options))){if(this._ready)return new Error("Can't call config() after localforage has been used.");for(var i in options){if("storeName"===i&&(options[i]=options[i].replace(/\W/g,"_")),"version"===i&&"number"!=typeof options[i])return new Error("Database version must be a number.");this._config[i]=options[i]}return!("driver"in options&&options.driver)||this.setDriver(this._config.driver)}return"string"==typeof options?this._config[options]:this._config},LocalForage.prototype.defineDriver=function(driverObject,callback,errorCallback){var promise=new Promise$1(function(resolve,reject){try{var driverName=driverObject._driver,complianceError=new Error("Custom driver not compliant; see https://mozilla.github.io/localForage/#definedriver");if(!driverObject._driver)return void reject(complianceError);for(var driverMethods=LibraryMethods.concat("_initStorage"),i=0,len=driverMethods.length;i<len;i++){var driverMethodName=driverMethods[i],isRequired=!includes(OptionalDriverMethods,driverMethodName);if((isRequired||driverObject[driverMethodName])&&"function"!=typeof driverObject[driverMethodName])return void reject(complianceError)}var configureMissingMethods=function(){for(var methodNotImplementedFactory=function(methodName){return function(){var error=new Error("Method "+methodName+" is not implemented by the current driver"),promise=Promise$1.reject(error);return executeCallback(promise,arguments[arguments.length-1]),promise}},_i=0,_len=OptionalDriverMethods.length;_i<_len;_i++){var optionalDriverMethod=OptionalDriverMethods[_i];driverObject[optionalDriverMethod]||(driverObject[optionalDriverMethod]=methodNotImplementedFactory(optionalDriverMethod))}};configureMissingMethods();var setDriverSupport=function(support){DefinedDrivers[driverName]&&console.info("Redefining LocalForage driver: "+driverName),DefinedDrivers[driverName]=driverObject,DriverSupport[driverName]=support,resolve()};"_support"in driverObject?driverObject._support&&"function"==typeof driverObject._support?driverObject._support().then(setDriverSupport,reject):setDriverSupport(!!driverObject._support):setDriverSupport(!0)}catch(e){reject(e)}});return executeTwoCallbacks(promise,callback,errorCallback),promise},LocalForage.prototype.driver=function(){return this._driver||null},LocalForage.prototype.getDriver=function(driverName,callback,errorCallback){var getDriverPromise=DefinedDrivers[driverName]?Promise$1.resolve(DefinedDrivers[driverName]):Promise$1.reject(new Error("Driver not found."));return executeTwoCallbacks(getDriverPromise,callback,errorCallback),getDriverPromise},LocalForage.prototype.getSerializer=function(callback){var serializerPromise=Promise$1.resolve(localforageSerializer);return executeTwoCallbacks(serializerPromise,callback),serializerPromise},LocalForage.prototype.ready=function(callback){var self=this,promise=self._driverSet.then(function(){return null===self._ready&&(self._ready=self._initDriver()),self._ready});return executeTwoCallbacks(promise,callback,callback),promise},LocalForage.prototype.setDriver=function(drivers,callback,errorCallback){function setDriverToConfig(){self._config.driver=self.driver()}function extendSelfWithDriver(driver){return self._extend(driver),setDriverToConfig(),self._ready=self._initStorage(self._config),self._ready}function initDriver(supportedDrivers){return function(){function driverPromiseLoop(){for(;currentDriverIndex<supportedDrivers.length;){var driverName=supportedDrivers[currentDriverIndex];return currentDriverIndex++,self._dbInfo=null,self._ready=null,self.getDriver(driverName).then(extendSelfWithDriver).catch(driverPromiseLoop)}setDriverToConfig();var error=new Error("No available storage method found.");return self._driverSet=Promise$1.reject(error),self._driverSet}var currentDriverIndex=0;return driverPromiseLoop()}}var self=this;isArray(drivers)||(drivers=[drivers]);var supportedDrivers=this._getSupportedDrivers(drivers),oldDriverSetDone=null!==this._driverSet?this._driverSet.catch(function(){return Promise$1.resolve()}):Promise$1.resolve();return this._driverSet=oldDriverSetDone.then(function(){var driverName=supportedDrivers[0];return self._dbInfo=null,self._ready=null,self.getDriver(driverName).then(function(driver){self._driver=driver._driver,setDriverToConfig(),self._wrapLibraryMethodsWithReady(),self._initDriver=initDriver(supportedDrivers)})}).catch(function(){setDriverToConfig();var error=new Error("No available storage method found.");return self._driverSet=Promise$1.reject(error),self._driverSet}),executeTwoCallbacks(this._driverSet,callback,errorCallback),this._driverSet},LocalForage.prototype.supports=function(driverName){return!!DriverSupport[driverName]},LocalForage.prototype._extend=function(libraryMethodsAndProperties){extend(this,libraryMethodsAndProperties)},LocalForage.prototype._getSupportedDrivers=function(drivers){for(var supportedDrivers=[],i=0,len=drivers.length;i<len;i++){var driverName=drivers[i];this.supports(driverName)&&supportedDrivers.push(driverName)}return supportedDrivers},LocalForage.prototype._wrapLibraryMethodsWithReady=function(){for(var i=0,len=LibraryMethods.length;i<len;i++)callWhenReady(this,LibraryMethods[i])},LocalForage.prototype.createInstance=function(options){return new LocalForage(options)},LocalForage}(),localforage_js=new LocalForage;module.exports=localforage_js},{3:3}]},{},[4])(4)})},function(module,exports){module.exports=__WEBPACK_EXTERNAL_MODULE_74__},function(module,exports,__webpack_require__){var __WEBPACK_AMD_DEFINE_FACTORY__,__WEBPACK_AMD_DEFINE_ARRAY__,__WEBPACK_AMD_DEFINE_RESULT__,require,require;(function(global){"use strict";var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(obj){return typeof obj}:function(obj){return obj&&"function"==typeof Symbol&&obj.constructor===Symbol&&obj!==Symbol.prototype?"symbol":typeof obj};CB._isNode||!function(f){if("object"===_typeof(exports)&&"undefined"!=typeof module)module.exports=f();else{__WEBPACK_AMD_DEFINE_ARRAY__=[],__WEBPACK_AMD_DEFINE_FACTORY__=f,__WEBPACK_AMD_DEFINE_RESULT__="function"==typeof __WEBPACK_AMD_DEFINE_FACTORY__?__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports,__WEBPACK_AMD_DEFINE_ARRAY__):__WEBPACK_AMD_DEFINE_FACTORY__,!(void 0!==__WEBPACK_AMD_DEFINE_RESULT__&&(module.exports=__WEBPACK_AMD_DEFINE_RESULT__))}}(function(){var define;return function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a="function"==typeof require&&require;if(!u&&a)return require(o,!0);if(i)return i(o,!0);var f=new Error("Cannot find module '"+o+"'");throw f.code="MODULE_NOT_FOUND",f}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}for(var i="function"==typeof require&&require,o=0;o<r.length;o++)s(r[o]);return s}({1:[function(_dereq_,module,exports){module.exports=_dereq_("./lib/")},{"./lib/":2}],2:[function(_dereq_,module,exports){module.exports=_dereq_("./socket"),module.exports.parser=_dereq_("engine.io-parser")},{"./socket":3,"engine.io-parser":19}],3:[function(_dereq_,module,exports){(function(global){function Socket(uri,opts){if(!(this instanceof Socket))return new Socket(uri,opts);opts=opts||{},uri&&"object"==("undefined"==typeof uri?"undefined":_typeof(uri))&&(opts=uri,uri=null),uri?(uri=parseuri(uri),opts.hostname=uri.host,opts.secure="https"==uri.protocol||"wss"==uri.protocol,opts.port=uri.port,uri.query&&(opts.query=uri.query)):opts.host&&(opts.hostname=parseuri(opts.host).host),this.secure=null!=opts.secure?opts.secure:global.location&&"https:"==location.protocol,opts.hostname&&!opts.port&&(opts.port=this.secure?"443":"80"),this.agent=opts.agent||!1,this.hostname=opts.hostname||(global.location?location.hostname:"localhost"),this.port=opts.port||(global.location&&location.port?location.port:this.secure?443:80),this.query=opts.query||{},"string"==typeof this.query&&(this.query=parseqs.decode(this.query)),this.upgrade=!1!==opts.upgrade,this.path=(opts.path||"/engine.io").replace(/\/$/,"")+"/",this.forceJSONP=!!opts.forceJSONP,this.jsonp=!1!==opts.jsonp,this.forceBase64=!!opts.forceBase64,this.enablesXDR=!!opts.enablesXDR,this.timestampParam=opts.timestampParam||"t",this.timestampRequests=opts.timestampRequests,this.transports=opts.transports||["polling","websocket"],this.readyState="",this.writeBuffer=[],this.policyPort=opts.policyPort||843,this.rememberUpgrade=opts.rememberUpgrade||!1,this.binaryType=null,this.onlyBinaryUpgrades=opts.onlyBinaryUpgrades,this.perMessageDeflate=!1!==opts.perMessageDeflate&&(opts.perMessageDeflate||{}),!0===this.perMessageDeflate&&(this.perMessageDeflate={}),this.perMessageDeflate&&null==this.perMessageDeflate.threshold&&(this.perMessageDeflate.threshold=1024),this.pfx=opts.pfx||null,this.key=opts.key||null,this.passphrase=opts.passphrase||null,this.cert=opts.cert||null,this.ca=opts.ca||null,this.ciphers=opts.ciphers||null,this.rejectUnauthorized=void 0===opts.rejectUnauthorized?null:opts.rejectUnauthorized;var freeGlobal="object"==("undefined"==typeof global?"undefined":_typeof(global))&&global;freeGlobal.global===freeGlobal&&opts.extraHeaders&&Object.keys(opts.extraHeaders).length>0&&(this.extraHeaders=opts.extraHeaders),this.open()}function clone(obj){var o={};for(var i in obj)obj.hasOwnProperty(i)&&(o[i]=obj[i]);return o}var transports=_dereq_("./transports"),Emitter=_dereq_("component-emitter"),debug=_dereq_("debug")("engine.io-client:socket"),index=_dereq_("indexof"),parser=_dereq_("engine.io-parser"),parseuri=_dereq_("parseuri"),parsejson=_dereq_("parsejson"),parseqs=_dereq_("parseqs");module.exports=Socket,Socket.priorWebsocketSuccess=!1,Emitter(Socket.prototype),Socket.protocol=parser.protocol,Socket.Socket=Socket,Socket.Transport=_dereq_("./transport"),Socket.transports=_dereq_("./transports"),Socket.parser=_dereq_("engine.io-parser"),Socket.prototype.createTransport=function(name){debug('creating transport "%s"',name);var query=clone(this.query);query.EIO=parser.protocol,query.transport=name,this.id&&(query.sid=this.id);var transport=new transports[name]({agent:this.agent,hostname:this.hostname,port:this.port,secure:this.secure,path:this.path,query:query,forceJSONP:this.forceJSONP,jsonp:this.jsonp,forceBase64:this.forceBase64,enablesXDR:this.enablesXDR,timestampRequests:this.timestampRequests,timestampParam:this.timestampParam,policyPort:this.policyPort,socket:this,pfx:this.pfx,key:this.key,passphrase:this.passphrase,cert:this.cert,ca:this.ca,ciphers:this.ciphers,rejectUnauthorized:this.rejectUnauthorized,perMessageDeflate:this.perMessageDeflate,extraHeaders:this.extraHeaders});return transport},Socket.prototype.open=function(){var transport;if(this.rememberUpgrade&&Socket.priorWebsocketSuccess&&this.transports.indexOf("websocket")!=-1)transport="websocket";else{if(0===this.transports.length){var self=this;return void setTimeout(function(){self.emit("error","No transports available")},0)}transport=this.transports[0]}this.readyState="opening";try{transport=this.createTransport(transport)}catch(e){return this.transports.shift(),void this.open()}transport.open(),this.setTransport(transport)},Socket.prototype.setTransport=function(transport){debug("setting transport %s",transport.name);var self=this;this.transport&&(debug("clearing existing transport %s",this.transport.name),this.transport.removeAllListeners()),this.transport=transport,transport.on("drain",function(){self.onDrain()}).on("packet",function(packet){self.onPacket(packet)}).on("error",function(e){self.onError(e)}).on("close",function(){self.onClose("transport close")})},Socket.prototype.probe=function(name){function onTransportOpen(){if(self.onlyBinaryUpgrades){var upgradeLosesBinary=!this.supportsBinary&&self.transport.supportsBinary;failed=failed||upgradeLosesBinary}failed||(debug('probe transport "%s" opened',name),transport.send([{type:"ping",data:"probe"}]),transport.once("packet",function(msg){if(!failed)if("pong"==msg.type&&"probe"==msg.data){if(debug('probe transport "%s" pong',name),self.upgrading=!0,self.emit("upgrading",transport),!transport)return;Socket.priorWebsocketSuccess="websocket"==transport.name,debug('pausing current transport "%s"',self.transport.name),self.transport.pause(function(){failed||"closed"!=self.readyState&&(debug("changing transport and sending upgrade packet"),cleanup(),self.setTransport(transport),transport.send([{type:"upgrade"}]),self.emit("upgrade",transport),transport=null,self.upgrading=!1,self.flush())})}else{debug('probe transport "%s" failed',name);var err=new Error("probe error");err.transport=transport.name,self.emit("upgradeError",err)}}))}function freezeTransport(){failed||(failed=!0,cleanup(),transport.close(),transport=null)}function onerror(err){var error=new Error("probe error: "+err);error.transport=transport.name,freezeTransport(),debug('probe transport "%s" failed because of error: %s',name,err),self.emit("upgradeError",error)}function onTransportClose(){onerror("transport closed")}function onclose(){onerror("socket closed")}function onupgrade(to){transport&&to.name!=transport.name&&(debug('"%s" works - aborting "%s"',to.name,transport.name),freezeTransport())}function cleanup(){transport.removeListener("open",onTransportOpen),transport.removeListener("error",onerror),transport.removeListener("close",onTransportClose),self.removeListener("close",onclose),self.removeListener("upgrading",onupgrade)}debug('probing transport "%s"',name);var transport=this.createTransport(name,{probe:1}),failed=!1,self=this;Socket.priorWebsocketSuccess=!1,transport.once("open",onTransportOpen),transport.once("error",onerror),transport.once("close",onTransportClose),this.once("close",onclose),this.once("upgrading",onupgrade),transport.open()},Socket.prototype.onOpen=function(){if(debug("socket open"),this.readyState="open",Socket.priorWebsocketSuccess="websocket"==this.transport.name,this.emit("open"),this.flush(),"open"==this.readyState&&this.upgrade&&this.transport.pause){debug("starting upgrade probes");for(var i=0,l=this.upgrades.length;i<l;i++)this.probe(this.upgrades[i])}},Socket.prototype.onPacket=function(packet){if("opening"==this.readyState||"open"==this.readyState)switch(debug('socket receive: type "%s", data "%s"',packet.type,packet.data),this.emit("packet",packet),this.emit("heartbeat"),packet.type){case"open":this.onHandshake(parsejson(packet.data));break;case"pong":this.setPing(),this.emit("pong");break;case"error":var err=new Error("server error");err.code=packet.data,this.onError(err);break;case"message":this.emit("data",packet.data),this.emit("message",packet.data)}else debug('packet received with socket readyState "%s"',this.readyState)},Socket.prototype.onHandshake=function(data){this.emit("handshake",data),this.id=data.sid,this.transport.query.sid=data.sid,this.upgrades=this.filterUpgrades(data.upgrades),this.pingInterval=data.pingInterval,this.pingTimeout=data.pingTimeout,this.onOpen(),"closed"!=this.readyState&&(this.setPing(),this.removeListener("heartbeat",this.onHeartbeat),this.on("heartbeat",this.onHeartbeat))},Socket.prototype.onHeartbeat=function(timeout){clearTimeout(this.pingTimeoutTimer);var self=this;self.pingTimeoutTimer=setTimeout(function(){"closed"!=self.readyState&&self.onClose("ping timeout")},timeout||self.pingInterval+self.pingTimeout)},Socket.prototype.setPing=function(){var self=this;clearTimeout(self.pingIntervalTimer),self.pingIntervalTimer=setTimeout(function(){debug("writing ping packet - expecting pong within %sms",self.pingTimeout),self.ping(),self.onHeartbeat(self.pingTimeout)},self.pingInterval)},Socket.prototype.ping=function(){var self=this;this.sendPacket("ping",function(){self.emit("ping")})},Socket.prototype.onDrain=function(){this.writeBuffer.splice(0,this.prevBufferLen),this.prevBufferLen=0,0===this.writeBuffer.length?this.emit("drain"):this.flush()},Socket.prototype.flush=function(){"closed"!=this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length&&(debug("flushing %d packets in socket",this.writeBuffer.length),this.transport.send(this.writeBuffer),this.prevBufferLen=this.writeBuffer.length,this.emit("flush"))},Socket.prototype.write=Socket.prototype.send=function(msg,options,fn){return this.sendPacket("message",msg,options,fn),this},Socket.prototype.sendPacket=function(type,data,options,fn){if("function"==typeof data&&(fn=data,data=void 0),"function"==typeof options&&(fn=options,options=null),"closing"!=this.readyState&&"closed"!=this.readyState){options=options||{},options.compress=!1!==options.compress;var packet={type:type,data:data,options:options};this.emit("packetCreate",packet),this.writeBuffer.push(packet),fn&&this.once("flush",fn),this.flush()}},Socket.prototype.close=function(){function close(){self.onClose("forced close"),debug("socket closing - telling transport to close"),self.transport.close()}function cleanupAndClose(){self.removeListener("upgrade",cleanupAndClose),self.removeListener("upgradeError",cleanupAndClose),close()}function waitForUpgrade(){self.once("upgrade",cleanupAndClose),self.once("upgradeError",cleanupAndClose)}if("opening"==this.readyState||"open"==this.readyState){this.readyState="closing";var self=this;this.writeBuffer.length?this.once("drain",function(){this.upgrading?waitForUpgrade():close()}):this.upgrading?waitForUpgrade():close()}return this},Socket.prototype.onError=function(err){debug("socket error %j",err),Socket.priorWebsocketSuccess=!1,this.emit("error",err),this.onClose("transport error",err)},Socket.prototype.onClose=function(reason,desc){if("opening"==this.readyState||"open"==this.readyState||"closing"==this.readyState){debug('socket close with reason: "%s"',reason);var self=this;clearTimeout(this.pingIntervalTimer),clearTimeout(this.pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),this.readyState="closed",this.id=null,this.emit("close",reason,desc),self.writeBuffer=[],self.prevBufferLen=0}},Socket.prototype.filterUpgrades=function(upgrades){for(var filteredUpgrades=[],i=0,j=upgrades.length;i<j;i++)~index(this.transports,upgrades[i])&&filteredUpgrades.push(upgrades[i]);return filteredUpgrades}}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})},{"./transport":4,"./transports":5,"component-emitter":15,debug:17,"engine.io-parser":19,indexof:23,parsejson:26,parseqs:27,parseuri:28}],4:[function(_dereq_,module,exports){function Transport(opts){this.path=opts.path,this.hostname=opts.hostname,this.port=opts.port,this.secure=opts.secure,this.query=opts.query,this.timestampParam=opts.timestampParam,this.timestampRequests=opts.timestampRequests,this.readyState="",this.agent=opts.agent||!1,this.socket=opts.socket,this.enablesXDR=opts.enablesXDR,this.pfx=opts.pfx,this.key=opts.key,this.passphrase=opts.passphrase,this.cert=opts.cert,this.ca=opts.ca,this.ciphers=opts.ciphers,this.rejectUnauthorized=opts.rejectUnauthorized,this.extraHeaders=opts.extraHeaders}var parser=_dereq_("engine.io-parser"),Emitter=_dereq_("component-emitter");module.exports=Transport,Emitter(Transport.prototype),Transport.prototype.onError=function(msg,desc){var err=new Error(msg);return err.type="TransportError",err.description=desc,this.emit("error",err),this},Transport.prototype.open=function(){return"closed"!=this.readyState&&""!=this.readyState||(this.readyState="opening",this.doOpen()),this},Transport.prototype.close=function(){return"opening"!=this.readyState&&"open"!=this.readyState||(this.doClose(),this.onClose()),this},Transport.prototype.send=function(packets){if("open"!=this.readyState)throw new Error("Transport not open");this.write(packets)},Transport.prototype.onOpen=function(){this.readyState="open",this.writable=!0,this.emit("open")},Transport.prototype.onData=function(data){var packet=parser.decodePacket(data,this.socket.binaryType);this.onPacket(packet)},Transport.prototype.onPacket=function(packet){this.emit("packet",packet)},Transport.prototype.onClose=function(){this.readyState="closed",this.emit("close")}},{"component-emitter":15,"engine.io-parser":19}],5:[function(_dereq_,module,exports){(function(global){function polling(opts){var xhr,xd=!1,xs=!1,jsonp=!1!==opts.jsonp;if(global.location){var isSSL="https:"==location.protocol,port=location.port;port||(port=isSSL?443:80),xd=opts.hostname!=location.hostname||port!=opts.port,xs=opts.secure!=isSSL}if(opts.xdomain=xd,opts.xscheme=xs,xhr=new XMLHttpRequest(opts),"open"in xhr&&!opts.forceJSONP)return new XHR(opts);if(!jsonp)throw new Error("JSONP disabled");return new JSONP(opts)}var XMLHttpRequest=_dereq_("xmlhttprequest-ssl"),XHR=_dereq_("./polling-xhr"),JSONP=_dereq_("./polling-jsonp"),websocket=_dereq_("./websocket");exports.polling=polling,exports.websocket=websocket}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})},{"./polling-jsonp":6,"./polling-xhr":7,"./websocket":9,"xmlhttprequest-ssl":10}],6:[function(_dereq_,module,exports){(function(global){function empty(){}function JSONPPolling(opts){Polling.call(this,opts),this.query=this.query||{},callbacks||(global.___eio||(global.___eio=[]),callbacks=global.___eio),this.index=callbacks.length;var self=this;callbacks.push(function(msg){self.onData(msg)}),this.query.j=this.index,global.document&&global.addEventListener&&global.addEventListener("beforeunload",function(){self.script&&(self.script.onerror=empty)},!1)}var Polling=_dereq_("./polling"),inherit=_dereq_("component-inherit");module.exports=JSONPPolling;var callbacks,rNewline=/\n/g,rEscapedNewline=/\\n/g;inherit(JSONPPolling,Polling),JSONPPolling.prototype.supportsBinary=!1,JSONPPolling.prototype.doClose=function(){this.script&&(this.script.parentNode.removeChild(this.script),this.script=null),this.form&&(this.form.parentNode.removeChild(this.form),this.form=null,this.iframe=null),Polling.prototype.doClose.call(this)},JSONPPolling.prototype.doPoll=function(){var self=this,script=document.createElement("script");this.script&&(this.script.parentNode.removeChild(this.script),this.script=null),script.async=!0,script.src=this.uri(),script.onerror=function(e){self.onError("jsonp poll error",e)};var insertAt=document.getElementsByTagName("script")[0];insertAt?insertAt.parentNode.insertBefore(script,insertAt):(document.head||document.body).appendChild(script),this.script=script;var isUAgecko="undefined"!=typeof navigator&&/gecko/i.test(navigator.userAgent);isUAgecko&&setTimeout(function(){var iframe=document.createElement("iframe");document.body.appendChild(iframe),document.body.removeChild(iframe)},100)},JSONPPolling.prototype.doWrite=function(data,fn){function complete(){initIframe(),fn()}function initIframe(){if(self.iframe)try{self.form.removeChild(self.iframe)}catch(e){self.onError("jsonp polling iframe removal error",e)}try{var html='<iframe src="javascript:0" name="'+self.iframeId+'">';iframe=document.createElement(html)}catch(e){iframe=document.createElement("iframe"),iframe.name=self.iframeId,iframe.src="javascript:0"}iframe.id=self.iframeId,self.form.appendChild(iframe),self.iframe=iframe}var self=this;if(!this.form){var iframe,form=document.createElement("form"),area=document.createElement("textarea"),id=this.iframeId="eio_iframe_"+this.index;form.className="socketio",form.style.position="absolute",form.style.top="-1000px",form.style.left="-1000px",form.target=id,form.method="POST",form.setAttribute("accept-charset","utf-8"),area.name="d",form.appendChild(area),document.body.appendChild(form),this.form=form,this.area=area}this.form.action=this.uri(),initIframe(),data=data.replace(rEscapedNewline,"\\\n"),this.area.value=data.replace(rNewline,"\\n");try{this.form.submit()}catch(e){}this.iframe.attachEvent?this.iframe.onreadystatechange=function(){"complete"==self.iframe.readyState&&complete()}:this.iframe.onload=complete}}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})},{"./polling":8,"component-inherit":16}],7:[function(_dereq_,module,exports){(function(global){function empty(){}function XHR(opts){if(Polling.call(this,opts),global.location){var isSSL="https:"==location.protocol,port=location.port;port||(port=isSSL?443:80),this.xd=opts.hostname!=global.location.hostname||port!=opts.port,this.xs=opts.secure!=isSSL}else this.extraHeaders=opts.extraHeaders}function Request(opts){this.method=opts.method||"GET",this.uri=opts.uri,this.xd=!!opts.xd,this.xs=!!opts.xs,this.async=!1!==opts.async,this.data=void 0!=opts.data?opts.data:null,this.agent=opts.agent,this.isBinary=opts.isBinary,this.supportsBinary=opts.supportsBinary,this.enablesXDR=opts.enablesXDR,this.pfx=opts.pfx,this.key=opts.key,this.passphrase=opts.passphrase,this.cert=opts.cert,this.ca=opts.ca,this.ciphers=opts.ciphers,this.rejectUnauthorized=opts.rejectUnauthorized,this.extraHeaders=opts.extraHeaders,this.create()}function unloadHandler(){for(var i in Request.requests)Request.requests.hasOwnProperty(i)&&Request.requests[i].abort()}var XMLHttpRequest=_dereq_("xmlhttprequest-ssl"),Polling=_dereq_("./polling"),Emitter=_dereq_("component-emitter"),inherit=_dereq_("component-inherit"),debug=_dereq_("debug")("engine.io-client:polling-xhr");module.exports=XHR,module.exports.Request=Request,inherit(XHR,Polling),XHR.prototype.supportsBinary=!0,XHR.prototype.request=function(opts){return opts=opts||{},opts.uri=this.uri(),opts.xd=this.xd,opts.xs=this.xs,opts.agent=this.agent||!1,opts.supportsBinary=this.supportsBinary,opts.enablesXDR=this.enablesXDR,opts.pfx=this.pfx,opts.key=this.key,opts.passphrase=this.passphrase,opts.cert=this.cert,opts.ca=this.ca,opts.ciphers=this.ciphers,opts.rejectUnauthorized=this.rejectUnauthorized,opts.extraHeaders=this.extraHeaders,new Request(opts)},XHR.prototype.doWrite=function(data,fn){var isBinary="string"!=typeof data&&void 0!==data,req=this.request({method:"POST",data:data,isBinary:isBinary}),self=this;req.on("success",fn),req.on("error",function(err){self.onError("xhr post error",err)}),this.sendXhr=req},XHR.prototype.doPoll=function(){debug("xhr poll");var req=this.request(),self=this;req.on("data",function(data){self.onData(data)}),req.on("error",function(err){self.onError("xhr poll error",err)}),this.pollXhr=req},Emitter(Request.prototype),Request.prototype.create=function(){var opts={agent:this.agent,xdomain:this.xd,xscheme:this.xs,enablesXDR:this.enablesXDR};opts.pfx=this.pfx,opts.key=this.key,opts.passphrase=this.passphrase,opts.cert=this.cert,opts.ca=this.ca,opts.ciphers=this.ciphers,opts.rejectUnauthorized=this.rejectUnauthorized;var xhr=this.xhr=new XMLHttpRequest(opts),self=this;try{debug("xhr open %s: %s",this.method,this.uri),xhr.open(this.method,this.uri,this.async);try{if(this.extraHeaders){xhr.setDisableHeaderCheck(!0);for(var i in this.extraHeaders)this.extraHeaders.hasOwnProperty(i)&&xhr.setRequestHeader(i,this.extraHeaders[i])}}catch(e){}if(this.supportsBinary&&(xhr.responseType="arraybuffer"),"POST"==this.method)try{this.isBinary?xhr.setRequestHeader("Content-type","application/octet-stream"):xhr.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(e){}"withCredentials"in xhr&&(xhr.withCredentials=!0),this.hasXDR()?(xhr.onload=function(){self.onLoad()},xhr.onerror=function(){self.onError(xhr.responseText)}):xhr.onreadystatechange=function(){4==xhr.readyState&&(200==xhr.status||1223==xhr.status?self.onLoad():setTimeout(function(){self.onError(xhr.status)},0))},debug("xhr data %s",this.data),xhr.send(this.data)}catch(e){return void setTimeout(function(){self.onError(e)},0)}global.document&&(this.index=Request.requestsCount++,Request.requests[this.index]=this)},Request.prototype.onSuccess=function(){this.emit("success"),this.cleanup()},Request.prototype.onData=function(data){
this.emit("data",data),this.onSuccess()},Request.prototype.onError=function(err){this.emit("error",err),this.cleanup(!0)},Request.prototype.cleanup=function(fromError){if("undefined"!=typeof this.xhr&&null!==this.xhr){if(this.hasXDR()?this.xhr.onload=this.xhr.onerror=empty:this.xhr.onreadystatechange=empty,fromError)try{this.xhr.abort()}catch(e){}global.document&&delete Request.requests[this.index],this.xhr=null}},Request.prototype.onLoad=function(){var data;try{var contentType;try{contentType=this.xhr.getResponseHeader("Content-Type").split(";")[0]}catch(e){}if("application/octet-stream"===contentType)data=this.xhr.response;else if(this.supportsBinary)try{data=String.fromCharCode.apply(null,new Uint8Array(this.xhr.response))}catch(e){for(var ui8Arr=new Uint8Array(this.xhr.response),dataArray=[],idx=0,length=ui8Arr.length;idx<length;idx++)dataArray.push(ui8Arr[idx]);data=String.fromCharCode.apply(null,dataArray)}else data=this.xhr.responseText}catch(e){this.onError(e)}null!=data&&this.onData(data)},Request.prototype.hasXDR=function(){return"undefined"!=typeof global.XDomainRequest&&!this.xs&&this.enablesXDR},Request.prototype.abort=function(){this.cleanup()},global.document&&(Request.requestsCount=0,Request.requests={},global.attachEvent?global.attachEvent("onunload",unloadHandler):global.addEventListener&&global.addEventListener("beforeunload",unloadHandler,!1))}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})},{"./polling":8,"component-emitter":15,"component-inherit":16,debug:17,"xmlhttprequest-ssl":10}],8:[function(_dereq_,module,exports){function Polling(opts){var forceBase64=opts&&opts.forceBase64;hasXHR2&&!forceBase64||(this.supportsBinary=!1),Transport.call(this,opts)}var Transport=_dereq_("../transport"),parseqs=_dereq_("parseqs"),parser=_dereq_("engine.io-parser"),inherit=_dereq_("component-inherit"),yeast=_dereq_("yeast"),debug=_dereq_("debug")("engine.io-client:polling");module.exports=Polling;var hasXHR2=function(){var XMLHttpRequest=_dereq_("xmlhttprequest-ssl"),xhr=new XMLHttpRequest({xdomain:!1});return null!=xhr.responseType}();inherit(Polling,Transport),Polling.prototype.name="polling",Polling.prototype.doOpen=function(){this.poll()},Polling.prototype.pause=function(onPause){function pause(){debug("paused"),self.readyState="paused",onPause()}var self=this;if(this.readyState="pausing",this.polling||!this.writable){var total=0;this.polling&&(debug("we are currently polling - waiting to pause"),total++,this.once("pollComplete",function(){debug("pre-pause polling complete"),--total||pause()})),this.writable||(debug("we are currently writing - waiting to pause"),total++,this.once("drain",function(){debug("pre-pause writing complete"),--total||pause()}))}else pause()},Polling.prototype.poll=function(){debug("polling"),this.polling=!0,this.doPoll(),this.emit("poll")},Polling.prototype.onData=function(data){var self=this;debug("polling got data %s",data);var callback=function(packet,index,total){return"opening"==self.readyState&&self.onOpen(),"close"==packet.type?(self.onClose(),!1):void self.onPacket(packet)};parser.decodePayload(data,this.socket.binaryType,callback),"closed"!=this.readyState&&(this.polling=!1,this.emit("pollComplete"),"open"==this.readyState?this.poll():debug('ignoring poll - transport state "%s"',this.readyState))},Polling.prototype.doClose=function(){function close(){debug("writing close packet"),self.write([{type:"close"}])}var self=this;"open"==this.readyState?(debug("transport open - closing"),close()):(debug("transport not open - deferring close"),this.once("open",close))},Polling.prototype.write=function(packets){var self=this;this.writable=!1;var callbackfn=function(){self.writable=!0,self.emit("drain")},self=this;parser.encodePayload(packets,this.supportsBinary,function(data){self.doWrite(data,callbackfn)})},Polling.prototype.uri=function(){var query=this.query||{},schema=this.secure?"https":"http",port="";!1!==this.timestampRequests&&(query[this.timestampParam]=yeast()),this.supportsBinary||query.sid||(query.b64=1),query=parseqs.encode(query),this.port&&("https"==schema&&443!=this.port||"http"==schema&&80!=this.port)&&(port=":"+this.port),query.length&&(query="?"+query);var ipv6=this.hostname.indexOf(":")!==-1;return schema+"://"+(ipv6?"["+this.hostname+"]":this.hostname)+port+this.path+query}},{"../transport":4,"component-inherit":16,debug:17,"engine.io-parser":19,parseqs:27,"xmlhttprequest-ssl":10,yeast:30}],9:[function(_dereq_,module,exports){(function(global){function WS(opts){var forceBase64=opts&&opts.forceBase64;forceBase64&&(this.supportsBinary=!1),this.perMessageDeflate=opts.perMessageDeflate,Transport.call(this,opts)}var Transport=_dereq_("../transport"),parser=_dereq_("engine.io-parser"),parseqs=_dereq_("parseqs"),inherit=_dereq_("component-inherit"),yeast=_dereq_("yeast"),debug=_dereq_("debug")("engine.io-client:websocket"),BrowserWebSocket=global.WebSocket||global.MozWebSocket,WebSocket=BrowserWebSocket;if(!WebSocket&&"undefined"==typeof window)try{WebSocket=_dereq_("ws")}catch(e){}module.exports=WS,inherit(WS,Transport),WS.prototype.name="websocket",WS.prototype.supportsBinary=!0,WS.prototype.doOpen=function(){if(this.check()){var uri=this.uri(),protocols=void 0,opts={agent:this.agent,perMessageDeflate:this.perMessageDeflate};opts.pfx=this.pfx,opts.key=this.key,opts.passphrase=this.passphrase,opts.cert=this.cert,opts.ca=this.ca,opts.ciphers=this.ciphers,opts.rejectUnauthorized=this.rejectUnauthorized,this.extraHeaders&&(opts.headers=this.extraHeaders),this.ws=BrowserWebSocket?new WebSocket(uri):new WebSocket(uri,protocols,opts),void 0===this.ws.binaryType&&(this.supportsBinary=!1),this.ws.supports&&this.ws.supports.binary?(this.supportsBinary=!0,this.ws.binaryType="buffer"):this.ws.binaryType="arraybuffer",this.addEventListeners()}},WS.prototype.addEventListeners=function(){var self=this;this.ws.onopen=function(){self.onOpen()},this.ws.onclose=function(){self.onClose()},this.ws.onmessage=function(ev){self.onData(ev.data)},this.ws.onerror=function(e){self.onError("websocket error",e)}},"undefined"!=typeof navigator&&/iPad|iPhone|iPod/i.test(navigator.userAgent)&&(WS.prototype.onData=function(data){var self=this;setTimeout(function(){Transport.prototype.onData.call(self,data)},0)}),WS.prototype.write=function(packets){function done(){self.emit("flush"),setTimeout(function(){self.writable=!0,self.emit("drain")},0)}var self=this;this.writable=!1;for(var total=packets.length,i=0,l=total;i<l;i++)!function(packet){parser.encodePacket(packet,self.supportsBinary,function(data){if(!BrowserWebSocket){var opts={};if(packet.options&&(opts.compress=packet.options.compress),self.perMessageDeflate){var len="string"==typeof data?global.Buffer.byteLength(data):data.length;len<self.perMessageDeflate.threshold&&(opts.compress=!1)}}try{BrowserWebSocket?self.ws.send(data):self.ws.send(data,opts)}catch(e){debug("websocket closed before onclose event")}--total||done()})}(packets[i])},WS.prototype.onClose=function(){Transport.prototype.onClose.call(this)},WS.prototype.doClose=function(){"undefined"!=typeof this.ws&&this.ws.close()},WS.prototype.uri=function(){var query=this.query||{},schema=this.secure?"wss":"ws",port="";this.port&&("wss"==schema&&443!=this.port||"ws"==schema&&80!=this.port)&&(port=":"+this.port),this.timestampRequests&&(query[this.timestampParam]=yeast()),this.supportsBinary||(query.b64=1),query=parseqs.encode(query),query.length&&(query="?"+query);var ipv6=this.hostname.indexOf(":")!==-1;return schema+"://"+(ipv6?"["+this.hostname+"]":this.hostname)+port+this.path+query},WS.prototype.check=function(){return!(!WebSocket||"__initialize"in WebSocket&&this.name===WS.prototype.name)}}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})},{"../transport":4,"component-inherit":16,debug:17,"engine.io-parser":19,parseqs:27,ws:void 0,yeast:30}],10:[function(_dereq_,module,exports){var hasCORS=_dereq_("has-cors");module.exports=function(opts){var xdomain=opts.xdomain,xscheme=opts.xscheme,enablesXDR=opts.enablesXDR;try{if("undefined"!=typeof XMLHttpRequest&&(!xdomain||hasCORS))return new XMLHttpRequest}catch(e){}try{if("undefined"!=typeof XDomainRequest&&!xscheme&&enablesXDR)return new XDomainRequest}catch(e){}if(!xdomain)try{return new ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}},{"has-cors":22}],11:[function(_dereq_,module,exports){function after(count,callback,err_cb){function proxy(err,result){if(proxy.count<=0)throw new Error("after called too many times");--proxy.count,err?(bail=!0,callback(err),callback=err_cb):0!==proxy.count||bail||callback(null,result)}var bail=!1;return err_cb=err_cb||noop,proxy.count=count,0===count?callback():proxy}function noop(){}module.exports=after},{}],12:[function(_dereq_,module,exports){module.exports=function(arraybuffer,start,end){var bytes=arraybuffer.byteLength;if(start=start||0,end=end||bytes,arraybuffer.slice)return arraybuffer.slice(start,end);if(start<0&&(start+=bytes),end<0&&(end+=bytes),end>bytes&&(end=bytes),start>=bytes||start>=end||0===bytes)return new ArrayBuffer(0);for(var abv=new Uint8Array(arraybuffer),result=new Uint8Array(end-start),i=start,ii=0;i<end;i++,ii++)result[ii]=abv[i];return result.buffer}},{}],13:[function(_dereq_,module,exports){!function(chars){exports.encode=function(arraybuffer){var i,bytes=new Uint8Array(arraybuffer),len=bytes.length,base64="";for(i=0;i<len;i+=3)base64+=chars[bytes[i]>>2],base64+=chars[(3&bytes[i])<<4|bytes[i+1]>>4],base64+=chars[(15&bytes[i+1])<<2|bytes[i+2]>>6],base64+=chars[63&bytes[i+2]];return len%3===2?base64=base64.substring(0,base64.length-1)+"=":len%3===1&&(base64=base64.substring(0,base64.length-2)+"=="),base64},exports.decode=function(base64){var i,encoded1,encoded2,encoded3,encoded4,bufferLength=.75*base64.length,len=base64.length,p=0;"="===base64[base64.length-1]&&(bufferLength--,"="===base64[base64.length-2]&&bufferLength--);var arraybuffer=new ArrayBuffer(bufferLength),bytes=new Uint8Array(arraybuffer);for(i=0;i<len;i+=4)encoded1=chars.indexOf(base64[i]),encoded2=chars.indexOf(base64[i+1]),encoded3=chars.indexOf(base64[i+2]),encoded4=chars.indexOf(base64[i+3]),bytes[p++]=encoded1<<2|encoded2>>4,bytes[p++]=(15&encoded2)<<4|encoded3>>2,bytes[p++]=(3&encoded3)<<6|63&encoded4;return arraybuffer}}("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/")},{}],14:[function(_dereq_,module,exports){(function(global){function mapArrayBufferViews(ary){for(var i=0;i<ary.length;i++){var chunk=ary[i];if(chunk.buffer instanceof ArrayBuffer){var buf=chunk.buffer;if(chunk.byteLength!==buf.byteLength){var copy=new Uint8Array(chunk.byteLength);copy.set(new Uint8Array(buf,chunk.byteOffset,chunk.byteLength)),buf=copy.buffer}ary[i]=buf}}}function BlobBuilderConstructor(ary,options){options=options||{};var bb=new BlobBuilder;mapArrayBufferViews(ary);for(var i=0;i<ary.length;i++)bb.append(ary[i]);return options.type?bb.getBlob(options.type):bb.getBlob()}function BlobConstructor(ary,options){return mapArrayBufferViews(ary),new Blob(ary,options||{})}var BlobBuilder=global.BlobBuilder||global.WebKitBlobBuilder||global.MSBlobBuilder||global.MozBlobBuilder,blobSupported=function(){try{var a=new Blob(["hi"]);return 2===a.size}catch(e){return!1}}(),blobSupportsArrayBufferView=blobSupported&&function(){try{var b=new Blob([new Uint8Array([1,2])]);return 2===b.size}catch(e){return!1}}(),blobBuilderSupported=BlobBuilder&&BlobBuilder.prototype.append&&BlobBuilder.prototype.getBlob;module.exports=function(){return blobSupported?blobSupportsArrayBufferView?global.Blob:BlobConstructor:blobBuilderSupported?BlobBuilderConstructor:void 0}()}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})},{}],15:[function(_dereq_,module,exports){function Emitter(obj){if(obj)return mixin(obj)}function mixin(obj){for(var key in Emitter.prototype)obj[key]=Emitter.prototype[key];return obj}module.exports=Emitter,Emitter.prototype.on=Emitter.prototype.addEventListener=function(event,fn){return this._callbacks=this._callbacks||{},(this._callbacks[event]=this._callbacks[event]||[]).push(fn),this},Emitter.prototype.once=function(event,fn){function on(){self.off(event,on),fn.apply(this,arguments)}var self=this;return this._callbacks=this._callbacks||{},on.fn=fn,this.on(event,on),this},Emitter.prototype.off=Emitter.prototype.removeListener=Emitter.prototype.removeAllListeners=Emitter.prototype.removeEventListener=function(event,fn){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var callbacks=this._callbacks[event];if(!callbacks)return this;if(1==arguments.length)return delete this._callbacks[event],this;for(var cb,i=0;i<callbacks.length;i++)if(cb=callbacks[i],cb===fn||cb.fn===fn){callbacks.splice(i,1);break}return this},Emitter.prototype.emit=function(event){this._callbacks=this._callbacks||{};var args=[].slice.call(arguments,1),callbacks=this._callbacks[event];if(callbacks){callbacks=callbacks.slice(0);for(var i=0,len=callbacks.length;i<len;++i)callbacks[i].apply(this,args)}return this},Emitter.prototype.listeners=function(event){return this._callbacks=this._callbacks||{},this._callbacks[event]||[]},Emitter.prototype.hasListeners=function(event){return!!this.listeners(event).length}},{}],16:[function(_dereq_,module,exports){module.exports=function(a,b){var fn=function(){};fn.prototype=b.prototype,a.prototype=new fn,a.prototype.constructor=a}},{}],17:[function(_dereq_,module,exports){function useColors(){return"WebkitAppearance"in document.documentElement.style||window.console&&(console.firebug||console.exception&&console.table)||navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31}function formatArgs(){var args=arguments,useColors=this.useColors;if(args[0]=(useColors?"%c":"")+this.namespace+(useColors?" %c":" ")+args[0]+(useColors?"%c ":" ")+"+"+exports.humanize(this.diff),!useColors)return args;var c="color: "+this.color;args=[args[0],c,"color: inherit"].concat(Array.prototype.slice.call(args,1));var index=0,lastC=0;return args[0].replace(/%[a-z%]/g,function(match){"%%"!==match&&(index++,"%c"===match&&(lastC=index))}),args.splice(lastC,0,c),args}function log(){return"object"===("undefined"==typeof console?"undefined":_typeof(console))&&console.log&&Function.prototype.apply.call(console.log,console,arguments)}function save(namespaces){try{null==namespaces?exports.storage.removeItem("debug"):exports.storage.debug=namespaces}catch(e){}}function load(){var r;try{r=exports.storage.debug}catch(e){}return r}function localstorage(){try{return window.localStorage}catch(e){}}exports=module.exports=_dereq_("./debug"),exports.log=log,exports.formatArgs=formatArgs,exports.save=save,exports.load=load,exports.useColors=useColors,exports.storage="undefined"!=typeof chrome&&"undefined"!=typeof chrome.storage?chrome.storage.local:localstorage(),exports.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"],exports.formatters.j=function(v){return JSON.stringify(v)},exports.enable(load())},{"./debug":18}],18:[function(_dereq_,module,exports){function selectColor(){return exports.colors[prevColor++%exports.colors.length]}function debug(namespace){function disabled(){}function enabled(){var self=enabled,curr=+new Date,ms=curr-(prevTime||curr);self.diff=ms,self.prev=prevTime,self.curr=curr,prevTime=curr,null==self.useColors&&(self.useColors=exports.useColors()),null==self.color&&self.useColors&&(self.color=selectColor());var args=Array.prototype.slice.call(arguments);args[0]=exports.coerce(args[0]),"string"!=typeof args[0]&&(args=["%o"].concat(args));var index=0;args[0]=args[0].replace(/%([a-z%])/g,function(match,format){if("%%"===match)return match;index++;var formatter=exports.formatters[format];if("function"==typeof formatter){var val=args[index];match=formatter.call(self,val),args.splice(index,1),index--}return match}),"function"==typeof exports.formatArgs&&(args=exports.formatArgs.apply(self,args));var logFn=enabled.log||exports.log||console.log.bind(console);logFn.apply(self,args)}disabled.enabled=!1,enabled.enabled=!0;var fn=exports.enabled(namespace)?enabled:disabled;return fn.namespace=namespace,fn}function enable(namespaces){exports.save(namespaces);for(var split=(namespaces||"").split(/[\s,]+/),len=split.length,i=0;i<len;i++)split[i]&&(namespaces=split[i].replace(/\*/g,".*?"),"-"===namespaces[0]?exports.skips.push(new RegExp("^"+namespaces.substr(1)+"$")):exports.names.push(new RegExp("^"+namespaces+"$")))}function disable(){exports.enable("")}function enabled(name){var i,len;for(i=0,len=exports.skips.length;i<len;i++)if(exports.skips[i].test(name))return!1;for(i=0,len=exports.names.length;i<len;i++)if(exports.names[i].test(name))return!0;return!1}function coerce(val){return val instanceof Error?val.stack||val.message:val}exports=module.exports=debug,exports.coerce=coerce,exports.disable=disable,exports.enable=enable,exports.enabled=enabled,exports.humanize=_dereq_("ms"),exports.names=[],exports.skips=[],exports.formatters={};var prevTime,prevColor=0},{ms:25}],19:[function(_dereq_,module,exports){(function(global){function encodeBase64Object(packet,callback){var message="b"+exports.packets[packet.type]+packet.data.data;return callback(message)}function encodeArrayBuffer(packet,supportsBinary,callback){if(!supportsBinary)return exports.encodeBase64Packet(packet,callback);var data=packet.data,contentArray=new Uint8Array(data),resultBuffer=new Uint8Array(1+data.byteLength);resultBuffer[0]=packets[packet.type];for(var i=0;i<contentArray.length;i++)resultBuffer[i+1]=contentArray[i];return callback(resultBuffer.buffer)}function encodeBlobAsArrayBuffer(packet,supportsBinary,callback){if(!supportsBinary)return exports.encodeBase64Packet(packet,callback);var fr=new FileReader;return fr.onload=function(){packet.data=fr.result,exports.encodePacket(packet,supportsBinary,!0,callback)},fr.readAsArrayBuffer(packet.data)}function encodeBlob(packet,supportsBinary,callback){if(!supportsBinary)return exports.encodeBase64Packet(packet,callback);if(dontSendBlobs)return encodeBlobAsArrayBuffer(packet,supportsBinary,callback);var length=new Uint8Array(1);length[0]=packets[packet.type];var blob=new Blob([length.buffer,packet.data]);return callback(blob)}function map(ary,each,done){for(var result=new Array(ary.length),next=after(ary.length,done),eachWithIndex=function(i,el,cb){each(el,function(error,msg){result[i]=msg,cb(error,result)})},i=0;i<ary.length;i++)eachWithIndex(i,ary[i],next)}var keys=_dereq_("./keys"),hasBinary=_dereq_("has-binary"),sliceBuffer=_dereq_("arraybuffer.slice"),base64encoder=_dereq_("base64-arraybuffer"),after=_dereq_("after"),utf8=_dereq_("utf8"),isAndroid=navigator.userAgent.match(/Android/i),isPhantomJS=/PhantomJS/i.test(navigator.userAgent),dontSendBlobs=isAndroid||isPhantomJS;exports.protocol=3;var packets=exports.packets={open:0,close:1,ping:2,pong:3,message:4,upgrade:5,noop:6},packetslist=keys(packets),err={type:"error",data:"parser error"},Blob=_dereq_("blob");exports.encodePacket=function(packet,supportsBinary,utf8encode,callback){"function"==typeof supportsBinary&&(callback=supportsBinary,supportsBinary=!1),"function"==typeof utf8encode&&(callback=utf8encode,utf8encode=null);var data=void 0===packet.data?void 0:packet.data.buffer||packet.data;if(global.ArrayBuffer&&data instanceof ArrayBuffer)return encodeArrayBuffer(packet,supportsBinary,callback);if(Blob&&data instanceof global.Blob)return encodeBlob(packet,supportsBinary,callback);if(data&&data.base64)return encodeBase64Object(packet,callback);var encoded=packets[packet.type];return void 0!==packet.data&&(encoded+=utf8encode?utf8.encode(String(packet.data)):String(packet.data)),callback(""+encoded)},exports.encodeBase64Packet=function(packet,callback){var message="b"+exports.packets[packet.type];if(Blob&&packet.data instanceof global.Blob){var fr=new FileReader;return fr.onload=function(){var b64=fr.result.split(",")[1];callback(message+b64)},fr.readAsDataURL(packet.data)}var b64data;try{b64data=String.fromCharCode.apply(null,new Uint8Array(packet.data))}catch(e){for(var typed=new Uint8Array(packet.data),basic=new Array(typed.length),i=0;i<typed.length;i++)basic[i]=typed[i];b64data=String.fromCharCode.apply(null,basic)}return message+=global.btoa(b64data),callback(message)},exports.decodePacket=function(data,binaryType,utf8decode){if("string"==typeof data||void 0===data){if("b"==data.charAt(0))return exports.decodeBase64Packet(data.substr(1),binaryType);if(utf8decode)try{data=utf8.decode(data)}catch(e){return err}var type=data.charAt(0);return Number(type)==type&&packetslist[type]?data.length>1?{type:packetslist[type],data:data.substring(1)}:{type:packetslist[type]}:err}var asArray=new Uint8Array(data),type=asArray[0],rest=sliceBuffer(data,1);return Blob&&"blob"===binaryType&&(rest=new Blob([rest])),{type:packetslist[type],data:rest}},exports.decodeBase64Packet=function(msg,binaryType){var type=packetslist[msg.charAt(0)];if(!global.ArrayBuffer)return{type:type,data:{base64:!0,data:msg.substr(1)}};var data=base64encoder.decode(msg.substr(1));return"blob"===binaryType&&Blob&&(data=new Blob([data])),{type:type,data:data}},exports.encodePayload=function(packets,supportsBinary,callback){function setLengthHeader(message){return message.length+":"+message}function encodeOne(packet,doneCallback){exports.encodePacket(packet,!!isBinary&&supportsBinary,!0,function(message){doneCallback(null,setLengthHeader(message))})}"function"==typeof supportsBinary&&(callback=supportsBinary,supportsBinary=null);var isBinary=hasBinary(packets);return supportsBinary&&isBinary?Blob&&!dontSendBlobs?exports.encodePayloadAsBlob(packets,callback):exports.encodePayloadAsArrayBuffer(packets,callback):packets.length?void map(packets,encodeOne,function(err,results){return callback(results.join(""))}):callback("0:")},exports.decodePayload=function(data,binaryType,callback){if("string"!=typeof data)return exports.decodePayloadAsBinary(data,binaryType,callback);"function"==typeof binaryType&&(callback=binaryType,binaryType=null);var packet;if(""==data)return callback(err,0,1);for(var n,msg,length="",i=0,l=data.length;i<l;i++){var chr=data.charAt(i);if(":"!=chr)length+=chr;else{if(""==length||length!=(n=Number(length)))return callback(err,0,1);if(msg=data.substr(i+1,n),length!=msg.length)return callback(err,0,1);if(msg.length){if(packet=exports.decodePacket(msg,binaryType,!0),err.type==packet.type&&err.data==packet.data)return callback(err,0,1);var ret=callback(packet,i+n,l);if(!1===ret)return}i+=n,length=""}}return""!=length?callback(err,0,1):void 0},exports.encodePayloadAsArrayBuffer=function(packets,callback){function encodeOne(packet,doneCallback){exports.encodePacket(packet,!0,!0,function(data){return doneCallback(null,data)})}return packets.length?void map(packets,encodeOne,function(err,encodedPackets){var totalLength=encodedPackets.reduce(function(acc,p){var len;return len="string"==typeof p?p.length:p.byteLength,acc+len.toString().length+len+2},0),resultArray=new Uint8Array(totalLength),bufferIndex=0;return encodedPackets.forEach(function(p){var isString="string"==typeof p,ab=p;if(isString){for(var view=new Uint8Array(p.length),i=0;i<p.length;i++)view[i]=p.charCodeAt(i);ab=view.buffer}isString?resultArray[bufferIndex++]=0:resultArray[bufferIndex++]=1;for(var lenStr=ab.byteLength.toString(),i=0;i<lenStr.length;i++)resultArray[bufferIndex++]=parseInt(lenStr[i]);resultArray[bufferIndex++]=255;for(var view=new Uint8Array(ab),i=0;i<view.length;i++)resultArray[bufferIndex++]=view[i]}),callback(resultArray.buffer)}):callback(new ArrayBuffer(0))},exports.encodePayloadAsBlob=function(packets,callback){function encodeOne(packet,doneCallback){exports.encodePacket(packet,!0,!0,function(encoded){var binaryIdentifier=new Uint8Array(1);if(binaryIdentifier[0]=1,"string"==typeof encoded){for(var view=new Uint8Array(encoded.length),i=0;i<encoded.length;i++)view[i]=encoded.charCodeAt(i);encoded=view.buffer,binaryIdentifier[0]=0}for(var len=encoded instanceof ArrayBuffer?encoded.byteLength:encoded.size,lenStr=len.toString(),lengthAry=new Uint8Array(lenStr.length+1),i=0;i<lenStr.length;i++)lengthAry[i]=parseInt(lenStr[i]);if(lengthAry[lenStr.length]=255,Blob){var blob=new Blob([binaryIdentifier.buffer,lengthAry.buffer,encoded]);doneCallback(null,blob)}})}map(packets,encodeOne,function(err,results){return callback(new Blob(results))})},exports.decodePayloadAsBinary=function(data,binaryType,callback){"function"==typeof binaryType&&(callback=binaryType,binaryType=null);for(var bufferTail=data,buffers=[],numberTooLong=!1;bufferTail.byteLength>0;){for(var tailArray=new Uint8Array(bufferTail),isString=0===tailArray[0],msgLength="",i=1;255!=tailArray[i];i++){if(msgLength.length>310){numberTooLong=!0;break}msgLength+=tailArray[i]}if(numberTooLong)return callback(err,0,1);bufferTail=sliceBuffer(bufferTail,2+msgLength.length),msgLength=parseInt(msgLength);var msg=sliceBuffer(bufferTail,0,msgLength);if(isString)try{msg=String.fromCharCode.apply(null,new Uint8Array(msg))}catch(e){var typed=new Uint8Array(msg);msg="";for(var i=0;i<typed.length;i++)msg+=String.fromCharCode(typed[i])}buffers.push(msg),bufferTail=sliceBuffer(bufferTail,msgLength)}var total=buffers.length;buffers.forEach(function(buffer,i){callback(exports.decodePacket(buffer,binaryType,!0),i,total)})}}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})},{"./keys":20,after:11,"arraybuffer.slice":12,"base64-arraybuffer":13,blob:14,"has-binary":21,utf8:29}],20:[function(_dereq_,module,exports){module.exports=Object.keys||function(obj){var arr=[],has=Object.prototype.hasOwnProperty;for(var i in obj)has.call(obj,i)&&arr.push(i);return arr}},{}],21:[function(_dereq_,module,exports){(function(global){function hasBinary(data){function _hasBinary(obj){if(!obj)return!1;if(global.Buffer&&global.Buffer.isBuffer(obj)||global.ArrayBuffer&&obj instanceof ArrayBuffer||global.Blob&&obj instanceof Blob||global.File&&obj instanceof File)return!0;if(isArray(obj)){for(var i=0;i<obj.length;i++)if(_hasBinary(obj[i]))return!0}else if(obj&&"object"==("undefined"==typeof obj?"undefined":_typeof(obj))){obj.toJSON&&(obj=obj.toJSON());for(var key in obj)if(Object.prototype.hasOwnProperty.call(obj,key)&&_hasBinary(obj[key]))return!0}return!1}return _hasBinary(data)}var isArray=_dereq_("isarray");module.exports=hasBinary}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})},{isarray:24}],22:[function(_dereq_,module,exports){try{module.exports="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(err){module.exports=!1}},{}],23:[function(_dereq_,module,exports){var indexOf=[].indexOf;module.exports=function(arr,obj){if(indexOf)return arr.indexOf(obj);for(var i=0;i<arr.length;++i)if(arr[i]===obj)return i;return-1}},{}],24:[function(_dereq_,module,exports){module.exports=Array.isArray||function(arr){return"[object Array]"==Object.prototype.toString.call(arr)}},{}],25:[function(_dereq_,module,exports){function parse(str){if(str=""+str,!(str.length>1e4)){var match=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(str);if(match){var n=parseFloat(match[1]),type=(match[2]||"ms").toLowerCase();switch(type){case"years":case"year":case"yrs":case"yr":case"y":return n*y;case"days":case"day":case"d":return n*d;case"hours":case"hour":case"hrs":case"hr":case"h":return n*h;case"minutes":case"minute":case"mins":case"min":case"m":return n*m;case"seconds":case"second":case"secs":case"sec":case"s":return n*s;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return n}}}}function short(ms){return ms>=d?Math.round(ms/d)+"d":ms>=h?Math.round(ms/h)+"h":ms>=m?Math.round(ms/m)+"m":ms>=s?Math.round(ms/s)+"s":ms+"ms"}function long(ms){return plural(ms,d,"day")||plural(ms,h,"hour")||plural(ms,m,"minute")||plural(ms,s,"second")||ms+" ms"}function plural(ms,n,name){if(!(ms<n))return ms<1.5*n?Math.floor(ms/n)+" "+name:Math.ceil(ms/n)+" "+name+"s"}var s=1e3,m=60*s,h=60*m,d=24*h,y=365.25*d;module.exports=function(val,options){return options=options||{},"string"==typeof val?parse(val):options.long?long(val):short(val)}},{}],26:[function(_dereq_,module,exports){(function(global){var rvalidchars=/^[\],:{}\s]*$/,rvalidescape=/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,rvalidtokens=/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,rvalidbraces=/(?:^|:|,)(?:\s*\[)+/g,rtrimLeft=/^\s+/,rtrimRight=/\s+$/;module.exports=function(data){return"string"==typeof data&&data?(data=data.replace(rtrimLeft,"").replace(rtrimRight,""),global.JSON&&JSON.parse?JSON.parse(data):rvalidchars.test(data.replace(rvalidescape,"@").replace(rvalidtokens,"]").replace(rvalidbraces,""))?new Function("return "+data)():void 0):null}}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})},{}],27:[function(_dereq_,module,exports){exports.encode=function(obj){var str="";for(var i in obj)obj.hasOwnProperty(i)&&(str.length&&(str+="&"),str+=encodeURIComponent(i)+"="+encodeURIComponent(obj[i]));return str},exports.decode=function(qs){for(var qry={},pairs=qs.split("&"),i=0,l=pairs.length;i<l;i++){var pair=pairs[i].split("=");qry[decodeURIComponent(pair[0])]=decodeURIComponent(pair[1])}return qry}},{}],28:[function(_dereq_,module,exports){var re=/^(?:(?![^:@]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,parts=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];module.exports=function(str){var src=str,b=str.indexOf("["),e=str.indexOf("]");b!=-1&&e!=-1&&(str=str.substring(0,b)+str.substring(b,e).replace(/:/g,";")+str.substring(e,str.length));for(var m=re.exec(str||""),uri={},i=14;i--;)uri[parts[i]]=m[i]||"";return b!=-1&&e!=-1&&(uri.source=src,uri.host=uri.host.substring(1,uri.host.length-1).replace(/;/g,":"),uri.authority=uri.authority.replace("[","").replace("]","").replace(/;/g,":"),uri.ipv6uri=!0),uri}},{}],29:[function(_dereq_,module,exports){(function(global){!function(root){function ucs2decode(string){for(var value,extra,output=[],counter=0,length=string.length;counter<length;)value=string.charCodeAt(counter++),value>=55296&&value<=56319&&counter<length?(extra=string.charCodeAt(counter++),56320==(64512&extra)?output.push(((1023&value)<<10)+(1023&extra)+65536):(output.push(value),counter--)):output.push(value);return output}function ucs2encode(array){for(var value,length=array.length,index=-1,output="";++index<length;)value=array[index],value>65535&&(value-=65536,output+=stringFromCharCode(value>>>10&1023|55296),value=56320|1023&value),output+=stringFromCharCode(value);return output}function checkScalarValue(codePoint){if(codePoint>=55296&&codePoint<=57343)throw Error("Lone surrogate U+"+codePoint.toString(16).toUpperCase()+" is not a scalar value")}function createByte(codePoint,shift){return stringFromCharCode(codePoint>>shift&63|128)}function encodeCodePoint(codePoint){if(0==(4294967168&codePoint))return stringFromCharCode(codePoint);var symbol="";return 0==(4294965248&codePoint)?symbol=stringFromCharCode(codePoint>>6&31|192):0==(4294901760&codePoint)?(checkScalarValue(codePoint),symbol=stringFromCharCode(codePoint>>12&15|224),symbol+=createByte(codePoint,6)):0==(4292870144&codePoint)&&(symbol=stringFromCharCode(codePoint>>18&7|240),symbol+=createByte(codePoint,12),symbol+=createByte(codePoint,6)),symbol+=stringFromCharCode(63&codePoint|128)}function utf8encode(string){for(var codePoint,codePoints=ucs2decode(string),length=codePoints.length,index=-1,byteString="";++index<length;)codePoint=codePoints[index],
byteString+=encodeCodePoint(codePoint);return byteString}function readContinuationByte(){if(byteIndex>=byteCount)throw Error("Invalid byte index");var continuationByte=255&byteArray[byteIndex];if(byteIndex++,128==(192&continuationByte))return 63&continuationByte;throw Error("Invalid continuation byte")}function decodeSymbol(){var byte1,byte2,byte3,byte4,codePoint;if(byteIndex>byteCount)throw Error("Invalid byte index");if(byteIndex==byteCount)return!1;if(byte1=255&byteArray[byteIndex],byteIndex++,0==(128&byte1))return byte1;if(192==(224&byte1)){var byte2=readContinuationByte();if(codePoint=(31&byte1)<<6|byte2,codePoint>=128)return codePoint;throw Error("Invalid continuation byte")}if(224==(240&byte1)){if(byte2=readContinuationByte(),byte3=readContinuationByte(),codePoint=(15&byte1)<<12|byte2<<6|byte3,codePoint>=2048)return checkScalarValue(codePoint),codePoint;throw Error("Invalid continuation byte")}if(240==(248&byte1)&&(byte2=readContinuationByte(),byte3=readContinuationByte(),byte4=readContinuationByte(),codePoint=(15&byte1)<<18|byte2<<12|byte3<<6|byte4,codePoint>=65536&&codePoint<=1114111))return codePoint;throw Error("Invalid UTF-8 detected")}function utf8decode(byteString){byteArray=ucs2decode(byteString),byteCount=byteArray.length,byteIndex=0;for(var tmp,codePoints=[];(tmp=decodeSymbol())!==!1;)codePoints.push(tmp);return ucs2encode(codePoints)}var freeExports="object"==("undefined"==typeof exports?"undefined":_typeof(exports))&&exports,freeModule="object"==("undefined"==typeof module?"undefined":_typeof(module))&&module&&module.exports==freeExports&&module,freeGlobal="object"==("undefined"==typeof global?"undefined":_typeof(global))&&global;freeGlobal.global!==freeGlobal&&freeGlobal.window!==freeGlobal||(root=freeGlobal);var byteArray,byteCount,byteIndex,stringFromCharCode=String.fromCharCode,utf8={version:"2.0.0",encode:utf8encode,decode:utf8decode};if("function"==typeof define&&"object"==_typeof(define.amd)&&define.amd)define(function(){return utf8});else if(freeExports&&!freeExports.nodeType)if(freeModule)freeModule.exports=utf8;else{var object={},hasOwnProperty=object.hasOwnProperty;for(var key in utf8)hasOwnProperty.call(utf8,key)&&(freeExports[key]=utf8[key])}else root.utf8=utf8}(this)}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})},{}],30:[function(_dereq_,module,exports){function encode(num){var encoded="";do encoded=alphabet[num%length]+encoded,num=Math.floor(num/length);while(num>0);return encoded}function decode(str){var decoded=0;for(i=0;i<str.length;i++)decoded=decoded*length+map[str.charAt(i)];return decoded}function yeast(){var now=encode(+new Date);return now!==prev?(seed=0,prev=now):now+"."+encode(seed++)}for(var prev,alphabet="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_".split(""),length=64,map={},seed=0,i=0;i<length;i++)map[alphabet[i]]=i;yeast.encode=encode,yeast.decode=decode,module.exports=yeast},{}],31:[function(_dereq_,module,exports){function lookup(uri,opts){"object"==("undefined"==typeof uri?"undefined":_typeof(uri))&&(opts=uri,uri=void 0),opts=opts||{};var io,parsed=url(uri),source=parsed.source,id=parsed.id,path=parsed.path,sameNamespace=cache[id]&&path in cache[id].nsps,newConnection=opts.forceNew||opts["force new connection"]||!1===opts.multiplex||sameNamespace;return newConnection?(debug("ignoring socket cache for %s",source),io=Manager(source,opts)):(cache[id]||(debug("new io instance for %s",source),cache[id]=Manager(source,opts)),io=cache[id]),io.socket(parsed.path)}var url=_dereq_("./url"),parser=_dereq_("socket.io-parser"),Manager=_dereq_("./manager"),debug=_dereq_("debug")("socket.io-client");module.exports=exports=lookup;var cache=exports.managers={};exports.protocol=parser.protocol,exports.connect=lookup,exports.Manager=_dereq_("./manager"),exports.Socket=_dereq_("./socket")},{"./manager":32,"./socket":34,"./url":35,debug:39,"socket.io-parser":47}],32:[function(_dereq_,module,exports){function Manager(uri,opts){return this instanceof Manager?(uri&&"object"==("undefined"==typeof uri?"undefined":_typeof(uri))&&(opts=uri,uri=void 0),opts=opts||{},opts.path=opts.path||"/socket.io",this.nsps={},this.subs=[],this.opts=opts,this.reconnection(opts.reconnection!==!1),this.reconnectionAttempts(opts.reconnectionAttempts||1/0),this.reconnectionDelay(opts.reconnectionDelay||1e3),this.reconnectionDelayMax(opts.reconnectionDelayMax||5e3),this.randomizationFactor(opts.randomizationFactor||.5),this.backoff=new Backoff({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==opts.timeout?2e4:opts.timeout),this.readyState="closed",this.uri=uri,this.connecting=[],this.lastPing=null,this.encoding=!1,this.packetBuffer=[],this.encoder=new parser.Encoder,this.decoder=new parser.Decoder,this.autoConnect=opts.autoConnect!==!1,void(this.autoConnect&&this.open())):new Manager(uri,opts)}var eio=_dereq_("engine.io-client"),Socket=_dereq_("./socket"),Emitter=_dereq_("component-emitter"),parser=_dereq_("socket.io-parser"),on=_dereq_("./on"),bind=_dereq_("component-bind"),debug=_dereq_("debug")("socket.io-client:manager"),indexOf=_dereq_("indexof"),Backoff=_dereq_("backo2"),has=Object.prototype.hasOwnProperty;module.exports=Manager,Manager.prototype.emitAll=function(){this.emit.apply(this,arguments);for(var nsp in this.nsps)has.call(this.nsps,nsp)&&this.nsps[nsp].emit.apply(this.nsps[nsp],arguments)},Manager.prototype.updateSocketIds=function(){for(var nsp in this.nsps)has.call(this.nsps,nsp)&&(this.nsps[nsp].id=this.engine.id)},Emitter(Manager.prototype),Manager.prototype.reconnection=function(v){return arguments.length?(this._reconnection=!!v,this):this._reconnection},Manager.prototype.reconnectionAttempts=function(v){return arguments.length?(this._reconnectionAttempts=v,this):this._reconnectionAttempts},Manager.prototype.reconnectionDelay=function(v){return arguments.length?(this._reconnectionDelay=v,this.backoff&&this.backoff.setMin(v),this):this._reconnectionDelay},Manager.prototype.randomizationFactor=function(v){return arguments.length?(this._randomizationFactor=v,this.backoff&&this.backoff.setJitter(v),this):this._randomizationFactor},Manager.prototype.reconnectionDelayMax=function(v){return arguments.length?(this._reconnectionDelayMax=v,this.backoff&&this.backoff.setMax(v),this):this._reconnectionDelayMax},Manager.prototype.timeout=function(v){return arguments.length?(this._timeout=v,this):this._timeout},Manager.prototype.maybeReconnectOnOpen=function(){!this.reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()},Manager.prototype.open=Manager.prototype.connect=function(fn){if(debug("readyState %s",this.readyState),~this.readyState.indexOf("open"))return this;debug("opening %s",this.uri),this.engine=eio(this.uri,this.opts);var socket=this.engine,self=this;this.readyState="opening",this.skipReconnect=!1;var openSub=on(socket,"open",function(){self.onopen(),fn&&fn()}),errorSub=on(socket,"error",function(data){if(debug("connect_error"),self.cleanup(),self.readyState="closed",self.emitAll("connect_error",data),fn){var err=new Error("Connection error");err.data=data,fn(err)}else self.maybeReconnectOnOpen()});if(!1!==this._timeout){var timeout=this._timeout;debug("connect attempt will timeout after %d",timeout);var timer=setTimeout(function(){debug("connect attempt timed out after %d",timeout),openSub.destroy(),socket.close(),socket.emit("error","timeout"),self.emitAll("connect_timeout",timeout)},timeout);this.subs.push({destroy:function(){clearTimeout(timer)}})}return this.subs.push(openSub),this.subs.push(errorSub),this},Manager.prototype.onopen=function(){debug("open"),this.cleanup(),this.readyState="open",this.emit("open");var socket=this.engine;this.subs.push(on(socket,"data",bind(this,"ondata"))),this.subs.push(on(socket,"ping",bind(this,"onping"))),this.subs.push(on(socket,"pong",bind(this,"onpong"))),this.subs.push(on(socket,"error",bind(this,"onerror"))),this.subs.push(on(socket,"close",bind(this,"onclose"))),this.subs.push(on(this.decoder,"decoded",bind(this,"ondecoded")))},Manager.prototype.onping=function(){this.lastPing=new Date,this.emitAll("ping")},Manager.prototype.onpong=function(){this.emitAll("pong",new Date-this.lastPing)},Manager.prototype.ondata=function(data){this.decoder.add(data)},Manager.prototype.ondecoded=function(packet){this.emit("packet",packet)},Manager.prototype.onerror=function(err){debug("error",err),this.emitAll("error",err)},Manager.prototype.socket=function(nsp){function onConnecting(){~indexOf(self.connecting,socket)||self.connecting.push(socket)}var socket=this.nsps[nsp];if(!socket){socket=new Socket(this,nsp),this.nsps[nsp]=socket;var self=this;socket.on("connecting",onConnecting),socket.on("connect",function(){socket.id=self.engine.id}),this.autoConnect&&onConnecting()}return socket},Manager.prototype.destroy=function(socket){var index=indexOf(this.connecting,socket);~index&&this.connecting.splice(index,1),this.connecting.length||this.close()},Manager.prototype.packet=function(packet){debug("writing packet %j",packet);var self=this;self.encoding?self.packetBuffer.push(packet):(self.encoding=!0,this.encoder.encode(packet,function(encodedPackets){for(var i=0;i<encodedPackets.length;i++)self.engine.write(encodedPackets[i],packet.options);self.encoding=!1,self.processPacketQueue()}))},Manager.prototype.processPacketQueue=function(){if(this.packetBuffer.length>0&&!this.encoding){var pack=this.packetBuffer.shift();this.packet(pack)}},Manager.prototype.cleanup=function(){debug("cleanup");for(var sub;sub=this.subs.shift();)sub.destroy();this.packetBuffer=[],this.encoding=!1,this.lastPing=null,this.decoder.destroy()},Manager.prototype.close=Manager.prototype.disconnect=function(){debug("disconnect"),this.skipReconnect=!0,this.reconnecting=!1,"opening"==this.readyState&&this.cleanup(),this.backoff.reset(),this.readyState="closed",this.engine&&this.engine.close()},Manager.prototype.onclose=function(reason){debug("onclose"),this.cleanup(),this.backoff.reset(),this.readyState="closed",this.emit("close",reason),this._reconnection&&!this.skipReconnect&&this.reconnect()},Manager.prototype.reconnect=function(){if(this.reconnecting||this.skipReconnect)return this;var self=this;if(this.backoff.attempts>=this._reconnectionAttempts)debug("reconnect failed"),this.backoff.reset(),this.emitAll("reconnect_failed"),this.reconnecting=!1;else{var delay=this.backoff.duration();debug("will wait %dms before reconnect attempt",delay),this.reconnecting=!0;var timer=setTimeout(function(){self.skipReconnect||(debug("attempting reconnect"),self.emitAll("reconnect_attempt",self.backoff.attempts),self.emitAll("reconnecting",self.backoff.attempts),self.skipReconnect||self.open(function(err){err?(debug("reconnect attempt error"),self.reconnecting=!1,self.reconnect(),self.emitAll("reconnect_error",err.data)):(debug("reconnect success"),self.onreconnect())}))},delay);this.subs.push({destroy:function(){clearTimeout(timer)}})}},Manager.prototype.onreconnect=function(){var attempt=this.backoff.attempts;this.reconnecting=!1,this.backoff.reset(),this.updateSocketIds(),this.emitAll("reconnect",attempt)}},{"./on":33,"./socket":34,backo2:36,"component-bind":37,"component-emitter":38,debug:39,"engine.io-client":1,indexof:42,"socket.io-parser":47}],33:[function(_dereq_,module,exports){function on(obj,ev,fn){return obj.on(ev,fn),{destroy:function(){obj.removeListener(ev,fn)}}}module.exports=on},{}],34:[function(_dereq_,module,exports){function Socket(io,nsp){this.io=io,this.nsp=nsp,this.json=this,this.ids=0,this.acks={},this.receiveBuffer=[],this.sendBuffer=[],this.connected=!1,this.disconnected=!0,this.io.autoConnect&&this.open()}var parser=_dereq_("socket.io-parser"),Emitter=_dereq_("component-emitter"),toArray=_dereq_("to-array"),on=_dereq_("./on"),bind=_dereq_("component-bind"),debug=_dereq_("debug")("socket.io-client:socket"),hasBin=_dereq_("has-binary");module.exports=exports=Socket;var events={connect:1,connect_error:1,connect_timeout:1,connecting:1,disconnect:1,error:1,reconnect:1,reconnect_attempt:1,reconnect_failed:1,reconnect_error:1,reconnecting:1,ping:1,pong:1},emit=Emitter.prototype.emit;Emitter(Socket.prototype),Socket.prototype.subEvents=function(){if(!this.subs){var io=this.io;this.subs=[on(io,"open",bind(this,"onopen")),on(io,"packet",bind(this,"onpacket")),on(io,"close",bind(this,"onclose"))]}},Socket.prototype.open=Socket.prototype.connect=function(){return this.connected?this:(this.subEvents(),this.io.open(),"open"==this.io.readyState&&this.onopen(),this.emit("connecting"),this)},Socket.prototype.send=function(){var args=toArray(arguments);return args.unshift("message"),this.emit.apply(this,args),this},Socket.prototype.emit=function(ev){if(events.hasOwnProperty(ev))return emit.apply(this,arguments),this;var args=toArray(arguments),parserType=parser.EVENT;hasBin(args)&&(parserType=parser.BINARY_EVENT);var packet={type:parserType,data:args};return packet.options={},packet.options.compress=!this.flags||!1!==this.flags.compress,"function"==typeof args[args.length-1]&&(debug("emitting packet with ack id %d",this.ids),this.acks[this.ids]=args.pop(),packet.id=this.ids++),this.connected?this.packet(packet):this.sendBuffer.push(packet),delete this.flags,this},Socket.prototype.packet=function(packet){packet.nsp=this.nsp,this.io.packet(packet)},Socket.prototype.onopen=function(){debug("transport is open - connecting"),"/"!=this.nsp&&this.packet({type:parser.CONNECT})},Socket.prototype.onclose=function(reason){debug("close (%s)",reason),this.connected=!1,this.disconnected=!0,delete this.id,this.emit("disconnect",reason)},Socket.prototype.onpacket=function(packet){if(packet.nsp==this.nsp)switch(packet.type){case parser.CONNECT:this.onconnect();break;case parser.EVENT:this.onevent(packet);break;case parser.BINARY_EVENT:this.onevent(packet);break;case parser.ACK:this.onack(packet);break;case parser.BINARY_ACK:this.onack(packet);break;case parser.DISCONNECT:this.ondisconnect();break;case parser.ERROR:this.emit("error",packet.data)}},Socket.prototype.onevent=function(packet){var args=packet.data||[];debug("emitting event %j",args),null!=packet.id&&(debug("attaching ack callback to event"),args.push(this.ack(packet.id))),this.connected?emit.apply(this,args):this.receiveBuffer.push(args)},Socket.prototype.ack=function(id){var self=this,sent=!1;return function(){if(!sent){sent=!0;var args=toArray(arguments);debug("sending ack %j",args);var type=hasBin(args)?parser.BINARY_ACK:parser.ACK;self.packet({type:type,id:id,data:args})}}},Socket.prototype.onack=function(packet){var ack=this.acks[packet.id];"function"==typeof ack?(debug("calling ack %s with %j",packet.id,packet.data),ack.apply(this,packet.data),delete this.acks[packet.id]):debug("bad ack %s",packet.id)},Socket.prototype.onconnect=function(){this.connected=!0,this.disconnected=!1,this.emit("connect"),this.emitBuffered()},Socket.prototype.emitBuffered=function(){var i;for(i=0;i<this.receiveBuffer.length;i++)emit.apply(this,this.receiveBuffer[i]);for(this.receiveBuffer=[],i=0;i<this.sendBuffer.length;i++)this.packet(this.sendBuffer[i]);this.sendBuffer=[]},Socket.prototype.ondisconnect=function(){debug("server disconnect (%s)",this.nsp),this.destroy(),this.onclose("io server disconnect")},Socket.prototype.destroy=function(){if(this.subs){for(var i=0;i<this.subs.length;i++)this.subs[i].destroy();this.subs=null}this.io.destroy(this)},Socket.prototype.close=Socket.prototype.disconnect=function(){return this.connected&&(debug("performing disconnect (%s)",this.nsp),this.packet({type:parser.DISCONNECT})),this.destroy(),this.connected&&this.onclose("io client disconnect"),this},Socket.prototype.compress=function(compress){return this.flags=this.flags||{},this.flags.compress=compress,this}},{"./on":33,"component-bind":37,"component-emitter":38,debug:39,"has-binary":41,"socket.io-parser":47,"to-array":51}],35:[function(_dereq_,module,exports){(function(global){function url(uri,loc){var obj=uri,loc=loc||global.location;null==uri&&(uri=loc.protocol+"//"+loc.host),"string"==typeof uri&&("/"==uri.charAt(0)&&(uri="/"==uri.charAt(1)?loc.protocol+uri:loc.host+uri),/^(https?|wss?):\/\//.test(uri)||(debug("protocol-less url %s",uri),uri="undefined"!=typeof loc?loc.protocol+"//"+uri:"https://"+uri),debug("parse %s",uri),obj=parseuri(uri)),obj.port||(/^(http|ws)$/.test(obj.protocol)?obj.port="80":/^(http|ws)s$/.test(obj.protocol)&&(obj.port="443")),obj.path=obj.path||"/";var ipv6=obj.host.indexOf(":")!==-1,host=ipv6?"["+obj.host+"]":obj.host;return obj.id=obj.protocol+"://"+host+":"+obj.port,obj.href=obj.protocol+"://"+host+(loc&&loc.port==obj.port?"":":"+obj.port),obj}var parseuri=_dereq_("parseuri"),debug=_dereq_("debug")("socket.io-client:url");module.exports=url}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})},{debug:39,parseuri:45}],36:[function(_dereq_,module,exports){function Backoff(opts){opts=opts||{},this.ms=opts.min||100,this.max=opts.max||1e4,this.factor=opts.factor||2,this.jitter=opts.jitter>0&&opts.jitter<=1?opts.jitter:0,this.attempts=0}module.exports=Backoff,Backoff.prototype.duration=function(){var ms=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var rand=Math.random(),deviation=Math.floor(rand*this.jitter*ms);ms=0==(1&Math.floor(10*rand))?ms-deviation:ms+deviation}return 0|Math.min(ms,this.max)},Backoff.prototype.reset=function(){this.attempts=0},Backoff.prototype.setMin=function(min){this.ms=min},Backoff.prototype.setMax=function(max){this.max=max},Backoff.prototype.setJitter=function(jitter){this.jitter=jitter}},{}],37:[function(_dereq_,module,exports){var slice=[].slice;module.exports=function(obj,fn){if("string"==typeof fn&&(fn=obj[fn]),"function"!=typeof fn)throw new Error("bind() requires a function");var args=slice.call(arguments,2);return function(){return fn.apply(obj,args.concat(slice.call(arguments)))}}},{}],38:[function(_dereq_,module,exports){function Emitter(obj){if(obj)return mixin(obj)}function mixin(obj){for(var key in Emitter.prototype)obj[key]=Emitter.prototype[key];return obj}module.exports=Emitter,Emitter.prototype.on=Emitter.prototype.addEventListener=function(event,fn){return this._callbacks=this._callbacks||{},(this._callbacks["$"+event]=this._callbacks["$"+event]||[]).push(fn),this},Emitter.prototype.once=function(event,fn){function on(){this.off(event,on),fn.apply(this,arguments)}return on.fn=fn,this.on(event,on),this},Emitter.prototype.off=Emitter.prototype.removeListener=Emitter.prototype.removeAllListeners=Emitter.prototype.removeEventListener=function(event,fn){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var callbacks=this._callbacks["$"+event];if(!callbacks)return this;if(1==arguments.length)return delete this._callbacks["$"+event],this;for(var cb,i=0;i<callbacks.length;i++)if(cb=callbacks[i],cb===fn||cb.fn===fn){callbacks.splice(i,1);break}return this},Emitter.prototype.emit=function(event){this._callbacks=this._callbacks||{};var args=[].slice.call(arguments,1),callbacks=this._callbacks["$"+event];if(callbacks){callbacks=callbacks.slice(0);for(var i=0,len=callbacks.length;i<len;++i)callbacks[i].apply(this,args)}return this},Emitter.prototype.listeners=function(event){return this._callbacks=this._callbacks||{},this._callbacks["$"+event]||[]},Emitter.prototype.hasListeners=function(event){return!!this.listeners(event).length}},{}],39:[function(_dereq_,module,exports){arguments[4][17][0].apply(exports,arguments)},{"./debug":40,dup:17}],40:[function(_dereq_,module,exports){arguments[4][18][0].apply(exports,arguments)},{dup:18,ms:44}],41:[function(_dereq_,module,exports){(function(global){function hasBinary(data){function _hasBinary(obj){if(!obj)return!1;if(global.Buffer&&global.Buffer.isBuffer&&global.Buffer.isBuffer(obj)||global.ArrayBuffer&&obj instanceof ArrayBuffer||global.Blob&&obj instanceof Blob||global.File&&obj instanceof File)return!0;if(isArray(obj)){for(var i=0;i<obj.length;i++)if(_hasBinary(obj[i]))return!0}else if(obj&&"object"==("undefined"==typeof obj?"undefined":_typeof(obj))){obj.toJSON&&"function"==typeof obj.toJSON&&(obj=obj.toJSON());for(var key in obj)if(Object.prototype.hasOwnProperty.call(obj,key)&&_hasBinary(obj[key]))return!0}return!1}return _hasBinary(data)}var isArray=_dereq_("isarray");module.exports=hasBinary}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})},{isarray:43}],42:[function(_dereq_,module,exports){arguments[4][23][0].apply(exports,arguments)},{dup:23}],43:[function(_dereq_,module,exports){arguments[4][24][0].apply(exports,arguments)},{dup:24}],44:[function(_dereq_,module,exports){arguments[4][25][0].apply(exports,arguments)},{dup:25}],45:[function(_dereq_,module,exports){arguments[4][28][0].apply(exports,arguments)},{dup:28}],46:[function(_dereq_,module,exports){(function(global){var isArray=_dereq_("isarray"),isBuf=_dereq_("./is-buffer");exports.deconstructPacket=function(packet){function _deconstructPacket(data){if(!data)return data;if(isBuf(data)){var placeholder={_placeholder:!0,num:buffers.length};return buffers.push(data),placeholder}if(isArray(data)){for(var newData=new Array(data.length),i=0;i<data.length;i++)newData[i]=_deconstructPacket(data[i]);return newData}if("object"==("undefined"==typeof data?"undefined":_typeof(data))&&!(data instanceof Date)){var newData={};for(var key in data)newData[key]=_deconstructPacket(data[key]);return newData}return data}var buffers=[],packetData=packet.data,pack=packet;return pack.data=_deconstructPacket(packetData),pack.attachments=buffers.length,{packet:pack,buffers:buffers}},exports.reconstructPacket=function(packet,buffers){function _reconstructPacket(data){if(data&&data._placeholder){var buf=buffers[data.num];return buf}if(isArray(data)){for(var i=0;i<data.length;i++)data[i]=_reconstructPacket(data[i]);return data}if(data&&"object"==("undefined"==typeof data?"undefined":_typeof(data))){for(var key in data)data[key]=_reconstructPacket(data[key]);return data}return data}return packet.data=_reconstructPacket(packet.data),packet.attachments=void 0,packet},exports.removeBlobs=function(data,callback){function _removeBlobs(obj,curKey,containingObject){if(!obj)return obj;if(global.Blob&&obj instanceof Blob||global.File&&obj instanceof File){pendingBlobs++;var fileReader=new FileReader;fileReader.onload=function(){containingObject?containingObject[curKey]=this.result:bloblessData=this.result,--pendingBlobs||callback(bloblessData)},fileReader.readAsArrayBuffer(obj)}else if(isArray(obj))for(var i=0;i<obj.length;i++)_removeBlobs(obj[i],i,obj);else if(obj&&"object"==("undefined"==typeof obj?"undefined":_typeof(obj))&&!isBuf(obj))for(var key in obj)_removeBlobs(obj[key],key,obj)}var pendingBlobs=0,bloblessData=data;_removeBlobs(bloblessData),pendingBlobs||callback(bloblessData)}}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})},{"./is-buffer":48,isarray:43}],47:[function(_dereq_,module,exports){function Encoder(){}function encodeAsString(obj){var str="",nsp=!1;return str+=obj.type,exports.BINARY_EVENT!=obj.type&&exports.BINARY_ACK!=obj.type||(str+=obj.attachments,str+="-"),obj.nsp&&"/"!=obj.nsp&&(nsp=!0,str+=obj.nsp),null!=obj.id&&(nsp&&(str+=",",nsp=!1),str+=obj.id),null!=obj.data&&(nsp&&(str+=","),str+=json.stringify(obj.data)),debug("encoded %j as %s",obj,str),str}function encodeAsBinary(obj,callback){function writeEncoding(bloblessData){var deconstruction=binary.deconstructPacket(bloblessData),pack=encodeAsString(deconstruction.packet),buffers=deconstruction.buffers;buffers.unshift(pack),callback(buffers)}binary.removeBlobs(obj,writeEncoding)}function Decoder(){this.reconstructor=null}function decodeString(str){var p={},i=0;if(p.type=Number(str.charAt(0)),null==exports.types[p.type])return error();if(exports.BINARY_EVENT==p.type||exports.BINARY_ACK==p.type){for(var buf="";"-"!=str.charAt(++i)&&(buf+=str.charAt(i),i!=str.length););if(buf!=Number(buf)||"-"!=str.charAt(i))throw new Error("Illegal attachments");p.attachments=Number(buf)}if("/"==str.charAt(i+1))for(p.nsp="";++i;){var c=str.charAt(i);if(","==c)break;if(p.nsp+=c,i==str.length)break}else p.nsp="/";var next=str.charAt(i+1);if(""!==next&&Number(next)==next){for(p.id="";++i;){var c=str.charAt(i);if(null==c||Number(c)!=c){--i;break}if(p.id+=str.charAt(i),i==str.length)break}p.id=Number(p.id)}if(str.charAt(++i))try{p.data=json.parse(str.substr(i))}catch(e){return error()}return debug("decoded %s as %j",str,p),p}function BinaryReconstructor(packet){this.reconPack=packet,this.buffers=[]}function error(data){return{type:exports.ERROR,data:"parser error"}}var debug=_dereq_("debug")("socket.io-parser"),json=_dereq_("json3"),Emitter=(_dereq_("isarray"),_dereq_("component-emitter")),binary=_dereq_("./binary"),isBuf=_dereq_("./is-buffer");exports.protocol=4,exports.types=["CONNECT","DISCONNECT","EVENT","BINARY_EVENT","ACK","BINARY_ACK","ERROR"],exports.CONNECT=0,exports.DISCONNECT=1,exports.EVENT=2,exports.ACK=3,exports.ERROR=4,exports.BINARY_EVENT=5,exports.BINARY_ACK=6,exports.Encoder=Encoder,exports.Decoder=Decoder,Encoder.prototype.encode=function(obj,callback){if(debug("encoding packet %j",obj),exports.BINARY_EVENT==obj.type||exports.BINARY_ACK==obj.type)encodeAsBinary(obj,callback);else{var encoding=encodeAsString(obj);callback([encoding])}},Emitter(Decoder.prototype),Decoder.prototype.add=function(obj){var packet;if("string"==typeof obj)packet=decodeString(obj),exports.BINARY_EVENT==packet.type||exports.BINARY_ACK==packet.type?(this.reconstructor=new BinaryReconstructor(packet),0===this.reconstructor.reconPack.attachments&&this.emit("decoded",packet)):this.emit("decoded",packet);else{if(!isBuf(obj)&&!obj.base64)throw new Error("Unknown type: "+obj);if(!this.reconstructor)throw new Error("got binary data when not reconstructing a packet");packet=this.reconstructor.takeBinaryData(obj),packet&&(this.reconstructor=null,this.emit("decoded",packet))}},Decoder.prototype.destroy=function(){this.reconstructor&&this.reconstructor.finishedReconstruction()},BinaryReconstructor.prototype.takeBinaryData=function(binData){if(this.buffers.push(binData),this.buffers.length==this.reconPack.attachments){var packet=binary.reconstructPacket(this.reconPack,this.buffers);return this.finishedReconstruction(),packet}return null},BinaryReconstructor.prototype.finishedReconstruction=function(){this.reconPack=null,this.buffers=[]}},{"./binary":46,"./is-buffer":48,"component-emitter":49,debug:39,isarray:43,json3:50}],48:[function(_dereq_,module,exports){(function(global){function isBuf(obj){return global.Buffer&&global.Buffer.isBuffer(obj)||global.ArrayBuffer&&obj instanceof ArrayBuffer}module.exports=isBuf}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})},{}],49:[function(_dereq_,module,exports){arguments[4][15][0].apply(exports,arguments)},{dup:15}],50:[function(_dereq_,module,exports){(function(global){(function(){function runInContext(context,exports){function has(name){if(has[name]!==undef)return has[name];var isSupported;if("bug-string-char-index"==name)isSupported="a"!="a"[0];else if("json"==name)isSupported=has("json-stringify")&&has("json-parse");else{var value,serialized='{"a":[1,true,false,null,"\\u0000\\b\\n\\f\\r\\t"]}';if("json-stringify"==name){var stringify=exports.stringify,stringifySupported="function"==typeof stringify&&isExtended;if(stringifySupported){(value=function(){return 1}).toJSON=value;try{stringifySupported="0"===stringify(0)&&"0"===stringify(new Number)&&'""'==stringify(new String)&&stringify(getClass)===undef&&stringify(undef)===undef&&stringify()===undef&&"1"===stringify(value)&&"[1]"==stringify([value])&&"[null]"==stringify([undef])&&"null"==stringify(null)&&"[null,null,null]"==stringify([undef,getClass,null])&&stringify({a:[value,!0,!1,null,"\0\b\n\f\r\t"]})==serialized&&"1"===stringify(null,value)&&"[\n 1,\n 2\n]"==stringify([1,2],null,1)&&'"-271821-04-20T00:00:00.000Z"'==stringify(new Date(-864e13))&&'"+275760-09-13T00:00:00.000Z"'==stringify(new Date(864e13))&&'"-000001-01-01T00:00:00.000Z"'==stringify(new Date(-621987552e5))&&'"1969-12-31T23:59:59.999Z"'==stringify(new Date(-1))}catch(exception){stringifySupported=!1}}isSupported=stringifySupported}if("json-parse"==name){var parse=exports.parse;if("function"==typeof parse)try{if(0===parse("0")&&!parse(!1)){value=parse(serialized);var parseSupported=5==value.a.length&&1===value.a[0];if(parseSupported){try{parseSupported=!parse('"\t"')}catch(exception){}if(parseSupported)try{parseSupported=1!==parse("01")}catch(exception){}if(parseSupported)try{parseSupported=1!==parse("1.")}catch(exception){}}}}catch(exception){parseSupported=!1}isSupported=parseSupported}}return has[name]=!!isSupported}context||(context=root.Object()),exports||(exports=root.Object());var Number=context.Number||root.Number,String=context.String||root.String,Object=context.Object||root.Object,Date=context.Date||root.Date,SyntaxError=context.SyntaxError||root.SyntaxError,TypeError=context.TypeError||root.TypeError,Math=context.Math||root.Math,nativeJSON=context.JSON||root.JSON;"object"==("undefined"==typeof nativeJSON?"undefined":_typeof(nativeJSON))&&nativeJSON&&(exports.stringify=nativeJSON.stringify,exports.parse=nativeJSON.parse);var _isProperty,_forEach,undef,objectProto=Object.prototype,getClass=objectProto.toString,isExtended=new Date(-0xc782b5b800cec);try{isExtended=isExtended.getUTCFullYear()==-109252&&0===isExtended.getUTCMonth()&&1===isExtended.getUTCDate()&&10==isExtended.getUTCHours()&&37==isExtended.getUTCMinutes()&&6==isExtended.getUTCSeconds()&&708==isExtended.getUTCMilliseconds()}catch(exception){}if(!has("json")){var functionClass="[object Function]",dateClass="[object Date]",numberClass="[object Number]",stringClass="[object String]",arrayClass="[object Array]",booleanClass="[object Boolean]",charIndexBuggy=has("bug-string-char-index");if(!isExtended)var floor=Math.floor,Months=[0,31,59,90,120,151,181,212,243,273,304,334],getDay=function(year,month){return Months[month]+365*(year-1970)+floor((year-1969+(month=+(month>1)))/4)-floor((year-1901+month)/100)+floor((year-1601+month)/400)};if((_isProperty=objectProto.hasOwnProperty)||(_isProperty=function(property){var constructor,members={};return(members.__proto__=null,members.__proto__={toString:1},members).toString!=getClass?_isProperty=function(property){var original=this.__proto__,result=property in(this.__proto__=null,this);return this.__proto__=original,result}:(constructor=members.constructor,_isProperty=function(property){var parent=(this.constructor||constructor).prototype;return property in this&&!(property in parent&&this[property]===parent[property])}),members=null,_isProperty.call(this,property)}),_forEach=function(object,callback){var Properties,members,property,size=0;(Properties=function(){this.valueOf=0}).prototype.valueOf=0,members=new Properties;for(property in members)_isProperty.call(members,property)&&size++;return Properties=members=null,size?_forEach=2==size?function(object,callback){var property,members={},isFunction=getClass.call(object)==functionClass;for(property in object)isFunction&&"prototype"==property||_isProperty.call(members,property)||!(members[property]=1)||!_isProperty.call(object,property)||callback(property)}:function(object,callback){var property,isConstructor,isFunction=getClass.call(object)==functionClass;for(property in object)isFunction&&"prototype"==property||!_isProperty.call(object,property)||(isConstructor="constructor"===property)||callback(property);(isConstructor||_isProperty.call(object,property="constructor"))&&callback(property)}:(members=["valueOf","toString","toLocaleString","propertyIsEnumerable","isPrototypeOf","hasOwnProperty","constructor"],_forEach=function(object,callback){var property,length,isFunction=getClass.call(object)==functionClass,hasProperty=!isFunction&&"function"!=typeof object.constructor&&objectTypes[_typeof(object.hasOwnProperty)]&&object.hasOwnProperty||_isProperty;
for(property in object)isFunction&&"prototype"==property||!hasProperty.call(object,property)||callback(property);for(length=members.length;property=members[--length];hasProperty.call(object,property)&&callback(property));}),_forEach(object,callback)},!has("json-stringify")){var Escapes={92:"\\\\",34:'\\"',8:"\\b",12:"\\f",10:"\\n",13:"\\r",9:"\\t"},leadingZeroes="000000",toPaddedString=function(width,value){return(leadingZeroes+(value||0)).slice(-width)},unicodePrefix="\\u00",quote=function(value){for(var result='"',index=0,length=value.length,useCharIndex=!charIndexBuggy||length>10,symbols=useCharIndex&&(charIndexBuggy?value.split(""):value);index<length;index++){var charCode=value.charCodeAt(index);switch(charCode){case 8:case 9:case 10:case 12:case 13:case 34:case 92:result+=Escapes[charCode];break;default:if(charCode<32){result+=unicodePrefix+toPaddedString(2,charCode.toString(16));break}result+=useCharIndex?symbols[index]:value.charAt(index)}}return result+'"'},serialize=function serialize(property,object,callback,properties,whitespace,indentation,stack){var value,className,year,month,date,time,hours,minutes,seconds,milliseconds,results,element,index,length,prefix,result;try{value=object[property]}catch(exception){}if("object"==("undefined"==typeof value?"undefined":_typeof(value))&&value)if(className=getClass.call(value),className!=dateClass||_isProperty.call(value,"toJSON"))"function"==typeof value.toJSON&&(className!=numberClass&&className!=stringClass&&className!=arrayClass||_isProperty.call(value,"toJSON"))&&(value=value.toJSON(property));else if(value>-1/0&&value<1/0){if(getDay){for(date=floor(value/864e5),year=floor(date/365.2425)+1970-1;getDay(year+1,0)<=date;year++);for(month=floor((date-getDay(year,0))/30.42);getDay(year,month+1)<=date;month++);date=1+date-getDay(year,month),time=(value%864e5+864e5)%864e5,hours=floor(time/36e5)%24,minutes=floor(time/6e4)%60,seconds=floor(time/1e3)%60,milliseconds=time%1e3}else year=value.getUTCFullYear(),month=value.getUTCMonth(),date=value.getUTCDate(),hours=value.getUTCHours(),minutes=value.getUTCMinutes(),seconds=value.getUTCSeconds(),milliseconds=value.getUTCMilliseconds();value=(year<=0||year>=1e4?(year<0?"-":"+")+toPaddedString(6,year<0?-year:year):toPaddedString(4,year))+"-"+toPaddedString(2,month+1)+"-"+toPaddedString(2,date)+"T"+toPaddedString(2,hours)+":"+toPaddedString(2,minutes)+":"+toPaddedString(2,seconds)+"."+toPaddedString(3,milliseconds)+"Z"}else value=null;if(callback&&(value=callback.call(object,property,value)),null===value)return"null";if(className=getClass.call(value),className==booleanClass)return""+value;if(className==numberClass)return value>-1/0&&value<1/0?""+value:"null";if(className==stringClass)return quote(""+value);if("object"==("undefined"==typeof value?"undefined":_typeof(value))){for(length=stack.length;length--;)if(stack[length]===value)throw TypeError();if(stack.push(value),results=[],prefix=indentation,indentation+=whitespace,className==arrayClass){for(index=0,length=value.length;index<length;index++)element=serialize(index,value,callback,properties,whitespace,indentation,stack),results.push(element===undef?"null":element);result=results.length?whitespace?"[\n"+indentation+results.join(",\n"+indentation)+"\n"+prefix+"]":"["+results.join(",")+"]":"[]"}else _forEach(properties||value,function(property){var element=serialize(property,value,callback,properties,whitespace,indentation,stack);element!==undef&&results.push(quote(property)+":"+(whitespace?" ":"")+element)}),result=results.length?whitespace?"{\n"+indentation+results.join(",\n"+indentation)+"\n"+prefix+"}":"{"+results.join(",")+"}":"{}";return stack.pop(),result}};exports.stringify=function(source,filter,width){var whitespace,callback,properties,className;if(objectTypes["undefined"==typeof filter?"undefined":_typeof(filter)]&&filter)if((className=getClass.call(filter))==functionClass)callback=filter;else if(className==arrayClass){properties={};for(var value,index=0,length=filter.length;index<length;value=filter[index++],className=getClass.call(value),(className==stringClass||className==numberClass)&&(properties[value]=1));}if(width)if((className=getClass.call(width))==numberClass){if((width-=width%1)>0)for(whitespace="",width>10&&(width=10);whitespace.length<width;whitespace+=" ");}else className==stringClass&&(whitespace=width.length<=10?width:width.slice(0,10));return serialize("",(value={},value[""]=source,value),callback,properties,whitespace,"",[])}}if(!has("json-parse")){var Index,Source,fromCharCode=String.fromCharCode,Unescapes={92:"\\",34:'"',47:"/",98:"\b",116:"\t",110:"\n",102:"\f",114:"\r"},abort=function(){throw Index=Source=null,SyntaxError()},lex=function(){for(var value,begin,position,isSigned,charCode,source=Source,length=source.length;Index<length;)switch(charCode=source.charCodeAt(Index)){case 9:case 10:case 13:case 32:Index++;break;case 123:case 125:case 91:case 93:case 58:case 44:return value=charIndexBuggy?source.charAt(Index):source[Index],Index++,value;case 34:for(value="@",Index++;Index<length;)if(charCode=source.charCodeAt(Index),charCode<32)abort();else if(92==charCode)switch(charCode=source.charCodeAt(++Index)){case 92:case 34:case 47:case 98:case 116:case 110:case 102:case 114:value+=Unescapes[charCode],Index++;break;case 117:for(begin=++Index,position=Index+4;Index<position;Index++)charCode=source.charCodeAt(Index),charCode>=48&&charCode<=57||charCode>=97&&charCode<=102||charCode>=65&&charCode<=70||abort();value+=fromCharCode("0x"+source.slice(begin,Index));break;default:abort()}else{if(34==charCode)break;for(charCode=source.charCodeAt(Index),begin=Index;charCode>=32&&92!=charCode&&34!=charCode;)charCode=source.charCodeAt(++Index);value+=source.slice(begin,Index)}if(34==source.charCodeAt(Index))return Index++,value;abort();default:if(begin=Index,45==charCode&&(isSigned=!0,charCode=source.charCodeAt(++Index)),charCode>=48&&charCode<=57){for(48==charCode&&(charCode=source.charCodeAt(Index+1),charCode>=48&&charCode<=57)&&abort(),isSigned=!1;Index<length&&(charCode=source.charCodeAt(Index),charCode>=48&&charCode<=57);Index++)if(46==source.charCodeAt(Index)){for(position=++Index;position<length&&(charCode=source.charCodeAt(position),charCode>=48&&charCode<=57);position++);position==Index&&abort(),Index=position}if(charCode=source.charCodeAt(Index),101==charCode||69==charCode){for(charCode=source.charCodeAt(++Index),43!=charCode&&45!=charCode||Index++,position=Index;position<length&&(charCode=source.charCodeAt(position),charCode>=48&&charCode<=57);position++);position==Index&&abort(),Index=position}return+source.slice(begin,Index)}if(isSigned&&abort(),"true"==source.slice(Index,Index+4))return Index+=4,!0;if("false"==source.slice(Index,Index+5))return Index+=5,!1;if("null"==source.slice(Index,Index+4))return Index+=4,null;abort()}return"$"},get=function get(value){var results,hasMembers;if("$"==value&&abort(),"string"==typeof value){if("@"==(charIndexBuggy?value.charAt(0):value[0]))return value.slice(1);if("["==value){for(results=[];value=lex(),"]"!=value;hasMembers||(hasMembers=!0))hasMembers&&(","==value?(value=lex(),"]"==value&&abort()):abort()),","==value&&abort(),results.push(get(value));return results}if("{"==value){for(results={};value=lex(),"}"!=value;hasMembers||(hasMembers=!0))hasMembers&&(","==value?(value=lex(),"}"==value&&abort()):abort()),","!=value&&"string"==typeof value&&"@"==(charIndexBuggy?value.charAt(0):value[0])&&":"==lex()||abort(),results[value.slice(1)]=get(lex());return results}abort()}return value},update=function(source,property,callback){var element=walk(source,property,callback);element===undef?delete source[property]:source[property]=element},walk=function(source,property,callback){var length,value=source[property];if("object"==("undefined"==typeof value?"undefined":_typeof(value))&&value)if(getClass.call(value)==arrayClass)for(length=value.length;length--;)update(value,length,callback);else _forEach(value,function(property){update(value,property,callback)});return callback.call(source,property,value)};exports.parse=function(source,callback){var result,value;return Index=0,Source=""+source,result=get(lex()),"$"!=lex()&&abort(),Index=Source=null,callback&&getClass.call(callback)==functionClass?walk((value={},value[""]=result,value),"",callback):result}}}return exports.runInContext=runInContext,exports}var isLoader="function"==typeof define&&define.amd,objectTypes={function:!0,object:!0},freeExports=objectTypes["undefined"==typeof exports?"undefined":_typeof(exports)]&&exports&&!exports.nodeType&&exports,root=objectTypes["undefined"==typeof window?"undefined":_typeof(window)]&&window||this,freeGlobal=freeExports&&objectTypes["undefined"==typeof module?"undefined":_typeof(module)]&&module&&!module.nodeType&&"object"==("undefined"==typeof global?"undefined":_typeof(global))&&global;if(!freeGlobal||freeGlobal.global!==freeGlobal&&freeGlobal.window!==freeGlobal&&freeGlobal.self!==freeGlobal||(root=freeGlobal),freeExports&&!isLoader)runInContext(root,freeExports);else{var nativeJSON=root.JSON,previousJSON=root.JSON3,isRestored=!1,JSON3=runInContext(root,root.JSON3={noConflict:function(){return isRestored||(isRestored=!0,root.JSON=nativeJSON,root.JSON3=previousJSON,nativeJSON=previousJSON=null),JSON3}});root.JSON={parse:JSON3.parse,stringify:JSON3.stringify}}isLoader&&define(function(){return JSON3})}).call(this)}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})},{}],51:[function(_dereq_,module,exports){function toArray(list,index){var array=[];index=index||0;for(var i=index||0;i<list.length;i++)array[i-index]=list[i];return array}module.exports=toArray},{}]},{},[31])(31)})}).call(exports,function(){return this}())},function(module,exports,__webpack_require__){"use strict";function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(exports,"__esModule",{value:!0});var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(obj){return typeof obj}:function(obj){return obj&&"function"==typeof Symbol&&obj.constructor===Symbol&&obj!==Symbol.prototype?"symbol":typeof obj},_CB=__webpack_require__(1),_CB2=_interopRequireDefault(_CB),Column=function Column(columnName,dataType,required,unique){if(_classCallCheck(this,Column),this.document={},!columnName||""===columnName)throw"Column Name is required.";if("string"!=typeof columnName)throw"Column Name should be of type string.";columnName&&(_CB2.default._columnNameValidation(columnName),this.document.name=columnName,this.document._type="column"),dataType?(_CB2.default._columnDataTypeValidation(dataType),this.document.dataType=dataType):this.document.dataType="Text","boolean"==typeof required?this.document.required=required:this.document.required=!1,"boolean"==typeof unique?this.document.unique=unique:this.document.unique=!1,"Text"===dataType&&(this.document.isSearchable=!0),this.document.relatedTo=null,this.document.relationType=null,this.document.isDeletable=!0,this.document.isEditable=!0,this.document.isRenamable=!1,this.document.editableByMasterKey=!1,this.document.defaultValue=null};Object.defineProperty(Column.prototype,"name",{get:function(){return this.document.name},set:function(name){this.document.name=name}}),Object.defineProperty(Column.prototype,"dataType",{get:function(){return this.document.dataType},set:function(dataType){this.document.dataType=dataType}}),Object.defineProperty(Column.prototype,"unique",{get:function(){return this.document.unique},set:function(unique){this.document.unique=unique}}),Object.defineProperty(Column.prototype,"relatedTo",{get:function(){return this.document.relatedTo},set:function(relatedTo){this.document.relatedTo=relatedTo}}),Object.defineProperty(Column.prototype,"required",{get:function(){return this.document.required},set:function(required){this.document.required=required}}),Object.defineProperty(Column.prototype,"defaultValue",{get:function(){return this.document.defaultValue},set:function(defaultValue){if("string"==typeof defaultValue){var supportedStringDataTypes=["Text","EncryptedText"];if(supportedStringDataTypes.indexOf(this.document.dataType)>-1)this.document.defaultValue=defaultValue;else if("URL"===this.document.dataType){if(defaultValue.match(/^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i)[0]!==defaultValue)throw new TypeError("Invalid URL");this.document.defaultValue=defaultValue}else if("Email"===this.document.dataType){if(defaultValue.match(/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/i)[0]!==defaultValue)throw new TypeError("Invalid Email");this.document.defaultValue=defaultValue}else{if("DateTime"!==this.document.dataType)throw new TypeError("Unsupported DataType");if("Invalid Date"==new Date(defaultValue))throw new TypeError("Invalid default value for DateTime Field");this.document.defaultValue=defaultValue}}else if(null!==defaultValue&&["number","boolean","object","undefined"].indexOf("undefined"==typeof defaultValue?"undefined":_typeof(defaultValue))>-1){if(this.document.dataType.toUpperCase()!==("undefined"==typeof defaultValue?"undefined":_typeof(defaultValue)).toUpperCase())throw new TypeError("Unsupported DataType");this.document.defaultValue=defaultValue}else{if(null!==defaultValue)throw new TypeError("Unsupported DataType");this.document.defaultValue=defaultValue}}}),Object.defineProperty(Column.prototype,"editableByMasterKey",{get:function(){return this.document.editableByMasterKey},set:function(editableByMasterKey){this.document.editableByMasterKey=editableByMasterKey}}),Object.defineProperty(Column.prototype,"isSearchable",{get:function(){return this.document.isSearchable},set:function(isSearchable){this.document.isSearchable=isSearchable}}),_CB2.default.Column=Column,exports.default=_CB2.default.Column},function(module,exports,__webpack_require__){"use strict";function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(exports,"__esModule",{value:!0});var _createClass=function(){function defineProperties(target,props){for(var i=0;i<props.length;i++){var descriptor=props[i];descriptor.enumerable=descriptor.enumerable||!1,descriptor.configurable=!0,"value"in descriptor&&(descriptor.writable=!0),Object.defineProperty(target,descriptor.key,descriptor)}}return function(Constructor,protoProps,staticProps){return protoProps&&defineProperties(Constructor.prototype,protoProps),staticProps&&defineProperties(Constructor,staticProps),Constructor}}(),_CB=__webpack_require__(1),_CB2=_interopRequireDefault(_CB),CloudTable=function(){function CloudTable(tableName){_classCallCheck(this,CloudTable),_CB2.default._tableValidation(tableName),this.document={},this.document.name=tableName,this.document.appId=_CB2.default.appId,this.document._type="table",this.document.isEditableByClientKey=!1,"user"===tableName.toLowerCase()?(this.document.type="user",this.document.maxCount=1):"role"===tableName.toLowerCase()?(this.document.type="role",this.document.maxCount=1):"device"===tableName.toLowerCase()?(this.document.type="device",this.document.maxCount=1):(this.document.type="custom",this.document.maxCount=9999),this.document.columns=_CB2.default._defaultColumns(this.document.type)}return _createClass(CloudTable,[{key:"addColumn",value:function(column){if("[object String]"===Object.prototype.toString.call(column)){var obj=new _CB2.default.Column(column);column=obj}if("[object Object]"===Object.prototype.toString.call(column))_CB2.default._columnValidation(column,this)&&this.document.columns.push(column);else if("[object Array]"===Object.prototype.toString.call(column))for(var i=0;i<column.length;i++)_CB2.default._columnValidation(column[i],this)&&this.document.columns.push(column[i])}},{key:"getColumn",value:function(columnName){if("[object String]"!==Object.prototype.toString.call(columnName))throw"Should enter a columnName";for(var columns=this.document.columns,i=0;i<columns.length;i++)if(columns[i].name===columnName)return columns[i];throw"Column Does Not Exists"}},{key:"updateColumn",value:function(column){if("[object Object]"!==Object.prototype.toString.call(column))throw"Invalid Column";if(!_CB2.default._columnValidation(column,this))throw"Invalid Column";for(var columns=this.document.columns,i=0;i<columns.length;i++)if(columns[i].name===column.name){columns[i]=column,this.document.columns=columns;break}}},{key:"deleteColumn",value:function(column){if("[object String]"===Object.prototype.toString.call(column)){var obj=new _CB2.default.Column(column);column=obj}if("[object Object]"===Object.prototype.toString.call(column)){if(_CB2.default._columnValidation(column,this)){for(var arr=[],i=0;i<this.columns.length;i++)this.columns[i].name!==column.name&&arr.push(this.columns[i]);this.document.columns=arr}}else if("[object Array]"===Object.prototype.toString.call(column))for(var arr=[],i=0;i<column.length;i++)if(_CB2.default._columnValidation(column[i],this)){for(var i=0;i<this.columns.length;i++)this.columns[i].name!==column[i].name&&arr.push(this.columns[i]);this.document.columns=arr}}},{key:"delete",value:function(callback){_CB2.default._validate();var def;callback||(def=new _CB2.default.Promise);var params=JSON.stringify({key:_CB2.default.appKey,name:this.name,method:"DELETE"}),thisObj=this,url=_CB2.default.apiUrl+"/app/"+_CB2.default.appId+"/"+this.name;if(_CB2.default._request("PUT",url,params,!0).then(function(response){callback?callback.success(thisObj):def.resolve(thisObj)},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise}},{key:"save",value:function(callback){var def;callback||(def=new _CB2.default.Promise),_CB2.default._validate();var thisObj=this,params=JSON.stringify({key:_CB2.default.appKey,data:_CB2.default.toJSON(thisObj)}),thisObj=this,url=_CB2.default.apiUrl+"/app/"+_CB2.default.appId+"/"+thisObj.document.name;if(_CB2.default._request("PUT",url,params,!0).then(function(response){response=JSON.parse(response),thisObj=_CB2.default.fromJSON(response),callback?callback.success(thisObj):def.resolve(thisObj)},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise}}]),CloudTable}();Object.defineProperty(CloudTable.prototype,"columns",{get:function(){return this.document.columns}}),Object.defineProperty(CloudTable.prototype,"name",{get:function(){return this.document.name},set:function(){throw"You can not rename a table"}}),Object.defineProperty(CloudTable.prototype,"id",{get:function(){return this.document._id}}),Object.defineProperty(CloudTable.prototype,"isEditableByClientKey",{get:function(){return this.document.isEditableByClientKey},set:function(isEditableByClientKey){this.document.isEditableByClientKey=isEditableByClientKey}}),_CB2.default.CloudTable=CloudTable,_CB2.default.CloudTable.getAll=function(callback){if(!_CB2.default.appId)throw"CB.appId is null.";var def;callback||(def=new _CB2.default.Promise);var params=JSON.stringify({key:_CB2.default.appKey}),url=_CB2.default.apiUrl+"/app/"+_CB2.default.appId+"/_getAll";if(_CB2.default._request("POST",url,params,!0).then(function(response){try{response=JSON.parse(response);var tableDocumentArray=_CB2.default.fromJSON(response),obj=tableDocumentArray.filter(function(table){return!!table});callback?callback.success(obj):def.resolve(obj)}catch(err){callback?callback.error(err):def.reject(err)}},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise},_CB2.default.CloudTable.get=function(table,callback){if("[object String]"===Object.prototype.toString.call(table)){var obj=new this(table);table=obj}if("[object Object]"===Object.prototype.toString.call(table)){if(!_CB2.default.appId)throw"CB.appId is null.";var def;callback||(def=new _CB2.default.Promise);var params=JSON.stringify({key:_CB2.default.appKey,appId:_CB2.default.appId}),url=_CB2.default.apiUrl+"/app/"+_CB2.default.appId+"/"+table.document.name;if(_CB2.default._request("POST",url,params,!0).then(function(response){if("null"===response||""===response)obj=null;else{response=JSON.parse(response);var obj=_CB2.default.fromJSON(response)}callback?callback.success(obj):def.resolve(obj)},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise}else if("[object Array]"===Object.prototype.toString.call(table))throw"cannot fetch array of tables"},exports.default=_CB2.default.CloudTable},function(module,exports,__webpack_require__){"use strict";function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(exports,"__esModule",{value:!0});var _CB=__webpack_require__(1),_CB2=_interopRequireDefault(_CB);_CB2.default.ACL=function(){this.document={},this.document.read={allow:{user:["all"],role:[]},deny:{user:[],role:[]}},this.document.write={allow:{user:["all"],role:[]},deny:{user:[],role:[]}},this.parent=null},_CB2.default.ACL.prototype.setPublicWriteAccess=function(value){if(value)this.document.write.allow.user=["all"];else{var index=this.document.write.allow.user.indexOf("all");index>-1&&this.document.write.allow.user.splice(index,1)}this.parent&&_CB2.default._modified(this.parent,"ACL")},_CB2.default.ACL.prototype.setPublicReadAccess=function(value){if(value)this.document.read.allow.user=["all"];else{var index=this.document.read.allow.user.indexOf("all");index>-1&&this.document.read.allow.user.splice(index,1)}this.parent&&_CB2.default._modified(this.parent,"ACL")},_CB2.default.ACL.prototype.setUserWriteAccess=function(userId,value){if(value){var index=this.document.write.allow.user.indexOf("all");index>-1&&this.document.write.allow.user.splice(index,1),this.document.write.allow.user.indexOf(userId)===-1&&this.document.write.allow.user.push(userId)}else{var index=this.document.write.allow.user.indexOf(userId);index>-1&&this.document.write.allow.user.splice(index,1),this.document.write.deny.user.push(userId)}this.parent&&_CB2.default._modified(this.parent,"ACL")},_CB2.default.ACL.prototype.setUserReadAccess=function(userId,value){if(value){var index=this.document.read.allow.user.indexOf("all");index>-1&&this.document.read.allow.user.splice(index,1),this.document.read.allow.user.indexOf(userId)===-1&&this.document.read.allow.user.push(userId)}else{var index=this.document.read.allow.user.indexOf(userId);index>-1&&this.document.read.allow.user.splice(index,1),this.document.read.deny.user.push(userId)}this.parent&&_CB2.default._modified(this.parent,"ACL")},_CB2.default.ACL.prototype.setRoleWriteAccess=function(roleId,value){if(value){var index=this.document.write.allow.user.indexOf("all");index>-1&&this.document.write.allow.user.splice(index,1),this.document.write.allow.role.indexOf(roleId)===-1&&this.document.write.allow.role.push(roleId)}else{var index=this.document.write.allow.role.indexOf(roleId);index>-1&&this.document.write.allow.role.splice(index,1);var index=this.document.write.allow.user.indexOf("all");index>-1&&this.document.write.allow.user.splice(index,1),this.document.write.deny.role.push(roleId)}this.parent&&_CB2.default._modified(this.parent,"ACL")},_CB2.default.ACL.prototype.setRoleReadAccess=function(roleId,value){if(value){var index=this.document.read.allow.user.indexOf("all");index>-1&&this.document.read.allow.user.splice(index,1),this.document.read.allow.role.indexOf(roleId)===-1&&this.document.read.allow.role.push(roleId)}else{var index=this.document.read.allow.role.indexOf(roleId);index>-1&&this.document.read.allow.role.splice(index,1);var index=this.document.read.allow.user.indexOf("all");index>-1&&this.document.read.allow.user.splice(index,1),this.document.read.deny.role.push(roleId)}this.parent&&_CB2.default._modified(this.parent,"ACL")},exports.default=_CB2.default.ACL},function(module,exports,__webpack_require__){"use strict";function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor))throw new TypeError("Cannot call a class as a function")}function greatCircleFormula(thisObj,point){var dLat=(thisObj.document.coordinates[1]-point.document.coordinates[1]).toRad(),dLon=(thisObj.document.coordinates[0]-point.document.coordinates[0]).toRad(),lat1=point.document.coordinates[1].toRad(),lat2=thisObj.document.coordinates[1].toRad(),a=Math.sin(dLat/2)*Math.sin(dLat/2)+Math.sin(dLon/2)*Math.sin(dLon/2)*Math.cos(lat1)*Math.cos(lat2),c=2*Math.atan2(Math.sqrt(a),Math.sqrt(1-a));return c}Object.defineProperty(exports,"__esModule",{value:!0});var _createClass=function(){function defineProperties(target,props){for(var i=0;i<props.length;i++){var descriptor=props[i];descriptor.enumerable=descriptor.enumerable||!1,descriptor.configurable=!0,"value"in descriptor&&(descriptor.writable=!0),Object.defineProperty(target,descriptor.key,descriptor)}}return function(Constructor,protoProps,staticProps){return protoProps&&defineProperties(Constructor.prototype,protoProps),staticProps&&defineProperties(Constructor,staticProps),Constructor}}(),_CB=__webpack_require__(1),_CB2=_interopRequireDefault(_CB),CloudGeoPoint=function(){function CloudGeoPoint(longitude,latitude){if(_classCallCheck(this,CloudGeoPoint),!latitude&&0!==latitude||!longitude&&0!==longitude)throw"Latitude or Longitude is empty.";if(isNaN(latitude))throw"Latitude "+latitude+" is not a number type.";if(isNaN(longitude))throw"Longitude "+longitude+" is not a number type.";if(this.document={},this.document._type="point",this.document._isModified=!0,!(Number(latitude)>=-90&&Number(latitude)<=90&&Number(longitude)>=-180&&Number(longitude)<=180))throw"latitude and longitudes are not in range";this.document.coordinates=[Number(longitude),Number(latitude)],this.document.latitude=Number(latitude),this.document.longitude=Number(longitude)}return _createClass(CloudGeoPoint,[{key:"get",value:function(name){return this.document[name]}},{key:"set",value:function(name,value){if("latitude"===name){if(!(Number(value)>=-90&&Number(value)<=90))throw"Latitude is not in Range";this.document.latitude=Number(value),this.document.coordinates[1]=Number(value),this.document._isModified=!0}else{if(!(Number(value)>=-180&&Number(value)<=180))throw"Latitude is not in Range";this.document.longitude=Number(value),this.document.coordinates[0]=Number(value),this.document._isModified=!0}}},{key:"distanceInKMs",value:function(point){var earthRedius=6371;return earthRedius*greatCircleFormula(this,point)}},{key:"distanceInMiles",value:function(point){var earthRedius=3959;return earthRedius*greatCircleFormula(this,point)}},{key:"distanceInRadians",value:function(point){return greatCircleFormula(this,point)}}]),CloudGeoPoint}();Object.defineProperty(CloudGeoPoint.prototype,"latitude",{get:function(){return this.document.coordinates[1]},set:function(latitude){if(!(Number(latitude)>=-90&&Number(latitude)<=90))throw"Latitude is not in Range";this.document.latitude=Number(latitude),this.document.coordinates[1]=Number(latitude),this.document._isModified=!0}}),Object.defineProperty(CloudGeoPoint.prototype,"longitude",{get:function(){return this.document.coordinates[0]},set:function(longitude){if(!(Number(longitude)>=-180&&Number(longitude)<=180))throw"Longitude is not in Range";this.document.longitude=Number(longitude),this.document.coordinates[0]=Number(longitude),this.document._isModified=!0}}),"undefined"==typeof Number.prototype.toRad&&(Number.prototype.toRad=function(){return this*Math.PI/180}),_CB2.default.CloudGeoPoint=_CB2.default.CloudGeoPoint||CloudGeoPoint,exports.default=_CB2.default.CloudGeoPoint},function(module,exports,__webpack_require__){"use strict";function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor))throw new TypeError("Cannot call a class as a function")}function _groupObjects(objects){for(var groups={},i=0;i<objects.length;i++){if(!(objects[i]instanceof _CB2.default.CloudObject))throw"Should Be an instance of CloudObjects";var groupName=objects[i].document._tableName;groups[groupName]||(groups[groupName]=[]),groups[groupName].push(objects[i].document)}objects=[];for(var groupName in groups)objects.push({tableName:groupName,object:groups[groupName]});return objects}Object.defineProperty(exports,"__esModule",{value:!0});var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(obj){return typeof obj}:function(obj){return obj&&"function"==typeof Symbol&&obj.constructor===Symbol&&obj!==Symbol.prototype?"symbol":typeof obj},_createClass=function(){function defineProperties(target,props){for(var i=0;i<props.length;i++){var descriptor=props[i];descriptor.enumerable=descriptor.enumerable||!1,descriptor.configurable=!0,"value"in descriptor&&(descriptor.writable=!0),Object.defineProperty(target,descriptor.key,descriptor)}}return function(Constructor,protoProps,staticProps){return protoProps&&defineProperties(Constructor.prototype,protoProps),staticProps&&defineProperties(Constructor,staticProps),Constructor}}(),_CB=__webpack_require__(1),_CB2=_interopRequireDefault(_CB),_localforage=__webpack_require__(73),_localforage2=_interopRequireDefault(_localforage),CloudObject=function(){function CloudObject(tableName,id){_classCallCheck(this,CloudObject),this.document={},this.document._tableName=tableName,this.document.ACL=new _CB2.default.ACL,this.document._type="custom",this.document.expires=null,this.document._hash=_CB2.default._generateHash(),id?(this.document._modifiedColumns=[],this.document._isModified=!1,this.document._id=id):(this.document._modifiedColumns=["createdAt","updatedAt","ACL","expires"],this.document._isModified=!0)}return _createClass(CloudObject,[{key:"set",value:function(columnName,data){var keywords=["_tableName","_type","operator"];if("id"===columnName||"_id"===columnName)throw"You cannot set the id of a CloudObject";if("id"===columnName&&(columnName="_"+columnName),keywords.indexOf(columnName)>-1)throw columnName+" is a keyword. Please choose a different column name.";this.document[columnName]=data,_CB2.default._modified(this,columnName)}},{key:"relate",value:function(columnName,objectTableName,objectId){var keywords=["_tableName","_type","operator"];if("id"===columnName||"_id"===columnName)throw"You cannot set the id of a CloudObject";if("id"===columnName)throw"You cannot link an object to this column";if(keywords.indexOf(columnName)>-1)throw columnName+" is a keyword. Please choose a different column name.";this.document[columnName]=new _CB2.default.CloudObject(objectTableName,objectId),_CB2.default._modified(this,columnName)}},{key:"get",value:function(columnName){return"id"===columnName&&(columnName="_"+columnName),this.document[columnName]}},{key:"unset",value:function(columnName){this.document[columnName]=null,_CB2.default._modified(this,columnName)}},{key:"save",value:function(callback){var def;_CB2.default._validate(),callback||(def=new _CB2.default.Promise);if(_CB2.default._fileCheck(this).then(function(thisObj){var params=JSON.stringify({document:_CB2.default.toJSON(thisObj),key:_CB2.default.appKey}),url=_CB2.default.apiUrl+"/data/"+_CB2.default.appId+"/"+thisObj.document._tableName;_CB2.default._request("PUT",url,params).then(function(response){thisObj=_CB2.default.fromJSON(JSON.parse(response),thisObj),callback?callback.success(thisObj):def.resolve(thisObj)},function(err){callback?callback.error(err):def.reject(err)})},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise}},{key:"pin",value:function(callback){_CB2.default.CloudObject.pin(this,callback)}},{key:"unPin",
value:function(callback){_CB2.default.CloudObject.unPin(this,callback)}},{key:"saveEventually",value:function(callback){var def,thisObj=this;if(callback||(def=new _CB2.default.Promise),_CB2.default._validate(),_localforage2.default.getItem("cb-saveEventually-"+_CB2.default.appId).then(function(value){var arr=[];value&&(arr=value),arr.push({saved:!1,document:_CB2.default.toJSON(thisObj)}),_localforage2.default.setItem("cb-saveEventually-"+_CB2.default.appId,arr).then(function(value){CloudObject.pin(thisObj,{success:function(obj){callback?callback.success(value):def.resolve(value)},error:function(err){callback?callback.error(err):def.reject(err)}})}).catch(function(err){callback?callback.error(err):def.reject(err)})}).catch(function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise}},{key:"disableSync",value:function(callback){_CB2.default.CloudObject.disableSync(this.document,callback)}},{key:"fetch",value:function(callback){if(!_CB2.default.appId)throw"CB.appId is null.";if(!this.document._id)throw"Can't fetch an object which is not saved.";var def,thisObj=this;callback||(def=new _CB2.default.Promise);var query=null;if(query="file"===thisObj.document._type?new _CB2.default.CloudQuery("_File"):new _CB2.default.CloudQuery(thisObj.document._tableName),query.findById(thisObj.get("id")).then(function(res){callback?callback.success(res):def.resolve(res)},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise}},{key:"delete",value:function(callback){if(!_CB2.default.appId)throw"CB.appId is null.";if(!this.document._id)throw"You cannot delete an object which is not saved.";var def,thisObj=this;callback||(def=new _CB2.default.Promise);var params=JSON.stringify({key:_CB2.default.appKey,document:_CB2.default.toJSON(thisObj),method:"DELETE"}),url=_CB2.default.apiUrl+"/data/"+_CB2.default.appId+"/"+thisObj.document._tableName;if(_CB2.default._request("PUT",url,params).then(function(response){thisObj=_CB2.default.fromJSON(JSON.parse(response),thisObj),callback?callback.success(thisObj):def.resolve(thisObj)},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise}}]),CloudObject}();Object.defineProperty(CloudObject.prototype,"ACL",{get:function(){return this.document.ACL},set:function(ACL){this.document.ACL=ACL,this.document.ACL.parent=this,_CB2.default._modified(this,"ACL")}}),Object.defineProperty(CloudObject.prototype,"id",{get:function(){return this.document._id}}),Object.defineProperty(CloudObject.prototype,"createdAt",{get:function(){return this.document.createdAt}}),Object.defineProperty(CloudObject.prototype,"updatedAt",{get:function(){return this.document.updatedAt}}),Object.defineProperty(CloudObject.prototype,"expires",{get:function(){return this.document.expires},set:function(expires){this.document.expires=expires,_CB2.default._modified(this,"expires")}}),CloudObject.on=function(tableName,eventType,cloudQuery,callback,done){if(_CB2.default._isRealtimeDisabled)throw"Realtime is disbaled for this app.";var def;if(!cloudQuery||cloudQuery instanceof _CB2.default.CloudQuery||(null!==callback&&"object"===("undefined"==typeof callback?"undefined":_typeof(callback))&&(done=callback,callback=null),callback=cloudQuery,cloudQuery=null),done||(def=new _CB2.default.Promise),cloudQuery&&cloudQuery instanceof _CB2.default.CloudQuery){if(cloudQuery.tableName!==tableName)throw"CloudQuery TableName and CloudNotification TableName should be same.";if(cloudQuery.query&&cloudQuery.query.$include.length>0)throw"Include with CloudNotificaitons is not supported right now.";if(Object.keys(cloudQuery.select).length>0)throw"You cannot pass the query with select in CloudNotifications."}if(tableName=tableName.toLowerCase(),eventType instanceof Array)for(var i=0;i<eventType.length;i++)_CB2.default.CloudObject.on(tableName,eventType[i],cloudQuery,callback),i==eventType.length-1&&(done&&done.success?done.success():def.resolve());else{if(eventType=eventType.toLowerCase(),"created"!==eventType&&"updated"!==eventType&&"deleted"!==eventType)throw"created, updated, deleted are supported notification types.";var timestamp=_CB2.default._generateHash(),payload={room:(_CB2.default.appId+"table"+tableName+eventType).toLowerCase()+timestamp,sessionId:_CB2.default._getSessionId(),data:{query:cloudQuery,timestamp:timestamp,eventType:eventType,appKey:_CB2.default.appKey}};_CB2.default.Socket.emit("join-object-channel",payload),_CB2.default.Socket.on(payload.room,function(data){data=JSON.parse(data),data=_CB2.default.fromJSON(data),cloudQuery&&cloudQuery instanceof _CB2.default.CloudQuery&&_CB2.default.CloudObject._validateNotificationQuery(data,cloudQuery)?callback(data):cloudQuery||callback(data)}),done&&done.success?done.success():def.resolve()}if(!done)return def.promise},CloudObject.off=function(tableName,eventType,done){if(_CB2.default._isRealtimeDisabled)throw"Realtime is disbaled for this app.";var def;if(done||(def=new _CB2.default.Promise),tableName=tableName.toLowerCase(),eventType instanceof Array)for(var i=0;i<eventType.length;i++)_CB2.default.CloudObject.off(tableName,eventType[i]),i==eventType.length-1&&(done&&done.success?done.success():def.resolve());else{eventType=eventType.toLowerCase();var timestamp=_CB2.default._generateHash();if("created"!==eventType&&"updated"!==eventType&&"deleted"!==eventType)throw"created, updated, deleted are supported notification types.";_CB2.default.Socket.emit("leave-object-channel",{event:(_CB2.default.appId+"table"+tableName+eventType).toLowerCase(),timestamp:timestamp,eventType:eventType}),_CB2.default.Socket.on("leave"+(_CB2.default.appId+"table"+tableName+eventType).toLowerCase()+timestamp,function(data){_CB2.default.Socket.removeAllListeners((_CB2.default.appId+"table"+tableName+eventType).toLowerCase()+data)}),done&&done.success?done.success():def.resolve()}if(!done)return def.promise},CloudObject.saveAll=function(array,callback){if(!array||array.constructor!==Array)throw"Array of CloudObjects is Null";for(var i=0;i<array.length;i++)if(!(array[i]instanceof _CB2.default.CloudObject))throw"Should Be an Array of CloudObjects";var def;if(callback||(def=new _CB2.default.Promise),_CB2.default._bulkObjFileCheck(array).then(function(){var params=JSON.stringify({document:_CB2.default.toJSON(array),key:_CB2.default.appKey}),url=_CB2.default.apiUrl+"/data/"+_CB2.default.appId+"/"+array[0]._tableName;_CB2.default._request("PUT",url,params).then(function(response){var thisObj=_CB2.default.fromJSON(JSON.parse(response));callback?callback.success(thisObj):def.resolve(thisObj)},function(err){callback?callback.error(err):def.reject(err)})},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise},CloudObject.deleteAll=function(array,callback){if(!array&&array.constructor!==Array)throw"Array of CloudObjects is Null";for(var i=0;i<array.length;i++)if(!(array[i]instanceof _CB2.default.CloudObject))throw"Should Be an Array of CloudObjects";var def;callback||(def=new _CB2.default.Promise);var params=JSON.stringify({document:_CB2.default.toJSON(array),key:_CB2.default.appKey,method:"DELETE"}),url=_CB2.default.apiUrl+"/data/"+_CB2.default.appId+"/"+array[0]._tableName;if(_CB2.default._request("PUT",url,params).then(function(response){var thisObj=_CB2.default.fromJSON(JSON.parse(response));callback?callback.success(thisObj):def.resolve(thisObj)},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise},CloudObject.pin=function(cloudObjects,callback){if(!cloudObjects)throw"cloudObject(s) is required.";var def;if(callback||(def=new _CB2.default.Promise),_CB2.default._validate(),cloudObjects instanceof Array){var groupedObjects=_groupObjects(cloudObjects);groupedObjects.forEach(function(object){var arr=[];_localforage2.default.getItem(_CB2.default.appId+"-"+object.tableName).then(function(value){value&&(arr=value),_localforage2.default.setItem(_CB2.default.appId+"-"+object.tableName,arr.concat(object.object)).then(function(value){callback?callback.success(value):def.resolve(value)}).catch(function(err){callback?callback.error(err):def.reject(err)})}).catch(function(err){callback?callback.error(err):def.reject(err)})})}else cloudObjects=[cloudObjects],CloudObject.pin(cloudObjects,callback);if(!callback)return def.promise},CloudObject.unPin=function(cloudObjects,callback){if(!cloudObjects)throw"cloudObject(s) is required.";var def;if(callback||(def=new _CB2.default.Promise),_CB2.default._validate(),cloudObjects instanceof Array){var groupedObjects=_groupObjects(cloudObjects);groupedObjects.forEach(function(object){_localforage2.default.getItem(_CB2.default.appId+"-"+object.tableName).then(function(objects){var arr=[];objects.forEach(function(obj){object.object.forEach(function(cloudObject){cloudObject._hash!=obj._hash&&arr.push(obj)})}),_localforage2.default.setItem(_CB2.default.appId+"-"+object.tableName,arr).then(function(obj){callback?callback.success(obj):def.resolve(obj)}).catch(function(err){callback?callback.error(err):def.reject(err)})}).catch(function(err){callback?callback.error(err):def.reject(err)})})}else cloudObjects=[cloudObjects],CloudObject.unPin(cloudObjects);if(!callback)return def.promise},CloudObject.clearLocalStore=function(callback){_CB2.default._validate();var def;if(callback||(def=new _CB2.default.Promise),_localforage2.default.clear().then(function(){callback?callback.success():def.resolve()}).catch(function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise},CloudObject.sync=function(callback){var def;if(callback||(def=new _CB2.default.Promise),_CB2.default._validate(),_CB2.default.CloudApp._isConnected?_localforage2.default.getItem("cb-saveEventually-"+_CB2.default.appId).then(function(documents){var count=0,cloudObject=null;if(documents){var length=documents.length;documents.forEach(function(document){length--,document.saved||(cloudObject=_CB2.default.fromJSON(document.document),cloudObject.save({success:function(obj){count++,_CB2.default.CloudObject.disableSync(document.document,{success:function(obj){callback?callback.success(count):def.resolve(count)},error:function(err){callback?callback.error(err):def.reject(err)}})},error:function(err){callback?callback.error(err):def.reject(err)}}))})}else callback?callback.success("Already up to date"):def.resolve("Already up to date.")}).catch(function(err){callback?callback.error(err):def.reject(err)}):callback?callback.error("Internet connection not found."):def.reject("Internet connection not found."),!callback)return def.promise},CloudObject.disableSync=function(document,callback){var def;if(callback||(def=new _CB2.default.Promise),_CB2.default._validate(),_localforage2.default.getItem("cb-saveEventually-"+_CB2.default.appId).then(function(values){var arr=[];values.forEach(function(value){value.document._hash!=document._hash&&arr.push(value)}),_localforage2.default.setItem("cb-saveEventually-"+_CB2.default.appId,arr).then(function(obj){callback?callback.success(obj):def.resolve(obj)}).catch(function(err){callback?callback.error(err):def.reject(err)})}).catch(function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise},CloudObject._validateNotificationQuery=function(cloudObject,cloudQuery){if(!cloudQuery)throw"CloudQuery is null";if(!cloudQuery.query)throw"There is no query in CloudQuery";cloudQuery.query;return 0!==cloudQuery.limit&&(cloudQuery.skip>0?(--cloudQuery.skip,!1):(--cloudQuery.limit,!0))},_CB2.default.CloudObject=CloudObject,exports.default=_CB2.default.CloudObject},function(module,exports,__webpack_require__){"use strict";function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(exports,"__esModule",{value:!0});var _CB=__webpack_require__(1),_CB2=_interopRequireDefault(_CB);_CB2.default.CloudFile=_CB2.default.CloudFile||function(file,data,type,path){if(path||(path="/"+_CB2.default.appId),"[object File]"===Object.prototype.toString.call(file)||"[object Blob]"===Object.prototype.toString.call(file))this.fileObj=file,this.document={_id:null,_type:"file",_tableName:"_File",ACL:new _CB2.default.ACL,name:file&&file.name&&""!==file.name?file.name:"default.file",size:file.size,url:null,expires:null,path:path,updatedAt:Date.now(),createdAt:Date.now(),contentType:"undefined"!=typeof file.type&&""!==file.type?file.type:"unknown"},this.document._modifiedColumns=["name","updatedAt","ACL","expires","size","url","path","createdAt","contentType"],this.document._isModified=!0;else if("string"==typeof file){var regexp=RegExp("https?://(?:www.|(?!www))[^s.]+.[^s]{2,}|www.[^s]+.[^s]{2,}");regexp.test(file)?(this.document={_id:null,_type:"file",_tableName:"_File",ACL:new _CB2.default.ACL,name:"",size:"",url:file,expires:null,path:path,updatedAt:Date.now(),createdAt:Date.now(),contentType:""},this.document._modifiedColumns=["name","updatedAt","ACL","expires","size","url","path","createdAt","contentType"],this.document._isModified=!0):data?(this.data=data,type||(type=file.split(".")[file.split(".").length-1]),this.document={_id:null,_type:"file",_tableName:"_File",ACL:new _CB2.default.ACL,name:file,size:"",url:null,path:path,updatedAt:Date.now(),createdAt:Date.now(),expires:null,contentType:type},this.document._modifiedColumns=["name","updatedAt","ACL","expires","size","url","path","createdAt","contentType"],this.document._isModified=!0):(this.document={_id:file,_type:"file",_tableName:"_File"},this.document._modifiedColumns=["name","updatedAt","ACL","expires","size","url","path","createdAt","contentType"],this.document._isModified=!0)}},_CB2.default.CloudFile.prototype=Object.create(_CB2.default.CloudObject.prototype),Object.defineProperty(_CB2.default.CloudFile.prototype,"type",{get:function(){return this.document.contentType},set:function(type){this.document.contentType=type}}),Object.defineProperty(_CB2.default.CloudFile.prototype,"url",{get:function(){return this.document.url},set:function(url){this.document.url=url}}),Object.defineProperty(_CB2.default.CloudFile.prototype,"size",{get:function(){return this.document.size}}),Object.defineProperty(_CB2.default.CloudFile.prototype,"name",{get:function(){return this.document.name},set:function(name){this.document.name=name}}),Object.defineProperty(_CB2.default.CloudFile.prototype,"path",{get:function(){return this.document.path},set:function(path){this.document.path=path}}),Object.defineProperty(_CB2.default.CloudFile.prototype,"createdAt",{get:function(){return this.document.createdAt}}),Object.defineProperty(_CB2.default.CloudFile.prototype,"updatedAt",{get:function(){return this.document.updatedAt}}),_CB2.default.CloudFile.prototype.save=function(callback){var def;callback||(def=new _CB2.default.Promise);var thisObj=this;if(!this.fileObj&&!this.data&&"folder"!=this.type&&!this.url)throw"You cannot save a file which is null";if(this.data){var data=this.data,params=JSON.stringify({data:data,fileObj:_CB2.default.toJSON(this),key:_CB2.default.appKey}),url=_CB2.default.apiUrl+"/file/"+_CB2.default.appId,uploadProgressCallback=null;callback&&callback.uploadProgress&&(uploadProgressCallback=callback.uploadProgress),_CB2.default._request("POST",url,params,null,null,uploadProgressCallback).then(function(response){thisObj.document=JSON.parse(response),delete thisObj.data,callback?callback.success(thisObj):def.resolve(thisObj)},function(err){callback?callback.error(err):def.reject(err)})}else{var params;try{window?(params=new FormData,params.append("fileToUpload",this.fileObj),params.append("key",_CB2.default.appKey),params.append("fileObj",JSON.stringify(_CB2.default.toJSON(thisObj)))):(params={},params.fileToUpload=this.fileObj,params.key=_CB2.default.appKey,params.fileObj=JSON.stringify(_CB2.default.toJSON(thisObj)))}catch(e){params={},params.fileToUpload=this.fileObj,params.key=_CB2.default.appKey,params.fileObj=JSON.stringify(_CB2.default.toJSON(thisObj))}var url=_CB2.default.apiUrl+"/file/"+_CB2.default.appId,uploadProgressCallback=null;callback&&callback.uploadProgress&&(uploadProgressCallback=callback.uploadProgress),_CB2.default._request("POST",url,params,!1,!0,uploadProgressCallback).then(function(response){thisObj.document=JSON.parse(response),callback?callback.success(thisObj):def.resolve(thisObj)},function(err){callback?callback.error(err):def.reject(err)})}if(!callback)return def.promise},_CB2.default.CloudFile.prototype.delete=function(callback){var def;if(!this.url)throw"You cannot delete a file which does not have an URL";callback||(def=new _CB2.default.Promise);var thisObj=this,params=JSON.stringify({fileObj:_CB2.default.toJSON(thisObj),key:_CB2.default.appKey,method:"PUT"}),url=_CB2.default.apiUrl+"/file/"+_CB2.default.appId+"/"+this.document._id;if(_CB2.default._request("PUT",url,params).then(function(response){thisObj.url=null,callback?callback.success(thisObj):def.resolve(thisObj)},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise},_CB2.default.CloudFile.prototype.getFileContent=function(callback){var def;if(!this.url)throw"URL is null. Fetch this file object first using fetch()";callback||(def=new _CB2.default.Promise);var params=JSON.stringify({key:_CB2.default.appKey}),url=this.url;if(_CB2.default._request("POST",url,params).then(function(response){callback?callback.success(response):def.resolve(response)},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise},exports.default=_CB2.default.CloudFile},function(module,exports,__webpack_require__){"use strict";function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(exports,"__esModule",{value:!0});var _CB=__webpack_require__(1),_CB2=_interopRequireDefault(_CB),CloudRole=function CloudRole(roleName){_classCallCheck(this,CloudRole),this.document||(this.document={}),this.document._tableName="Role",this.document._type="role",this.document.name=roleName,this.document.expires=null,this.document.ACL=new _CB2.default.ACL,this.document.expires=null,this.document._isModified=!0,this.document._modifiedColumns=["createdAt","updatedAt","ACL","name","expires"]};CloudRole.prototype=Object.create(_CB2.default.CloudObject.prototype),Object.defineProperty(CloudRole.prototype,"name",{get:function(){return this.document.name},set:function(name){this.document.name=name,_CB2.default._modified(this,name)}}),_CB2.default.CloudRole=_CB2.default.CloudRole||CloudRole,exports.default=_CB2.default.CloudRole},function(module,exports,__webpack_require__){"use strict";function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor))throw new TypeError("Cannot call a class as a function")}function _getBrowser(){try{var isOpera=!!window.opr&&!!opr.addons||!!window.opera||navigator.userAgent.indexOf(" OPR/")>=0,isFirefox="undefined"!=typeof InstallTrigger,isSafari=/constructor/i.test(window.HTMLElement)||function(p){return"[object SafariRemoteNotification]"===p.toString()}(!window.safari||safari.pushNotification),isIE=!!document.documentMode,isEdge=!isIE&&!!window.StyleMedia,isChrome=!!window.chrome&&!!window.chrome.webstore;return isChrome?"Chrome":isEdge?"Edge":isIE?"Internet Explorer":isSafari?"Safari":isFirefox?"Firefox":isOpera?"Opera":"Other"}catch(e){return"other"}}function _getLocation(obj,callback){_axios2.default.get("https://ipinfo.io/json").then(function(data){obj.ip=data.data.ip,obj.city=data.data.city,obj.region=data.data.region,obj.country=data.data.country,obj.loc=data.data.loc,callback.success(obj)}).catch(function(err){obj.message="App is Offline",callback.success(obj)})}Object.defineProperty(exports,"__esModule",{value:!0});var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(obj){return typeof obj}:function(obj){return obj&&"function"==typeof Symbol&&obj.constructor===Symbol&&obj!==Symbol.prototype?"symbol":typeof obj},_CB=__webpack_require__(1),_CB2=_interopRequireDefault(_CB),_axios=__webpack_require__(50),_axios2=_interopRequireDefault(_axios),CloudEvent=(__webpack_require__(84),function CloudEvent(){_classCallCheck(this,CloudEvent)});CloudEvent.track=function(name,data,type,callback){var def;if("object"===("undefined"==typeof type?"undefined":_typeof(type))){if(null!=callback)throw"'type' cannot be an object.";callback=type,type="Custom"}if(type||(type="Custom"),callback||(def=new _CB2.default.Promise),CloudEvent._getDeviceInformation({success:function(object){data.device=object;var obj=new _CB2.default.CloudObject("_Event");obj.ACL=new _CB2.default.ACL,obj.ACL.setPublicReadAccess(!1),obj.ACL.setPublicWriteAccess(!1),obj.set("user",_CB2.default.CloudUser.current),obj.set("name",name),obj.set("data",data),obj.set("type",type),obj.save({success:function(obj){callback?callback.success(obj):def.resolve(obj)},error:function(err){callback?callback.error(err):def.reject(err)}})}}),!callback)return def.promise},CloudEvent._getDeviceInformation=function(callback){var obj=new Object;_CB2.default._isNode?obj.browser="node":obj.browser=_getBrowser(),_getLocation(obj,{success:function(object){callback.success(object)}})},CloudEvent._os=function(){},_CB2.default.CloudEvent=CloudEvent,exports.default=CloudEvent},function(module,exports){exports.endianness=function(){return"LE"},exports.hostname=function(){return"undefined"!=typeof location?location.hostname:""},exports.loadavg=function(){return[]},exports.uptime=function(){return 0},exports.freemem=function(){return Number.MAX_VALUE},exports.totalmem=function(){return Number.MAX_VALUE},exports.cpus=function(){return[]},exports.type=function(){return"Browser"},exports.release=function(){return"undefined"!=typeof navigator?navigator.appVersion:""},exports.networkInterfaces=exports.getNetworkInterfaces=function(){return{}},exports.arch=function(){return"javascript"},exports.platform=function(){return"browser"},exports.tmpdir=exports.tmpDir=function(){return"/tmp"},exports.EOL="\n"},function(module,exports,__webpack_require__){"use strict";function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(exports,"__esModule",{value:!0});var _CB=__webpack_require__(1),_CB2=_interopRequireDefault(_CB),CloudUser=function CloudUser(){_classCallCheck(this,CloudUser),this.document||(this.document={}),this.document._tableName="User",this.document.expires=null,this.document._type="user",this.document.expires=null,this.document.ACL=new _CB2.default.ACL,this.document._isModified=!0,this.document._modifiedColumns=["createdAt","updatedAt","ACL","expires"]};_CB2.default.CloudUser=_CB2.default.CloudUser||CloudUser,_CB2.default.CloudUser.getCurrentUser=function(callback){var def;callback||(def=new _CB2.default.Promise);var params=JSON.stringify({key:_CB2.default.appKey}),url=_CB2.default.apiUrl+"/user/"+_CB2.default.appId+"/currentUser";if(_CB2.default._request("POST",url,params).then(function(response){var user=response;if(response)try{user=new _CB2.default.CloudUser,_CB2.default.fromJSON(JSON.parse(response),user),_CB2.default.CloudUser.current=user,_CB2.default.CloudUser._setCurrentUser(user)}catch(e){}callback?callback.success(user):def.resolve(user)},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise},_CB2.default.CloudUser._getCurrentUser=function(){var content=_CB2.default._getCookie("CBCurrentUser");return content&&content.length>0?_CB2.default.fromJSON(JSON.parse(content)):null},_CB2.default.CloudUser._setCurrentUser=function(user){user&&_CB2.default._createCookie("CBCurrentUser",JSON.stringify(_CB2.default.toJSON(user)),2592e6)},_CB2.default.CloudUser._removeCurrentUser=function(){_CB2.default._deleteCookie("CBCurrentUser")},_CB2.default.CloudUser.resetPassword=function(email,callback){if(!email)throw"Email is required.";var def;callback||(def=new _CB2.default.Promise);var params=JSON.stringify({email:email,key:_CB2.default.appKey}),url=_CB2.default.apiUrl+"/user/"+_CB2.default.appId+"/resetPassword";if(_CB2.default._request("POST",url,params).then(function(response){callback?callback.success():def.resolve()},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise},_CB2.default.CloudUser.prototype=Object.create(_CB2.default.CloudObject.prototype),Object.defineProperty(_CB2.default.CloudUser.prototype,"username",{get:function(){return this.document.username},set:function(username){this.document.username=username,_CB2.default._modified(this,"username")}}),Object.defineProperty(_CB2.default.CloudUser.prototype,"password",{get:function(){return this.document.password},set:function(password){this.document.password=password,_CB2.default._modified(this,"password")}}),Object.defineProperty(_CB2.default.CloudUser.prototype,"email",{get:function(){return this.document.email},set:function(email){this.document.email=email,_CB2.default._modified(this,"email")}}),_CB2.default.CloudUser.current=_CB2.default.CloudUser._getCurrentUser(),_CB2.default.CloudUser.prototype.signUp=function(callback){if(_CB2.default._isNode&&!_CB2.default._isNative)throw"Error : You cannot signup the user on the server. Use CloudUser.save() instead.";if(!this.document.username)throw"Username is not set.";if(!this.document.password)throw"Password is not set.";if(!this.document.email)throw"Email is not set.";var def,thisObj=this;callback||(def=new _CB2.default.Promise);var params=JSON.stringify({document:_CB2.default.toJSON(thisObj),key:_CB2.default.appKey}),url=_CB2.default.apiUrl+"/user/"+_CB2.default.appId+"/signup";if(_CB2.default._request("POST",url,params).then(function(user){var response=null;user&&""!=user&&(_CB2.default.fromJSON(JSON.parse(user),thisObj),_CB2.default.CloudUser.current=thisObj,_CB2.default.CloudUser._setCurrentUser(thisObj),response=thisObj,function(thisObj){setTimeout(function(){_CB2.default.CloudEvent.track("Signup",{username:thisObj.username,email:thisObj.email})},1e3)}(thisObj)),callback?callback.success(response):def.resolve(response)},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise},_CB2.default.CloudUser.prototype.changePassword=function(oldPassword,newPassword,callback){var def,thisObj=this;callback||(def=new _CB2.default.Promise);var params=JSON.stringify({oldPassword:oldPassword,newPassword:newPassword,key:_CB2.default.appKey}),url=_CB2.default.apiUrl+"/user/"+_CB2.default.appId+"/changePassword";if(_CB2.default._request("PUT",url,params).then(function(response){callback?callback.success(_CB2.default.fromJSON(JSON.parse(response),thisObj)):def.resolve(_CB2.default.fromJSON(JSON.parse(response),thisObj))},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise},_CB2.default.CloudUser.prototype.logIn=function(callback){if(_CB2.default._isNode&&!_CB2.default._isNative)throw"Error : You cannot login the user on the server.";if(!this.document.username)throw"Username is not set.";if(!this.document.password)throw"Password is not set.";var def,thisObj=this;callback||(def=new _CB2.default.Promise);var params=JSON.stringify({document:_CB2.default.toJSON(thisObj),key:_CB2.default.appKey}),url=_CB2.default.apiUrl+"/user/"+_CB2.default.appId+"/login";if(_CB2.default._request("POST",url,params).then(function(response){thisObj=_CB2.default.fromJSON(JSON.parse(response),thisObj),_CB2.default.CloudUser.current=thisObj,callback?callback.success(thisObj):def.resolve(thisObj),_CB2.default.CloudUser._setCurrentUser(thisObj),_CB2.default.CloudEvent.track("Login",{username:thisObj.username,email:thisObj.email})},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise},_CB2.default.CloudUser.authenticateWithProvider=function(dataJson,callback){if(_CB2.default._isNode&&!_CB2.default._isNative)throw"Error : You cannot login the user on the server.";var def;if(callback||(def=new _CB2.default.Promise),!dataJson)throw"data object is null.";if(dataJson&&!dataJson.provider)throw"provider is not set.";if(dataJson&&!dataJson.accessToken)throw"accessToken is not set.";if("twiter"===dataJson.provider.toLowerCase()&&!dataJson.accessSecret)throw"accessSecret is required for provider twitter.";var params=JSON.stringify({provider:dataJson.provider,accessToken:dataJson.accessToken,accessSecret:dataJson.accessSecret,key:_CB2.default.appKey}),url=_CB2.default.apiUrl+"/user/"+_CB2.default.appId+"/loginwithprovider";if(_CB2.default._request("POST",url,params).then(function(response){var user=response;if(response)try{user=new _CB2.default.CloudUser,_CB2.default.fromJSON(JSON.parse(response),user),_CB2.default.CloudUser.current=user,_CB2.default.CloudUser._setCurrentUser(user)}catch(e){}callback?callback.success(user):def.resolve(user)},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise},_CB2.default.CloudUser.prototype.logOut=function(callback){if(_CB2.default._isNode&&!_CB2.default._isNative)throw"Error : You cannot logOut the user on the server.";var def,thisObj=this;callback||(def=new _CB2.default.Promise);var params=JSON.stringify({document:_CB2.default.toJSON(thisObj),key:_CB2.default.appKey}),url=_CB2.default.apiUrl+"/user/"+_CB2.default.appId+"/logout";if(_CB2.default._request("POST",url,params).then(function(response){_CB2.default.fromJSON(JSON.parse(response),thisObj),_CB2.default.CloudUser.current=null,callback?callback.success(thisObj):def.resolve(thisObj),_CB2.default.CloudUser._removeCurrentUser()},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise},_CB2.default.CloudUser.prototype.addToRole=function(role,callback){if(!role)throw"Role is null";var def,thisObj=this;callback||(def=new _CB2.default.Promise);var params=JSON.stringify({user:_CB2.default.toJSON(thisObj),role:_CB2.default.toJSON(role),key:_CB2.default.appKey}),url=_CB2.default.apiUrl+"/user/"+_CB2.default.appId+"/addToRole";if(_CB2.default._request("PUT",url,params).then(function(response){_CB2.default.fromJSON(JSON.parse(response),thisObj),callback?callback.success(thisObj):def.resolve(thisObj)},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise},_CB2.default.CloudUser.prototype.isInRole=function(role){if(!role)throw"role is null";var roleArray=this.get("roles"),userRoleIds=[];if(roleArray&&roleArray.length>0)for(var i=0;i<roleArray.length;++i)userRoleIds.push(roleArray[i].document._id);return userRoleIds.indexOf(role.document._id)>=0},_CB2.default.CloudUser.prototype.removeFromRole=function(role,callback){if(!role)throw"Role is null";var def,thisObj=this;callback||(def=new _CB2.default.Promise);var params=JSON.stringify({user:_CB2.default.toJSON(thisObj),role:_CB2.default.toJSON(role),key:_CB2.default.appKey}),url=_CB2.default.apiUrl+"/user/"+_CB2.default.appId+"/removeFromRole";if(_CB2.default._request("PUT",url,params).then(function(response){_CB2.default.fromJSON(JSON.parse(response),thisObj),callback?callback.success(thisObj):def.resolve(thisObj)},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise},exports.default=_CB2.default.CloudUser},function(module,exports,__webpack_require__){"use strict";function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(exports,"__esModule",{value:!0});var _CB=__webpack_require__(1),_CB2=_interopRequireDefault(_CB);_CB2.default.CloudNotification=_CB2.default.CloudNotification||{},_CB2.default.CloudNotification.on=function(channelName,callback,done){if(_CB2.default._isRealtimeDisabled)throw"Realtime is disbaled for this app.";_CB2.default._validate();var def;if(done||(def=new _CB2.default.Promise),_CB2.default.Socket.emit("join-custom-channel",_CB2.default.appId+channelName),
_CB2.default.Socket.on(_CB2.default.appId+channelName,function(data){callback(data)}),done&&done.success?done.success():def.resolve(),!done)return def.promise},_CB2.default.CloudNotification.off=function(channelName,done){if(_CB2.default._isRealtimeDisabled)throw"Realtime is disbaled for this app.";_CB2.default._validate();var def;if(done||(def=new _CB2.default.Promise),_CB2.default.Socket.emit("leave-custom-channel",_CB2.default.appId+channelName),_CB2.default.Socket.removeAllListeners(_CB2.default.appId+channelName),done&&done.success?done.success():def.resolve(),!done)return def.promise},_CB2.default.CloudNotification.publish=function(channelName,data,done){if(_CB2.default._isRealtimeDisabled)throw"Realtime is disbaled for this app.";_CB2.default._validate();var def;if(done||(def=new _CB2.default.Promise),_CB2.default.Socket.emit("publish-custom-channel",{channel:_CB2.default.appId+channelName,data:data}),done&&done.success?done.success():def.resolve(),!done)return def.promise},exports.default=_CB2.default.CloudNotification},function(module,exports,__webpack_require__){"use strict";function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(exports,"__esModule",{value:!0});var _CB=__webpack_require__(1),_CB2=_interopRequireDefault(_CB);_CB2.default.CloudPush={},_CB2.default.CloudPush.send=function(data,query,callback){var tableName="Device";if(!_CB2.default.appId)throw"CB.appId is null.";var def;if(callback||(def=new _CB2.default.Promise),!data)throw"data object is null.";if(data&&!data.message)throw"message is not set.";if(query&&"[object Object]"==Object.prototype.toString.call(query)&&"function"!=typeof query.success)var pushQuery=query;if(query&&"[object Array]"==Object.prototype.toString.call(query)&&"function"!=typeof query.success){var pushQuery=new _CB2.default.CloudQuery(tableName);pushQuery.containedIn("channels",query)}if(query&&"[object String]"==Object.prototype.toString.call(query)&&"function"!=typeof query.success){var pushQuery=new _CB2.default.CloudQuery(tableName);pushQuery.containedIn("channels",[query])}if(query&&"[object Object]"==Object.prototype.toString.call(query)&&"function"==typeof query.success){callback=query;var pushQuery=new _CB2.default.CloudQuery(tableName)}if(!query)var pushQuery=new _CB2.default.CloudQuery(tableName);var params=JSON.stringify({query:pushQuery.query,sort:pushQuery.sort,limit:pushQuery.limit,skip:pushQuery.skip,key:_CB2.default.appKey,data:data}),url=_CB2.default.apiUrl+"/push/"+_CB2.default.appId+"/send";if(_CB2.default._request("POST",url,params).then(function(response){var object=response;_CB2.default._isJsonString(response)&&(object=JSON.parse(response)),callback?callback.success(object):def.resolve(object)},function(err){_CB2.default._isJsonString(err)&&(err=JSON.parse(err)),callback?callback.error(err):def.reject(err)}),!callback)return def.promise},_CB2.default.CloudPush.enableWebNotifications=function(callback){var def;if(callback||(def=new _CB2.default.Promise),"undefined"!=typeof document?_CB2.default.CloudPush._requestBrowserNotifications().then(function(response){if("serviceWorker"in navigator)return navigator.serviceWorker.register("serviceWorker.js",{scope:"./"});var noServerDef=new _CB2.default.Promise;return noServerDef.reject("Service workers aren't supported in this browser."),noServerDef}).then(function(registration){if(registration.showNotification)return _CB2.default.CloudPush._subscribe();var noServerDef=new _CB2.default.Promise;return noServerDef.reject("Notifications aren't supported on service workers."),noServerDef}).then(function(subscription){var browserKey=subscription.getKey?subscription.getKey("p256dh"):"";browserKey=browserKey?btoa(String.fromCharCode.apply(null,new Uint8Array(browserKey))):"";var authKey=subscription.getKey?subscription.getKey("auth"):"";authKey=authKey?btoa(String.fromCharCode.apply(null,new Uint8Array(authKey))):"",_CB2.default.CloudPush._addDevice(_CB2.default._getThisBrowserName(),subscription.endpoint,browserKey,authKey,{success:function(obj){callback?callback.success():def.resolve()},error:function(_error){callback?callback.error(_error):def.reject(_error)}})},function(error){callback?callback.error(error):def.reject(error)}):callback?callback.error("Browser document not found"):def.reject("Browser document not found"),!callback)return def.promise},_CB2.default.CloudPush.disableWebNotifications=function(callback){var def;if(callback||(def=new _CB2.default.Promise),"undefined"!=typeof document?_CB2.default.CloudPush._getSubscription().then(function(subscription){if(subscription||(callback?callback.success():def.resolve()),subscription){var promises=[];promises.push(subscription.unsubscribe()),promises.push(_CB2.default.CloudPush._deleteDevice(_CB2.default._getThisBrowserName(),subscription.endpoint)),_CB2.default.Promise.all(promises).then(function(successful){callback?callback.success():def.resolve()},function(error){callback?callback.error(error):def.reject(error)})}},function(error){callback?callback.error(error):def.reject(error)}):callback?callback.error("Browser document not found"):def.reject("Browser document not found"),!callback)return def.promise},_CB2.default.CloudPush._subscribe=function(){var def=new _CB2.default.Promise;return"PushManager"in window?(navigator.serviceWorker.ready.then(function(reg){reg.pushManager.getSubscription().then(function(subscription){subscription?def.resolve(subscription):reg.pushManager.subscribe({userVisibleOnly:!0}).then(function(subscription){def.resolve(subscription)}).catch(function(err){def.reject(err)})}).catch(function(err){def.reject(err)})},function(error){def.reject(error)}),def.promise):def.reject("Push messaging isn't supported.")},_CB2.default.CloudPush._getSubscription=function(){var def=new _CB2.default.Promise;return navigator.serviceWorker.ready.then(function(reg){reg.pushManager.getSubscription().then(function(subscription){subscription?def.resolve(subscription):def.resolve(null)}).catch(function(err){def.reject(err)})},function(error){def.reject(error)}),def.promise},_CB2.default.CloudPush._requestBrowserNotifications=function(){var def=new _CB2.default.Promise;return"Notification"in window?"granted"===Notification.permission?def.resolve("Permission granted"):"denied"!==Notification.permission&&Notification.requestPermission(function(permission){"granted"===permission&&def.resolve("Permission granted"),"denied"===permission&&def.reject("Permission denied")}):def.reject("This browser does not support system notifications"),def.promise},_CB2.default.CloudPush._addDevice=function(deviceOS,endPoint,browserKey,authKey,callback){var def;_CB2.default._validate();var thisObj=new _CB2.default.CloudObject("Device");thisObj.set("deviceOS",deviceOS),thisObj.set("deviceToken",endPoint),thisObj.set("metadata",{browserKey:browserKey,authKey:authKey}),callback||(def=new _CB2.default.Promise);var params=JSON.stringify({document:_CB2.default.toJSON(thisObj),key:_CB2.default.appKey}),url=_CB2.default.apiUrl+"/push/"+_CB2.default.appId;if(_CB2.default._request("PUT",url,params).then(function(response){thisObj=_CB2.default.fromJSON(JSON.parse(response),thisObj),callback?callback.success(thisObj):def.resolve(thisObj)},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise},_CB2.default.CloudPush._deleteDevice=function(deviceOS,endPoint,callback){if(!_CB2.default.appId)throw"CB.appId is null.";var def;callback||(def=new _CB2.default.Promise);var data={deviceOS:deviceOS,deviceToken:endPoint},params=JSON.stringify({key:_CB2.default.appKey,document:data,method:"DELETE"}),url=_CB2.default.apiUrl+"/push/"+_CB2.default.appId;if(_CB2.default._request("PUT",url,params).then(function(response){callback?callback.success(response):def.resolve(response)},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise},exports.default=_CB2.default.CloudPush},function(module,exports,__webpack_require__){"use strict";function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(exports,"__esModule",{value:!0});var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(obj){return typeof obj}:function(obj){return obj&&"function"==typeof Symbol&&obj.constructor===Symbol&&obj!==Symbol.prototype?"symbol":typeof obj},_createClass=function(){function defineProperties(target,props){for(var i=0;i<props.length;i++){var descriptor=props[i];descriptor.enumerable=descriptor.enumerable||!1,descriptor.configurable=!0,"value"in descriptor&&(descriptor.writable=!0),Object.defineProperty(target,descriptor.key,descriptor)}}return function(Constructor,protoProps,staticProps){return protoProps&&defineProperties(Constructor.prototype,protoProps),staticProps&&defineProperties(Constructor,staticProps),Constructor}}(),_CB=__webpack_require__(1),_CB2=_interopRequireDefault(_CB),_localforage=__webpack_require__(73),_localforage2=_interopRequireDefault(_localforage),CloudQuery=function(){function CloudQuery(tableName){if(_classCallCheck(this,CloudQuery),!tableName)throw"Table Name cannot be null";this.tableName=tableName,this.query={},this.query.$include=[],this.query.$includeList=[],this.select={},this.sort={},this.skip=0,this.limit=10}return _createClass(CloudQuery,[{key:"search",value:function(_search,language,caseSensitive,diacriticSensitive){if("string"!=typeof _search)throw"First parameter is required and it should be a string.";if(null!==language&&"undefined"!=typeof language&&"string"!=typeof language)throw"Second parameter should be a string.";if(null!==caseSensitive&&"undefined"!=typeof caseSensitive&&"boolean"!=typeof caseSensitive)throw"Third parameter should be a boolean.";if(null!==diacriticSensitive&&"undefined"!=typeof diacriticSensitive&&"boolean"!=typeof diacriticSensitive)throw"Fourth parameter should be a boolean.";return this.query.$text={},"string"==typeof _search&&(this.query.$text.$search=_search),null!==language&&"undefined"!=typeof language&&"string"==typeof language&&(this.query.$text.$language=language),null!==caseSensitive&&"undefined"!=typeof caseSensitive&&"boolean"==typeof caseSensitive&&(this.query.$text.$caseSensitive=caseSensitive),null!==diacriticSensitive&&"undefined"!=typeof diacriticSensitive&&"boolean"==typeof diacriticSensitive&&(this.query.$text.$diacriticSensitive=diacriticSensitive),this}},{key:"equalTo",value:function(columnName,data){return"id"===columnName&&(columnName="_"+columnName),data&&data.constructor===_CB2.default.CloudObject&&(columnName+="._id",data=data.get("id")),this.query[columnName]=data,this}},{key:"delete",value:function(callback){var def;callback||(def=new _CB2.default.Promise),this.find({success:function(obj){_CB2.default.CloudObject.deleteAll(obj,callback)},error:function(err){callback?callback.error(err):def.reject(err)}})}},{key:"includeList",value:function(columnName){return"id"===columnName&&(columnName="_"+columnName),this.query.$includeList.push(columnName),this}},{key:"include",value:function(columnName){return"id"===columnName&&(columnName="_"+columnName),this.query.$include.push(columnName),this}},{key:"all",value:function(columnName){return"id"===columnName&&(columnName="_"+columnName),this.query.$all=columnName,this}},{key:"any",value:function(columnName){return"id"===columnName&&(columnName="_"+columnName),this.query.$any=columnName,this}},{key:"first",value:function(columnName){return"id"===columnName&&(columnName="_"+columnName),this.query.$first=columnName,this}},{key:"notEqualTo",value:function(columnName,data){return"id"===columnName&&(columnName="_"+columnName),null!==data&&data.constructor===_CB2.default.CloudObject&&(columnName+="._id",data=data.get("id")),this.query[columnName]={$ne:data},this}},{key:"greaterThan",value:function(columnName,data){return"id"===columnName&&(columnName="_"+columnName),this.query[columnName]||(this.query[columnName]={}),this.query[columnName].$gt=data,this}},{key:"greaterThanEqualTo",value:function(columnName,data){return"id"===columnName&&(columnName="_"+columnName),this.query[columnName]||(this.query[columnName]={}),this.query[columnName].$gte=data,this}},{key:"lessThan",value:function(columnName,data){return"id"===columnName&&(columnName="_"+columnName),this.query[columnName]||(this.query[columnName]={}),this.query[columnName].$lt=data,this}},{key:"lessThanEqualTo",value:function(columnName,data){return"id"===columnName&&(columnName="_"+columnName),this.query[columnName]||(this.query[columnName]={}),this.query[columnName].$lte=data,this}},{key:"orderByAsc",value:function(columnName){return"id"===columnName&&(columnName="_"+columnName),this.sort[columnName]=1,this}},{key:"orderByDesc",value:function(columnName){return"id"===columnName&&(columnName="_"+columnName),this.sort[columnName]=-1,this}},{key:"setLimit",value:function(data){return this.limit=data,this}},{key:"setSkip",value:function(data){return this.skip=data,this}},{key:"paginate",value:function(pageNo,totalItemsInPage,callback){if(!_CB2.default.appId)throw"CB.appId is null.";if(!this.tableName)throw"TableName is null.";var def,callback;if("object"===("undefined"==typeof callback?"undefined":_typeof(callback))&&"function"==typeof callback.success&&(callback=callback),callback||(def=new _CB2.default.Promise),pageNo&&"object"===("undefined"==typeof pageNo?"undefined":_typeof(pageNo))&&"function"==typeof pageNo.success&&(callback=pageNo,pageNo=null),totalItemsInPage&&"object"===("undefined"==typeof totalItemsInPage?"undefined":_typeof(totalItemsInPage))&&"function"==typeof totalItemsInPage.success&&(callback=totalItemsInPage,totalItemsInPage=null),pageNo&&"number"==typeof pageNo&&pageNo>0&&"number"==typeof totalItemsInPage&&totalItemsInPage>0){var skip=pageNo*totalItemsInPage-totalItemsInPage;this.setSkip(skip),this.setLimit(totalItemsInPage)}totalItemsInPage&&"number"==typeof totalItemsInPage&&totalItemsInPage>0&&this.setLimit(totalItemsInPage);var thisObj=this,promises=[];promises.push(this.find());var countQuery=Object.create(this);if(countQuery.setSkip(0),countQuery.setLimit(99999999),promises.push(countQuery.count()),_CB2.default.Promise.all(promises).then(function(list){var objectsList=null,count=null,totalPages=0;list&&list.length>0&&(objectsList=list[0],count=list[1],count?totalPages=Math.ceil(count/thisObj.limit):(count=0,totalPages=0),totalPages&&totalPages<0&&(totalPages=0)),callback?callback.success(objectsList,count,totalPages):def.resolve(objectsList,count,totalPages)},function(error){callback?callback.error(error):def.reject(error)}),!callback)return def.promise}},{key:"selectColumn",value:function(columnNames){if(0===Object.keys(this.select).length&&(this.select={_id:1,createdAt:1,updatedAt:1,ACL:1,_type:1,_tableName:1}),"[object Object]"===Object.prototype.toString.call(columnNames))this.select=columnNames;else if("[object Array]"===Object.prototype.toString.call(columnNames))for(var i=0;i<columnNames.length;i++)this.select[columnNames[i]]=1;else this.select[columnNames]=1;return this}},{key:"doNotSelectColumn",value:function(columnNames){if("[object Object]"===Object.prototype.toString.call(columnNames))this.select=columnNames;else if("[object Array]"===Object.prototype.toString.call(columnNames))for(var i=0;i<columnNames.length;i++)this.select[columnNames[i]]=0;else this.select[columnNames]=0;return this}},{key:"containedIn",value:function(columnName,data){var isCloudObject=!1,CbData=[];if("id"===columnName&&(columnName="_"+columnName),"[object Object]"===Object.prototype.toString.call(data)&&!data instanceof _CB2.default.CloudObject)throw"Array / value / CloudObject expected as an argument";if("[object Array]"===Object.prototype.toString.call(data)){for(var i=0;i<data.length;i++)if(data[i]instanceof _CB2.default.CloudObject){if(isCloudObject=!0,!data[i].id)throw"CloudObject passed should be saved and should have an id before being passed to containedIn";CbData.push(data[i].id)}0===CbData.length&&(CbData=data),isCloudObject&&(columnName+="._id"),this.query[columnName]||(this.query[columnName]={}),this.query[columnName].$in=CbData;var thisObj=this;"undefined"!=typeof this.query[columnName].$nin&&CbData.forEach(function(val){(index=thisObj.query[columnName].$nin.indexOf(val))>=0&&thisObj.query[columnName].$nin.splice(index,1)})}else{if(data instanceof _CB2.default.CloudObject){if(!data.id)throw"CloudObject passed should be saved and should have an id before being passed to containedIn";columnName+="._id",CbData=data.id}else CbData=data;this.query[columnName]||(this.query[columnName]={}),this.query[columnName].$in||(this.query[columnName].$in=[]),this.query[columnName].$in.indexOf(CbData)===-1&&this.query[columnName].$in.push(CbData),"undefined"!=typeof this.query[columnName].$nin&&(index=this.query[columnName].$nin.indexOf(CbData))>=0&&this.query[columnName].$nin.splice(index,1)}return this}},{key:"notContainedIn",value:function(columnName,data){var isCloudObject=!1,CbData=[];if("id"===columnName&&(columnName="_"+columnName),"[object Object]"===Object.prototype.toString.call(data)&&!data instanceof _CB2.default.CloudObject)throw"Array or string expected as an argument";if("[object Array]"===Object.prototype.toString.call(data)){for(var i=0;i<data.length;i++)if(data[i]instanceof _CB2.default.CloudObject){if(isCloudObject=!0,!data[i].id)throw"CloudObject passed should be saved and should have an id before being passed to notContainedIn";CbData.push(data[i].id)}0===CbData.length&&(CbData=data),isCloudObject&&(columnName+="._id"),this.query[columnName]||(this.query[columnName]={}),this.query[columnName].$nin=CbData,"undefined"!=typeof this.query[columnName].$in&&(thisObj=this,CbData.forEach(function(val){(index=thisObj.query[columnName].$in.indexOf(val))>=0&&thisObj.query[columnName].$in.splice(index,1)}))}else{if(data instanceof _CB2.default.CloudObject){if(!data.id)throw"CloudObject passed should be saved and should have an id before being passed to notContainedIn";columnName+="._id",CbData=data.id}else CbData=data;this.query[columnName]||(this.query[columnName]={}),this.query[columnName].$nin||(this.query[columnName].$nin=[]),this.query[columnName].$nin.indexOf(CbData)===-1&&this.query[columnName].$nin.push(CbData),"undefined"!=typeof this.query[columnName].$in&&(index=this.query[columnName].$in.indexOf(CbData))>=0&&this.query[columnName].$in.splice(index,1)}return this}},{key:"exists",value:function(columnName){return"id"===columnName&&(columnName="_"+columnName),this.query[columnName]||(this.query[columnName]={}),this.query[columnName].$exists=!0,this}},{key:"doesNotExists",value:function(columnName){return"id"===columnName&&(columnName="_"+columnName),this.query[columnName]||(this.query[columnName]={}),this.query[columnName].$exists=!1,this}},{key:"containsAll",value:function(columnName,data){var isCloudObject=!1,CbData=[];if("id"===columnName&&(columnName="_"+columnName),"[object Object]"===Object.prototype.toString.call(data)&&!data instanceof _CB2.default.CloudObject)throw"Array or string expected as an argument";if("[object Array]"===Object.prototype.toString.call(data)){for(var i=0;i<data.length;i++)if(data[i]instanceof _CB2.default.CloudObject){if(isCloudObject=!0,!data[i].id)throw"CloudObject passed should be saved and should have an id before being passed to containsAll";CbData.push(data[i].id)}0===CbData.length&&(CbData=data),isCloudObject&&(columnName+="._id"),this.query[columnName]||(this.query[columnName]={}),this.query[columnName].$all=CbData}else{if(data instanceof _CB2.default.CloudObject){if(!data.id)throw"CloudObject passed should be saved and should have an id before being passed to containsAll";columnName+="._id",CbData=data.id}else CbData=data;this.query[columnName]||(this.query[columnName]={}),this.query[columnName].$all||(this.query[columnName].$all=[]),this.query[columnName].$all.indexOf(CbData)===-1&&this.query[columnName].$all.push(CbData)}return this}},{key:"startsWith",value:function(columnName,value){"id"===columnName&&(columnName="_"+columnName);var regex="^"+value;return this.query[columnName]||(this.query[columnName]={}),this.query[columnName].$regex=regex,this.query[columnName].$options="im",this}},{key:"regex",value:function(columnName,value,isCaseInsensitive){return"id"===columnName&&(columnName="_"+columnName),this.query[columnName]||(this.query[columnName]={}),this.query[columnName].$regex=value,isCaseInsensitive&&(this.query[columnName].$options="i"),this}},{key:"substring",value:function(columnName,value,isCaseInsensitive){"string"==typeof columnName&&(columnName=[columnName]);for(var j=0;j<columnName.length;j++)if("[object Array]"===Object.prototype.toString.call(value)&&value.length>0){this.query.$or||(this.query.$or=[]);for(var i=0;i<value.length;i++){var obj={};obj[columnName[j]]={},obj[columnName[j]].$regex=".*"+value[i]+".*",isCaseInsensitive&&(obj[columnName[j]].$options="i"),this.query.$or.push(obj)}}else if(1===columnName.length)this.regex(columnName[j],".*"+value+".*",isCaseInsensitive);else{this.query.$or||(this.query.$or=[]);var obj={};obj[columnName[j]]={},obj[columnName[j]].$regex=".*"+value+".*",isCaseInsensitive&&(obj[columnName[j]].$options="i"),this.query.$or.push(obj)}return this}},{key:"near",value:function(columnName,geoPoint,maxDistance,minDistance){this.query[columnName]||(this.query[columnName]={},this.query[columnName].$near={$geometry:{coordinates:geoPoint.document.coordinates,type:"Point"},$maxDistance:maxDistance,$minDistance:minDistance})}},{key:"geoWithin",value:function(columnName,geoPoint,radius){if(radius)this.query[columnName]||(this.query[columnName]={},this.query[columnName].$geoWithin={$centerSphere:[geoPoint.document.coordinates,radius/3963.2]});else{var coordinates=[];if("[object Array]"!==Object.prototype.toString.call(geoPoint))throw"Invalid Parameter, coordinates should be an array of CloudGeoPoint Object";for(var i=0;i<geoPoint.length;i++)geoPoint[i].document.hasOwnProperty("coordinates")&&(coordinates[i]=geoPoint[i].document.coordinates);coordinates[coordinates.length]=coordinates[0];var type="Polygon";this.query[columnName]||(this.query[columnName]={},this.query[columnName].$geoWithin={},this.query[columnName].$geoWithin.$geometry={type:type,coordinates:[coordinates]})}}},{key:"count",value:function(callback){if(!_CB2.default.appId)throw"CB.appId is null.";if(!this.tableName)throw"TableName is null.";var def;callback||(def=new _CB2.default.Promise);var thisObj=this,params=JSON.stringify({query:thisObj.query,limit:thisObj.limit,skip:thisObj.skip,key:_CB2.default.appKey}),url=_CB2.default.apiUrl+"/data/"+_CB2.default.appId+"/"+thisObj.tableName+"/count";if(_CB2.default._request("POST",url,params).then(function(response){response=parseInt(response),callback?callback.success(response):def.resolve(response)},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise}},{key:"distinct",value:function(keys,callback){if("id"===keys&&(keys="_id"),!_CB2.default.appId)throw"CB.appId is null.";if(!this.tableName)throw"TableName is null.";if("[object Array]"!==Object.prototype.toString.call(keys)&&keys.length<=0)throw"keys should be array";var def;callback||(def=new _CB2.default.Promise);var thisObj=this,params=JSON.stringify({onKey:keys,query:thisObj.query,select:thisObj.select,sort:thisObj.sort,limit:thisObj.limit,skip:thisObj.skip,key:_CB2.default.appKey}),url=_CB2.default.apiUrl+"/data/"+_CB2.default.appId+"/"+thisObj.tableName+"/distinct";if(_CB2.default._request("POST",url,params).then(function(response){var object=_CB2.default.fromJSON(JSON.parse(response));callback?callback.success(object):def.resolve(object)},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise}},{key:"find",value:function(callback){if(!_CB2.default.appId)throw"CB.appId is null.";if(!this.tableName)throw"TableName is null.";var def;callback||(def=new _CB2.default.Promise);var thisObj=this,params=JSON.stringify({query:thisObj.query,select:thisObj.select,sort:thisObj.sort,limit:thisObj.limit,skip:thisObj.skip,key:_CB2.default.appKey}),url=_CB2.default.apiUrl+"/data/"+_CB2.default.appId+"/"+thisObj.tableName+"/find";if(_CB2.default._request("POST",url,params).then(function(response){var object=_CB2.default.fromJSON(JSON.parse(response));callback?callback.success(object):def.resolve(object)},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise}},{key:"findFromLocalStore",value:function(callback){var thisObj=this;if(!thisObj.tableName)throw"TableName is null.";_CB2.default._validate();var def;if(callback||(def=new _CB2.default.Promise),_localforage2.default.getItem(_CB2.default.appId+"-"+thisObj.tableName).then(function(documents){var cloudObjects=[],cloudObject=null;documents&&documents.forEach(function(document){cloudObject=_CB2.default.fromJSON(document),CloudQuery._validateQuery(cloudObject,thisObj.query)&&cloudObjects.push(cloudObject)}),callback?callback.success(cloudObjects):def.resolve(cloudObjects)}).catch(function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise}},{key:"get",value:function(objectId,callback){var query=new _CB2.default.CloudQuery(this.tableName);return query.findById(objectId,callback)}},{key:"findById",value:function(objectId,callback){var thisObj=this;if(!_CB2.default.appId)throw"CB.appId is null.";if(!this.tableName)throw"TableName is null.";var def;if(callback||(def=new _CB2.default.Promise),thisObj.skip&&0!==!thisObj.skip)throw"You cannot use skip and find object by Id in the same query";if(thisObj.limit&&0===thisObj.limit)throw"You cannot use limit and find object by Id in the same query";if(thisObj.sort&&Object.getOwnPropertyNames(thisObj.sort).length>0)throw"You cannot use sort and find object by Id in the same query";thisObj.equalTo("id",objectId);var params=JSON.stringify({query:thisObj.query,select:thisObj.select,key:_CB2.default.appKey,limit:1,skip:0,sort:{}}),url=_CB2.default.apiUrl+"/data/"+_CB2.default.appId+"/"+thisObj.tableName+"/find";if(_CB2.default._request("POST",url,params).then(function(response){response=JSON.parse(response),"[object Array]"===Object.prototype.toString.call(response)&&(response=response[0]),callback?callback.success(_CB2.default.fromJSON(response)):def.resolve(_CB2.default.fromJSON(response))},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise}},{key:"findOne",value:function(callback){if(!_CB2.default.appId)throw"CB.appId is null.";if(!this.tableName)throw"TableName is null.";var def;callback||(def=new _CB2.default.Promise);var params=JSON.stringify({query:this.query,select:this.select,sort:this.sort,skip:this.skip,key:_CB2.default.appKey}),url=_CB2.default.apiUrl+"/data/"+_CB2.default.appId+"/"+this.tableName+"/findOne";if(_CB2.default._request("POST",url,params).then(function(response){var object=_CB2.default.fromJSON(JSON.parse(response));callback?callback.success(object):def.resolve(object)},function(err){callback?callback.error(err):def.reject(err)}),!callback)return def.promise}}]),CloudQuery}();CloudQuery.or=function(obj1,obj2){var tableName,queryArray=[];if("[object Array]"===Object.prototype.toString.call(obj1)){tableName=obj1[0].tableName;for(var i=0;i<obj1.length;++i){if(obj1[i].tableName!=tableName)throw"Table names are not same";if(!obj1[i]instanceof _CB2.default.CloudQuery)throw"Array items are not instanceof of CloudQuery";queryArray.push(obj1[i].query)}}if("undefined"!=typeof obj2&&"undefined"!=typeof obj1&&"[object Array]"!==Object.prototype.toString.call(obj1)){if("[object Array]"===Object.prototype.toString.call(obj2))throw"First and second parameter should be an instance of CloudQuery object";if(!obj1.tableName===obj2.tableName)throw"Table names are not same";if(!obj1 instanceof _CB2.default.CloudQuery)throw"Data passed is not an instance of CloudQuery";if(!obj2 instanceof _CB2.default.CloudQuery)throw"Data passed is not an instance of CloudQuery";tableName=obj1.tableName,queryArray.push(obj1.query),queryArray.push(obj2.query)}if("undefined"==typeof tableName)throw"Invalid operation";var obj=new _CB2.default.CloudQuery(tableName);return obj.query.$or=queryArray,obj},CloudQuery._validateQuery=function(cloudObject,query){for(var key in query)if(query[key]){var value=query[key];if("object"===("undefined"==typeof value?"undefined":_typeof(value)))if("$or"===key){if(query[key].length>0){for(var isTrue=!1,i=0;i<query[key].length;i++)if(_CB2.default.CloudQuery._validateQuery(cloudObject,query[key][i])){isTrue=!0;break}if(!isTrue)return!1}}else for(var objectKeys in value){if("$ne"===objectKeys&&cloudObject.get(key)===query[key].$ne)return!1;if("$gt"===objectKeys&&cloudObject.get(key)<=query[key].$gt)return!1;if("$lt"===objectKeys&&cloudObject.get(key)>=query[key].$lt)return!1;if("$gte"===objectKeys&&cloudObject.get(key)<query[key].$gte)return!1;if("$lte"===objectKeys&&cloudObject.get(key)>query[key].$lte)return!1;if("$exists"===objectKeys)if(query[key][objectKeys]&&cloudObject.get(key));else if(query[key][objectKeys]!==!1)return!1;if("$exists"===objectKeys&&!query[key][objectKeys]&&cloudObject.get(key))return!1;if("$regex"===objectKeys){var reg=new RegExp(query[key][objectKeys]);if(query[key].$options){if("im"===query[key].$options){var value=trimStart("^",query[key][objectKeys]);if(0!==cloudObject.get(key).indexOf(value))return!1}}else if(!reg.test(cloudObject.get(key)))return!1}if("$in"===objectKeys&&query[key][objectKeys]){var arr=query[key][objectKeys],value=null;if(value=key.indexOf(".")>-1?cloudObject.get(key.substr(0,key.indexOf("."))):cloudObject.get(key),"[object Array]"===Object.prototype.toString.call(value)){for(var exists=!1,i=0;i<value.length;i++)if(value[i]instanceof _CB2.default.CloudObject){if(arr.indexOf(value[i].id)>-1){exists=!0;break}}else if(arr.indexOf(value[i])>-1){exists=!0;break}if(!exists)return!1}else if(arr.indexOf(value)===-1)return!1}if("$nin"===objectKeys&&query[key][objectKeys]){var arr=query[key][objectKeys],value=null;if(value=key.indexOf(".")>-1?cloudObject.get(key.substr(0,key.indexOf("."))):cloudObject.get(key),"[object Array]"===Object.prototype.toString.call(value)){for(var exists=!1,i=0;i<value.length;i++)if(value[i]instanceof _CB2.default.CloudObject){if(arr.indexOf(value[i].id)!==-1){exists=!0;break}}else if(arr.indexOf(value[i])!==-1){exists=!0;break}if(exists)return!1}else if(arr.indexOf(value)!==-1)return!1}if("$all"===objectKeys&&query[key][objectKeys]){var arr=query[key][objectKeys],value=null;if(value=key.indexOf(".")>-1?cloudObject.get(key.substr(0,key.indexOf("."))):cloudObject.get(key),"[object Array]"===Object.prototype.toString.call(value)){for(var i=0;i<value.length;i++)if(value[i]instanceof _CB2.default.CloudObject){if(arr.indexOf(value[i].id)===-1)return!1}else if(arr.indexOf(value[i])===-1)return!1}else if(arr.indexOf(value)===-1)return!1}}else if(key.indexOf(".")!==-1){var temp=key.substring(0,key.indexOf("."));if(!cloudObject.get(temp))return!1;if(cloudObject.get(temp).id!==query[key])return!1}else if(cloudObject.get(key)!==query[key])return!1}return!0},_CB2.default.CloudQuery=CloudQuery,exports.default=_CB2.default.CloudQuery}]))});