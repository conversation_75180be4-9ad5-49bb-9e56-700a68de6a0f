{"name": "ezybackend-account-ui", "version": "0.0.0", "description": "", "main": "webpack.config.js", "scripts": {"start": "node server.js", "client": "webpack --progress --watch", "build": "webpack --progress"}, "author": "", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"axios": "^0.14.0", "babel-core": "^6.17.0", "babel-loader": "^6.2.5", "babel-preset-es2015": "^6.22.0", "babel-preset-react": "^6.22.0", "card-validator": "^4.1.1", "ejs": "^2.5.5", "express": "^4.14.0", "history": "^4.3.0", "lodash": "^4.17.4", "material-ui": "^0.16.0", "prop-types": "^15.6.0", "react": "~15.2.1", "react-addons-css-transition-group": "^15.4.2", "react-cookie": "^0.4.8", "react-dom": "~15.2.1", "react-router": "^2.8.1", "react-tap-event-plugin": "^1.0.0", "webpack": "~1.13.1", "webpack-dev-server": "~1.14.1"}, "devDependencies": {"babel-cli": "^6.22.2", "babel-preset-env": "^1.1.8", "babel-preset-react": "^6.22.0", "babel-register": "^6.22.0"}}