# 🚀 EzyBackend Deployment Guide

This guide covers various deployment options for EzyBackend in production environments.

## 🐳 Docker Deployment (Recommended)

### **Quick Start with Docker Compose**

1. **Clone the Repository**
   ```bash
   git clone https://github.com/Jitenderkumar2030/ezybackend.git
   cd ezybackend
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start Services**
   ```bash
   docker-compose up -d
   ```

4. **Verify Deployment**
   ```bash
   # Check service health
   curl http://localhost:4730/health  # Data Service
   curl http://localhost:3000/health  # User Service
   curl http://localhost:1440         # Dashboard
   ```

### **Production Docker Compose**

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  mongodb:
    image: mongo:7.0
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
    volumes:
      - mongodb_data:/data/db
    networks:
      - ezybackend

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - ezybackend

  data-service:
    image: ezybackend/data-service:latest
    restart: unless-stopped
    ports:
      - "4730:4730"
    environment:
      - NODE_ENV=production
      - MONGODB_URL=mongodb://${MONGO_USERNAME}:${MONGO_PASSWORD}@mongodb:27017/ezybackend
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
    depends_on:
      - mongodb
      - redis
    networks:
      - ezybackend

  user-service:
    image: ezybackend/user-service:latest
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URL=mongodb://${MONGO_USERNAME}:${MONGO_PASSWORD}@mongodb:27017/ezybackend
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
    depends_on:
      - mongodb
      - redis
    networks:
      - ezybackend

  dashboard:
    image: ezybackend/dashboard-ui:latest
    restart: unless-stopped
    ports:
      - "1440:1440"
    environment:
      - NODE_ENV=production
      - API_URL=http://data-service:4730
    depends_on:
      - data-service
    networks:
      - ezybackend

  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - data-service
      - user-service
      - dashboard
    networks:
      - ezybackend

volumes:
  mongodb_data:
  redis_data:

networks:
  ezybackend:
    driver: bridge
```

### **Environment Configuration**

```bash
# .env
NODE_ENV=production

# Database
MONGO_USERNAME=ezybackend
MONGO_PASSWORD=your-secure-password
MONGODB_URL=******************************************************************

# Redis
REDIS_PASSWORD=your-redis-password
REDIS_URL=redis://:your-redis-password@redis:6379

# Security
JWT_SECRET=your-jwt-secret-key
ENCRYPTION_KEY=your-encryption-key

# OAuth (optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret

# Email (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Monitoring
ENABLE_METRICS=true
LOG_LEVEL=info
```

## ☁️ Cloud Deployment

### **AWS ECS**

1. **Build and Push Images**
   ```bash
   # Build images
   docker build -t ezybackend/data-service ./data-service
   docker build -t ezybackend/user-service ./user-service
   docker build -t ezybackend/dashboard-ui ./dashboard-ui

   # Tag for ECR
   docker tag ezybackend/data-service:latest 123456789.dkr.ecr.region.amazonaws.com/ezybackend/data-service:latest

   # Push to ECR
   docker push 123456789.dkr.ecr.region.amazonaws.com/ezybackend/data-service:latest
   ```

2. **ECS Task Definition**
   ```json
   {
     "family": "ezybackend-data-service",
     "networkMode": "awsvpc",
     "requiresCompatibilities": ["FARGATE"],
     "cpu": "512",
     "memory": "1024",
     "executionRoleArn": "arn:aws:iam::123456789:role/ecsTaskExecutionRole",
     "containerDefinitions": [
       {
         "name": "data-service",
         "image": "123456789.dkr.ecr.region.amazonaws.com/ezybackend/data-service:latest",
         "portMappings": [
           {
             "containerPort": 4730,
             "protocol": "tcp"
           }
         ],
         "environment": [
           {
             "name": "NODE_ENV",
             "value": "production"
           }
         ],
         "logConfiguration": {
           "logDriver": "awslogs",
           "options": {
             "awslogs-group": "/ecs/ezybackend",
             "awslogs-region": "us-east-1",
             "awslogs-stream-prefix": "ecs"
           }
         }
       }
     ]
   }
   ```

### **Google Cloud Run**

```bash
# Build and deploy
gcloud builds submit --tag gcr.io/PROJECT-ID/ezybackend-data-service ./data-service

gcloud run deploy ezybackend-data-service \
  --image gcr.io/PROJECT-ID/ezybackend-data-service \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --port 4730 \
  --memory 1Gi \
  --cpu 1 \
  --set-env-vars NODE_ENV=production
```

### **Azure Container Instances**

```bash
# Create resource group
az group create --name ezybackend-rg --location eastus

# Deploy container
az container create \
  --resource-group ezybackend-rg \
  --name ezybackend-data-service \
  --image ezybackend/data-service:latest \
  --ports 4730 \
  --environment-variables NODE_ENV=production \
  --memory 1 \
  --cpu 1
```

## 🔧 Manual Deployment

### **Prerequisites**
- Node.js 20+ (LTS)
- MongoDB 4.4+
- Redis 6.0+
- PM2 (for process management)

### **Installation Steps**

1. **Setup Application**
   ```bash
   git clone https://github.com/Jitenderkumar2030/ezybackend.git
   cd ezybackend

   # Install dependencies
   cd data-service && npm ci --production
   cd ../user-service && npm ci --production
   cd ../dashboard-ui && npm ci && npm run build
   ```

2. **Configure Services**
   ```bash
   # Create systemd services
   sudo cp scripts/systemd/*.service /etc/systemd/system/
   sudo systemctl daemon-reload
   sudo systemctl enable ezybackend-data-service
   sudo systemctl enable ezybackend-user-service
   ```

3. **Start Services**
   ```bash
   sudo systemctl start ezybackend-data-service
   sudo systemctl start ezybackend-user-service
   ```

### **PM2 Process Management**

```bash
# Install PM2
npm install -g pm2

# Start services with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save
pm2 startup
```

**ecosystem.config.js**:
```javascript
module.exports = {
  apps: [
    {
      name: 'ezybackend-data-service',
      script: './data-service/server.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 4730
      }
    },
    {
      name: 'ezybackend-user-service',
      script: './user-service/server.js',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      }
    }
  ]
};
```

## 🔒 Security Considerations

### **SSL/TLS Configuration**
```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    location /api/ {
        proxy_pass http://data-service:4730;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### **Firewall Rules**
```bash
# UFW configuration
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw deny 4730/tcp   # Block direct access to data service
sudo ufw deny 3000/tcp   # Block direct access to user service
sudo ufw enable
```

## 📊 Monitoring & Logging

### **Health Checks**
```bash
# Add to crontab for monitoring
*/5 * * * * curl -f http://localhost:4730/health || echo "Data service down" | mail -s "EzyBackend Alert" <EMAIL>
```

### **Log Aggregation**
```yaml
# docker-compose.logging.yml
version: '3.8'
services:
  data-service:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

## 🔄 Backup Strategy

### **Database Backup**
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
mongodump --uri="******************************************************" --out="/backups/mongodb_$DATE"
tar -czf "/backups/mongodb_$DATE.tar.gz" "/backups/mongodb_$DATE"
rm -rf "/backups/mongodb_$DATE"

# Keep only last 7 days
find /backups -name "mongodb_*.tar.gz" -mtime +7 -delete
```

### **Automated Backups**
```bash
# Add to crontab
0 2 * * * /path/to/backup.sh
```

## 🚀 Scaling

### **Horizontal Scaling**
```yaml
# docker-compose.scale.yml
services:
  data-service:
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
```

### **Load Balancing**
```nginx
upstream data_service {
    server data-service-1:4730;
    server data-service-2:4730;
    server data-service-3:4730;
}

server {
    location /api/ {
        proxy_pass http://data_service;
    }
}
```

---

**Need help with deployment?** Check our [troubleshooting guide](TROUBLESHOOTING.md) or open an issue on GitHub!
