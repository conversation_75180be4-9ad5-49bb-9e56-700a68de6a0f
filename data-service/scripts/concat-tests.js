#!/usr/bin/env node

/*
#     EzyBackend - Core Engine that powers Backend as a Service
#     (c) 2014 HackerBay, Inc. 
#     EzyBackend may be freely distributed under the Apache 2 License
#     
#     Modern replacement for grunt concat task
#     Concatenates test files for browser testing
*/

const fs = require('fs');
const path = require('path');

// Test files to concatenate (same order as original gruntfile)
const testFiles = [
  'test/config.js',
  'test/util/util.js',
  'test/requireEzyBackend.js',
  'test/init/init.js',
  'test/CloudTable/createTestTables.js',
  'test/misc/exportimportTable.js',
  'test/CloudFile/CloudFile.js',
  'test/CloudUser/userTest.js',
  'test/CloudEvent/test.js',
  'test/CloudCache/CloudCache.js',
  'test/CloudQueue/tests.js',
  'test/CloudObject/test.js',
  'test/CloudObject/bulkApi.js',
  'test/CloudObject/file.js',
  'test/CloudFile/FileACL.js',
  'test/CloudObject/expire.js',
  'test/CloudObject/notification.js',
  'test/CloudObject/notificationQueries.js',
  'test/CloudObject/encryption.js',
  'test/CloudExpire/test.js',
  'test/CloudQuery/includeList.js',
  'test/CloudQuery/queryTest.js',
  'test/CloudQuery/encryption.js',
  'test/CloudRole/role.js',
  'test/ACL/aclTest1.js',
  'test/ACL/queryAcl.js',
  'test/ACL/searchAcl.js',
  'test/CloudNotification/test.js',
  'test/ACL/masterKeyACL.js',
  'test/CloudGeoPoint/CloudGeoPoint.js',
  'test/CloudObject/versionTest.js',
  'test/CloudTable/test.js',
  'test/CloudTable/cloudtable.js',
  'test/CloudTable/acl.js',
  'test/CloudApp/connected.js',
  'test/CloudApp/acl.js',
  'test/AtomicityTests/atomicity.js',
  'test/CloudTable/deleteTestTables.js',
  'test/CloudApp/deleteApp.js',
  'test/DisabledRealtime/init.js',
  'test/DisabledRealtime/CloudNotificationTests.js',
  'test/DisabledRealtime/CloudObjectNotification.js',
  'test/DisabledRealtime/CloudObject.js'
];

const outputFile = 'test/test.js';

function concatTests() {
  console.log('🔧 Concatenating test files...');
  
  let concatenatedContent = '';
  let processedFiles = 0;
  
  // Add header comment
  concatenatedContent += `/*
 * EzyBackend Test Suite - Concatenated Test Files
 * Generated on: ${new Date().toISOString()}
 * 
 * This file is automatically generated by scripts/concat-tests.js
 * Do not edit this file directly - edit the individual test files instead
 */

`;

  testFiles.forEach((file, index) => {
    const filePath = path.join(__dirname, '..', file);
    
    try {
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Add file separator comment
        concatenatedContent += `\n/* ===== ${file} ===== */\n`;
        concatenatedContent += content;
        concatenatedContent += '\n';
        
        processedFiles++;
        console.log(`✅ Added: ${file}`);
      } else {
        console.warn(`⚠️  File not found: ${file}`);
      }
    } catch (error) {
      console.error(`❌ Error reading ${file}:`, error.message);
    }
  });
  
  // Write concatenated content to output file
  try {
    const outputPath = path.join(__dirname, '..', outputFile);
    fs.writeFileSync(outputPath, concatenatedContent, 'utf8');
    
    console.log(`\n🎉 Successfully concatenated ${processedFiles}/${testFiles.length} test files`);
    console.log(`📄 Output written to: ${outputFile}`);
    console.log(`📊 Total size: ${(concatenatedContent.length / 1024).toFixed(2)} KB`);
  } catch (error) {
    console.error('❌ Error writing concatenated file:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  concatTests();
}

module.exports = { concatTests };
