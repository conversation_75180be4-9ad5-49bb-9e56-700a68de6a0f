/*
#     EzyBackend Dashboard UI - Jest Configuration
#     (c) 2014 HackerBay, Inc. 
#     EzyBackend may be freely distributed under the Apache 2 License
#     
#     Modern Jest configuration for React 18+ testing
*/

module.exports = {
  testEnvironment: 'jsdom',
  
  setupFilesAfterEnv: ['<rootDir>/test/setup.js'],
  
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/app/$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
  },
  
  transform: {
    '^.+\\.(js|jsx)$': ['babel-jest', {
      presets: [
        ['@babel/preset-env', {
          targets: {
            node: 'current',
          },
        }],
        ['@babel/preset-react', {
          runtime: 'automatic',
        }],
      ],
      plugins: [
        '@babel/plugin-proposal-class-properties',
      ],
    }],
  },
  
  moduleFileExtensions: ['js', 'jsx', 'json'],
  
  testMatch: [
    '<rootDir>/test/**/*.test.{js,jsx}',
    '<rootDir>/app/**/*.test.{js,jsx}',
  ],
  
  collectCoverageFrom: [
    'app/**/*.{js,jsx}',
    '!app/**/*.test.{js,jsx}',
    '!app/index.js',
  ],
  
  coverageDirectory: 'coverage',
  
  coverageReporters: ['text', 'lcov', 'html'],
  
  testTimeout: 10000,
  
  verbose: true,
  
  clearMocks: true,
  
  restoreMocks: true,
};
