# EzyBackend Docker ignore file
# Optimized for production builds

# Version control
.git/
.gitignore

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Configuration files (use environment variables instead)
config/cloudboost.json
config/ezybackend.json

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage and test files
coverage/
.nyc_output/
test/
*.test.js
*.spec.js

# Build artifacts
build/
dist/
.grunt/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
*.suo
*.user
*.userosscache
*.sln.docstates
*.userprefs
*.njsproj
.vs/
.ntvs_analysis.dat

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Environment files
.env
.env.test
.env.local
.env.production

# Logs
logs/
*.log

# Temporary files
*.tmp
*.tmp_proj
tmp/
temp/

# Development tools
.husky/
.prettierrc
.eslintrc.js
webpack.config.js
scripts/

# Documentation
README.md
CHANGELOG.md
docs/

# Docker files
Dockerfile*
.dockerignore

# Credentials and secrets
credentials.tar.enc
*.pem
*.key
*.crt

# Database dumps
*.rdb
dump.rdb

# Legacy Visual Studio files
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
bld/
[Bb]in/
[Oo]bj/
project.lock.json
artifacts/
*.sln
*_i.c
*_p.c
*_i.h
*.ilk
*.meta
*.obj
*.pch
*.pdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc
*.doubt
DataServices.njsproj
DataServices.v12.suo
DataServices.v14.suo

# Uploads directory
uploads/
