#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# EzyBackend User Service pre-commit hook
# Run linting and formatting checks before commit

echo "🔍 Running pre-commit checks for User Service..."

# Run ESLint
echo "📋 Checking code style with ESLint..."
npm run lint-check

# Run Prettier check
echo "🎨 Checking code formatting with Prettier..."
npm run format-check

echo "✅ User Service pre-commit checks passed!"
