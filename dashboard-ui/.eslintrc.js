/*
#     EzyBackend Dashboard UI - ESLint Configuration
#     (c) 2014 HackerBay, Inc. 
#     EzyBackend may be freely distributed under the Apache 2 License
#     
#     Modern ESLint configuration for React 18+
*/

module.exports = {
  env: {
    browser: true,
    es2022: true,
    node: true,
    jest: true,
  },
  extends: [
    'eslint:recommended',
    'airbnb',
    'airbnb/hooks',
  ],
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
  },
  settings: {
    react: {
      version: 'detect',
    },
    'import/resolver': {
      webpack: {
        config: './webpack.config.js',
      },
    },
  },
  rules: {
    // React specific rules
    'react/react-in-jsx-scope': 'off', // Not needed with React 17+
    'react/jsx-uses-react': 'off',
    'react/prop-types': 'warn',
    'react/jsx-props-no-spreading': 'warn',
    'react/state-in-constructor': 'off',
    'react/jsx-filename-extension': ['warn', { extensions: ['.js', '.jsx'] }],
    
    // Legacy compatibility rules
    'no-console': 'warn',
    'no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    'consistent-return': 'warn',
    'no-param-reassign': 'warn',
    'no-underscore-dangle': 'off',
    'func-names': 'off',
    'prefer-arrow-callback': 'warn',
    'object-shorthand': 'warn',
    'prefer-const': 'warn',
    'no-var': 'error',
    'prefer-template': 'warn',
    
    // Import rules
    'import/no-extraneous-dependencies': ['error', {
      devDependencies: [
        'test/**',
        '**/*.test.{js,jsx}',
        '**/*.spec.{js,jsx}',
        'webpack.config.js',
        'jest.config.js',
      ],
    }],
    'import/prefer-default-export': 'off',
    
    // Style preferences
    'max-len': ['warn', { 
      code: 120, 
      ignoreUrls: true,
      ignoreRegExpLiterals: true,
      ignoreStrings: true,
      ignoreTemplateLiterals: true
    }],
    'comma-dangle': ['error', 'always-multiline'],
    'quotes': ['error', 'single', { allowTemplateLiterals: true }],
    'semi': ['error', 'always'],
    'indent': ['error', 2],
    
    // Relaxed rules for legacy codebase migration
    'no-use-before-define': 'warn',
    'no-nested-ternary': 'warn',
    'no-lonely-if': 'warn',
    'class-methods-use-this': 'warn',
    'jsx-a11y/click-events-have-key-events': 'warn',
    'jsx-a11y/no-static-element-interactions': 'warn',
  },
  overrides: [
    {
      files: ['test/**/*.{js,jsx}', '**/*.test.{js,jsx}'],
      env: {
        jest: true,
      },
      rules: {
        'import/no-extraneous-dependencies': 'off',
        'no-unused-expressions': 'off',
        'react/jsx-props-no-spreading': 'off',
      },
    },
    {
      files: ['webpack.config.js', 'jest.config.js'],
      env: {
        node: true,
      },
      rules: {
        'import/no-extraneous-dependencies': 'off',
      },
    },
  ],
  ignorePatterns: [
    'node_modules/',
    'public/',
    'dist/',
    'coverage/',
  ],
};
