<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="cleartype" content="on" />
  <meta name="apple-mobile-web-app-capable" content="no">
  <meta name="viewport" content="width=device-width,minimum-scale=1.0,initial-scale=1,user-scalable=yes">
  <title>EzyBackend Dashboard</title>
  <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500" rel="stylesheet">
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" integrity="sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u" crossorigin="anonymous">
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
  <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
  <!-- font "Signika" -->
  <link rel="stylesheet" type="text/css" href="//fonts.googleapis.com/css?family=Signika" />
  <link rel="shortcut icon" href="public/assets/images/cblogo.png">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/messenger/1.5.0/css/messenger.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/messenger/1.5.0/css/messenger-theme-flat.min.css">
  <!-- Style For the initial loader -->
  <style>
  @keyframes lds-rolling {
        0% {
          -webkit-transform: translate(-50%, -50%) rotate(0deg);
          transform: translate(-50%, -50%) rotate(0deg);
        }
        100% {
          -webkit-transform: translate(-50%, -50%) rotate(360deg);
          transform: translate(-50%, -50%) rotate(360deg);
        }
      }
      @-webkit-keyframes lds-rolling {
        0% {
          -webkit-transform: translate(-50%, -50%) rotate(0deg);
          transform: translate(-50%, -50%) rotate(0deg);
        }
        100% {
          -webkit-transform: translate(-50%, -50%) rotate(360deg);
          transform: translate(-50%, -50%) rotate(360deg);
        }
      }
      .lds-rolling {
        position: relative;
      }
      .lds-rolling div,
      .lds-rolling div:after {
        width: 25%;
        height: 25%;
        border: 5px solid #2196f3;
        border-top-color: transparent;
        border-radius: 50%;
      }
      .lds-rolling div {
        -webkit-animation: lds-rolling 1s linear infinite;
        animation: lds-rolling 1s linear infinite;
        top: 100px;
        left: 100px;
      }
      .lds-rolling div:after {
        -webkit-transform: rotate(90deg);
        transform: rotate(90deg);
      }
      .lds-rolling {
        width: 200px !important;
        height: 200px !important;
        -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
        transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
      }
      .loader{
        position: absolute;
        width:25%;
        height:25%;
        top:47%;
        left:50%;
      }
  </style>
</head>

<body>
  <div id="initialLoader" class="loader">
    <div class="lds-css ng-scope"><div style="width:100%;height:100%" class="lds-rolling"><div></div></div>
  </div>
</div>
  <div id="app">
  </div>
  </div>
  <script type="text/javascript" src="https://js.stripe.com/v2/"></script>
  <script>
	var CONFIG = <%- config %>;
	for (const key in CONFIG) {
		if (CONFIG.hasOwnProperty(key)) {
			window[key] = CONFIG[key];
		}
	}
	Stripe.setPublishableKey(STRIPE_KEY);
  </script>
  <!-- start Mixpanel -->
  <script type="text/javascript" async>
    (function (f, b) {
      if (!b.__SV) {
        var a, e, i, g; window.mixpanel = b; b._i = []; b.init = function (a, e, d) {
          function f(b, h) { var a = h.split("."); 2 == a.length && (b = b[a[0]], h = a[1]); b[h] = function () { b.push([h].concat(Array.prototype.slice.call(arguments, 0))) } } var c = b; "undefined" !== typeof d ? c = b[d] = [] : d = "mixpanel"; c.people = c.people || []; c.toString = function (b) { var a = "mixpanel"; "mixpanel" !== d && (a += "." + d); b || (a += " (stub)"); return a }; c.people.toString = function () { return c.toString(1) + ".people (stub)" }; i = "disable track track_pageview track_links track_forms register register_once alias unregister identify name_tag set_config people.set people.set_once people.increment people.append people.union people.track_charge people.clear_charges people.delete_user".split(" ");
          for (g = 0; g < i.length; g++)f(c, i[g]); b._i.push([a, e, d])
        }; b.__SV = 1.2; a = f.createElement("script"); a.type = "text/javascript"; a.async = !0; a.src = "undefined" !== typeof MIXPANEL_CUSTOM_LIB_URL ? MIXPANEL_CUSTOM_LIB_URL : "//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js"; e = f.getElementsByTagName("script")[0]; e.parentNode.insertBefore(a, e)
      }
    })(document, window.mixpanel || []);
    mixpanel.init("be05a4d590e8bc9d5f2cd6b3b3ffb78b");
  </script>
  <!-- end Mixpanel -->
  <script type="text/javascript" src="http://cdn.jsdelivr.net/gh/EzyBackend/ezybackend/sdk/dist/ezybackend.min.js"></script>
  <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
  <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2014-11-29/FileSaver.min.js"></script>
  <script src="https://code.jquery.com/jquery-3.1.1.min.js" integrity="sha256-hVVnYaiADRTO2PzUGmuLJr8BLUSjGIZsDYGmIJLv2b8="
    crossorigin="anonymous">

    </script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/messenger/1.5.0/js/messenger.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/messenger/1.5.0/js/messenger-theme-flat.min.js"></script>
  <script>
    Messenger.options = {
      extraClasses: 'messenger-fixed messenger-on-top messenger-on-right notificationoffset',
      theme: 'flat'
    }
  </script>
  <!-- Begin Inspectlet Embed Code -->
  <script type="text/javascript" id="inspectletjs" async>
    window.__insp = window.__insp || [];
    __insp.push(['wid', 328563898]);
    (function () {
      function ldinsp() { if (typeof window.__inspld != "undefined") return; window.__inspld = 1; var insp = document.createElement('script'); insp.type = 'text/javascript'; insp.async = true; insp.id = "inspsync"; insp.src = ('https:' == document.location.protocol ? 'https' : 'http') + '://cdn.inspectlet.com/inspectlet.js'; var x = document.getElementsByTagName('script')[0]; x.parentNode.insertBefore(insp, x); };
      setTimeout(ldinsp, 500); document.readyState != "complete" ? (window.attachEvent ? window.attachEvent('onload', ldinsp) : window.addEventListener('load', ldinsp, false)) : ldinsp();
    })();

  </script>
    <script type="text/javascript" src="public/client.min.js">
    </script>
  <!-- End Inspectlet Embed Code -->
</body>

</html>