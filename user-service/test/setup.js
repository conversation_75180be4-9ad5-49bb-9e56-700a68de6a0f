/*
#     EzyBackend User Service - Test Setup
#     (c) 2014 HackerBay, Inc. 
#     EzyBackend may be freely distributed under the Apache 2 License
#     
#     Modern test setup replacing grunt configuration
*/

const chai = require('chai');
const chaiHttp = require('chai-http');
const supertest = require('supertest');
const express = require('express');

// Setup chai
chai.use(chaiHttp);

// Global test variables (replacing grunt setup)
global.mocha = require('mocha');
global.chai = chai;
global.expect = chai.expect;
global.chaiHttp = chaiHttp;
global.supertest = supertest;
global.fs = require('fs');

// Test server URL
global.URL = process.env.TEST_URL || 'http://localhost:3000';

// Initialize test app
try {
  const app = require('../app.js')(express(), () => {});
  global.app = app;
  global.request = supertest(app);
} catch (error) {
  console.warn('⚠️  Could not initialize test app:', error.message);
  console.log('📝 Tests may need to start the server manually');
}

// Test utilities (replacing grunt inline functions)
global.util = {
  makeString(length = 5) {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    
    for (let i = 0; i < length; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    
    return text;
  },
  
  makeEmail() {
    return `${this.makeString()}@sample.com`;
  },
  
  generateRandomString(length = 8) {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    
    for (let i = 0; i < length; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    
    return text;
  },
  
  generateRandomNumber(min = 1, max = 1000) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  },
  
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
};

// Test configuration
const testConfig = {
  timeout: 30000, // 30 seconds default timeout
  retries: 2,     // Retry failed tests twice
  reporter: 'spec'
};

// Apply test configuration
if (typeof describe !== 'undefined') {
  // Set default timeout for all tests
  this.timeout(testConfig.timeout);
}

// Environment setup
process.env.NODE_ENV = process.env.NODE_ENV || 'test';

// Suppress console output during tests unless DEBUG is set
if (!process.env.DEBUG) {
  const originalConsole = console.log;
  console.log = (...args) => {
    if (args[0] && args[0].includes && (
      args[0].includes('Test') || 
      args[0].includes('✅') || 
      args[0].includes('❌') ||
      args[0].includes('⚠️')
    )) {
      originalConsole.apply(console, args);
    }
  };
}

console.log('🧪 EzyBackend User Service test setup initialized');
console.log(`📍 Test URL: ${global.URL}`);
console.log(`🌍 Environment: ${process.env.NODE_ENV}`);

module.exports = {
  testConfig,
  util: global.util
};
