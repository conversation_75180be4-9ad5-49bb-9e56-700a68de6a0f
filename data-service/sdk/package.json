{"name": "ezybackend", "version": "6.6.6", "description": "Database Service that does Storage, Search, Real-time and a whole lot more.", "main": "./dist/ezybackend.js", "types": "./dist/ezybackend.d.ts", "keywords": ["database as a service", "dbaas", "no-sql", "database", "backend", "baas", "backend-as-a-service"], "scripts": {"2npm": "publish"}, "author": "<EMAIL>", "license": "MIT", "dependencies": {"axios": "^0.14.0", "bluebird": "^3.4.6", "deep-equal": "^1.0.1", "form-data": "^2.1.2", "jsdom": "^9.10.0", "localStorage": "^1.0.3", "localforage": "^1.5.0", "socket.io-client": "^1.7.1", "phantomjs": "^2.1.7"}, "devDependencies": {"babel-core": "^6.17.0", "babel-loader": "^6.2.5", "babel-preset-es2015": "^6.16.0", "babel-preset-es2015-without-strict": "0.0.4", "chai": "^3.5.0", "chance": "^1.0.1", "grunt": "~0.4.5", "grunt-bump": "^0.3.1", "grunt-bumpup": "^0.6.3", "grunt-contrib-concat": "^0.5.1", "grunt-contrib-uglify": "^0.9.1", "grunt-contrib-watch": "^0.6.1", "grunt-release": "^0.13.0", "json-loader": "^0.5.4", "mocha": "^2.4.5", "phantomjs-prebuilt": "*", "mocha-phantomjs": "^4.1.0", "publish": "^0.6.0", "webpack": "~1.13.1", "webpack-dev-server": "~1.14.1"}}