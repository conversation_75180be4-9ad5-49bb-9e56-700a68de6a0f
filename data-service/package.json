{"name": "ezybackend", "version": "2.0.0", "main": "server.js", "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "repository": {"type": "git", "url": "https://github.com/Ji<PERSON>erkumar2030/ezybackend.git"}, "license": "Apache-2.0", "type": "module", "dependencies": {"app-root-path": "^3.1.0", "axios": "^1.10.0", "axios-retry": "^4.5.0", "bluebird": "^3.7.2", "body-parser": "^2.2.0", "busboy": "^1.6.0", "connect-redis": "^9.0.0", "cookie-parser": "^1.4.7", "cookies": "^0.9.1", "cors": "^2.8.5", "cron": "^4.3.1", "csv2json": "^2.0.2", "deep-equal": "^2.2.3", "ejs": "^3.1.10", "express": "^5.1.0", "express-mung": "^0.5.1", "express-session": "^1.18.1", "express-winston": "^4.2.0", "fb": "^2.0.0", "form-data": "^4.0.3", "github": "^14.0.0", "googleapis": "^150.0.1", "gridfs-stream": "^1.1.1", "icg-json-to-xlsx": "^0.2.5", "ioredis": "^5.6.1", "jimp": "^1.6.0", "jsdom": "^26.1.0", "json2csv": "^5.0.7", "json2xlsx": "^0.1.6", "localforage": "^1.10.0", "mandrill-api": "^1.0.45", "mongo-adapter": "^1.0.8", "mongodb": "^6.17.0", "node-fetch": "^3.3.2", "node-linkedin": "^0.5.6", "node-twitter-api": "^1.8.0", "node-xlsx": "^0.24.0", "nodemailer": "^7.0.5", "nodemailer-mailgun-transport": "^2.1.5", "oauth": "^0.10.2", "octonode": "^0.10.2", "q": "^1.5.1", "should": "^13.2.3", "slack-node": "^0.2.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "socket.io-redis": "^6.1.1", "underscore": "^1.13.7", "url-parse": "^1.5.10", "uuid": "^11.1.0", "web-push": "^3.6.7", "winston": "^3.17.0", "winston-loggly-transport": "^1.0.3"}, "devDependencies": {"@babel/core": "^7.25.0", "@babel/preset-env": "^7.25.0", "babel-loader": "^9.2.1", "chai": "^5.2.0", "chance": "^1.1.13", "esbuild": "^0.24.2", "eslint": "^9.30.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.32.0", "husky": "^9.1.7", "mocha": "^11.7.1", "prettier": "^3.4.2", "terser-webpack-plugin": "^5.3.11", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}, "scripts": {"start": "node server.js", "dev": "NODE_ENV=development node server.js", "test": "npm run lint && npm run test-unit", "test-unit": "mocha --reporter spec test/test.js --exit", "test-concat": "npm run concat-tests && npm run test-unit", "lint": "eslint . --ext .js --fix", "lint-check": "eslint . --ext .js", "format": "prettier --write \"**/*.{js,json,md}\"", "format-check": "prettier --check \"**/*.{js,json,md}\"", "build-sdk": "webpack --mode production", "build-sdk-dev": "webpack --mode development", "build-sdk-fast": "node esbuild.config.js", "watch-sdk": "webpack --mode development --watch", "watch-sdk-fast": "node esbuild.config.js watch", "concat-tests": "node scripts/concat-tests.js", "version-bump": "npm version patch", "prepare": "husky install || true"}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "20"}}]]}}